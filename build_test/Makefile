# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.31

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/local/bin/cmake

# The command to remove a file.
RM = /usr/local/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/work/common/optical_power_meter_driver

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/work/common/optical_power_meter_driver/build_test

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target package
package: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Run CPack packaging tool..."
	/usr/local/bin/cpack --config ./CPackConfig.cmake
.PHONY : package

# Special rule for the target package
package/fast: package
.PHONY : package/fast

# Special rule for the target package_source
package_source:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Run CPack packaging tool for source..."
	/usr/local/bin/cpack --config ./CPackSourceConfig.cmake /home/<USER>/work/common/optical_power_meter_driver/build_test/CPackSourceConfig.cmake
.PHONY : package_source

# Special rule for the target package_source
package_source/fast: package_source
.PHONY : package_source/fast

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake cache editor..."
	/usr/local/bin/ccmake -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake to regenerate build system..."
	/usr/local/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components
.PHONY : list_install_components/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Install the project..."
	/usr/local/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Install the project..."
	/usr/local/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing only the local directory..."
	/usr/local/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing only the local directory..."
	/usr/local/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing the project stripped..."
	/usr/local/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing the project stripped..."
	/usr/local/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/work/common/optical_power_meter_driver/build_test/CMakeFiles /home/<USER>/work/common/optical_power_meter_driver/build_test//CMakeFiles/progress.marks
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/work/common/optical_power_meter_driver/build_test/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named optical_power_meter_driver

# Build rule for target.
optical_power_meter_driver: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 optical_power_meter_driver
.PHONY : optical_power_meter_driver

# fast build rule for target.
optical_power_meter_driver/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/optical_power_meter_driver.dir/build.make CMakeFiles/optical_power_meter_driver.dir/build
.PHONY : optical_power_meter_driver/fast

#=============================================================================
# Target rules for targets named qt_power_meter_example

# Build rule for target.
qt_power_meter_example: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 qt_power_meter_example
.PHONY : qt_power_meter_example

# fast build rule for target.
qt_power_meter_example/fast:
	$(MAKE) $(MAKESILENT) -f example/qt_example/CMakeFiles/qt_power_meter_example.dir/build.make example/qt_example/CMakeFiles/qt_power_meter_example.dir/build
.PHONY : qt_power_meter_example/fast

#=============================================================================
# Target rules for targets named qt_power_meter_example_autogen

# Build rule for target.
qt_power_meter_example_autogen: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 qt_power_meter_example_autogen
.PHONY : qt_power_meter_example_autogen

# fast build rule for target.
qt_power_meter_example_autogen/fast:
	$(MAKE) $(MAKESILENT) -f example/qt_example/CMakeFiles/qt_power_meter_example_autogen.dir/build.make example/qt_example/CMakeFiles/qt_power_meter_example_autogen.dir/build
.PHONY : qt_power_meter_example_autogen/fast

src/linux/power_meter_pm101x.o: src/linux/power_meter_pm101x.cpp.o
.PHONY : src/linux/power_meter_pm101x.o

# target to build an object file
src/linux/power_meter_pm101x.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/optical_power_meter_driver.dir/build.make CMakeFiles/optical_power_meter_driver.dir/src/linux/power_meter_pm101x.cpp.o
.PHONY : src/linux/power_meter_pm101x.cpp.o

src/linux/power_meter_pm101x.i: src/linux/power_meter_pm101x.cpp.i
.PHONY : src/linux/power_meter_pm101x.i

# target to preprocess a source file
src/linux/power_meter_pm101x.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/optical_power_meter_driver.dir/build.make CMakeFiles/optical_power_meter_driver.dir/src/linux/power_meter_pm101x.cpp.i
.PHONY : src/linux/power_meter_pm101x.cpp.i

src/linux/power_meter_pm101x.s: src/linux/power_meter_pm101x.cpp.s
.PHONY : src/linux/power_meter_pm101x.s

# target to generate assembly for a file
src/linux/power_meter_pm101x.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/optical_power_meter_driver.dir/build.make CMakeFiles/optical_power_meter_driver.dir/src/linux/power_meter_pm101x.cpp.s
.PHONY : src/linux/power_meter_pm101x.cpp.s

src/power_meter_base.o: src/power_meter_base.cpp.o
.PHONY : src/power_meter_base.o

# target to build an object file
src/power_meter_base.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/optical_power_meter_driver.dir/build.make CMakeFiles/optical_power_meter_driver.dir/src/power_meter_base.cpp.o
.PHONY : src/power_meter_base.cpp.o

src/power_meter_base.i: src/power_meter_base.cpp.i
.PHONY : src/power_meter_base.i

# target to preprocess a source file
src/power_meter_base.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/optical_power_meter_driver.dir/build.make CMakeFiles/optical_power_meter_driver.dir/src/power_meter_base.cpp.i
.PHONY : src/power_meter_base.cpp.i

src/power_meter_base.s: src/power_meter_base.cpp.s
.PHONY : src/power_meter_base.s

# target to generate assembly for a file
src/power_meter_base.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/optical_power_meter_driver.dir/build.make CMakeFiles/optical_power_meter_driver.dir/src/power_meter_base.cpp.s
.PHONY : src/power_meter_base.cpp.s

src/power_meter_pm100x.o: src/power_meter_pm100x.cpp.o
.PHONY : src/power_meter_pm100x.o

# target to build an object file
src/power_meter_pm100x.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/optical_power_meter_driver.dir/build.make CMakeFiles/optical_power_meter_driver.dir/src/power_meter_pm100x.cpp.o
.PHONY : src/power_meter_pm100x.cpp.o

src/power_meter_pm100x.i: src/power_meter_pm100x.cpp.i
.PHONY : src/power_meter_pm100x.i

# target to preprocess a source file
src/power_meter_pm100x.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/optical_power_meter_driver.dir/build.make CMakeFiles/optical_power_meter_driver.dir/src/power_meter_pm100x.cpp.i
.PHONY : src/power_meter_pm100x.cpp.i

src/power_meter_pm100x.s: src/power_meter_pm100x.cpp.s
.PHONY : src/power_meter_pm100x.s

# target to generate assembly for a file
src/power_meter_pm100x.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/optical_power_meter_driver.dir/build.make CMakeFiles/optical_power_meter_driver.dir/src/power_meter_pm100x.cpp.s
.PHONY : src/power_meter_pm100x.cpp.s

src/thorlabs_optical_power_meter.o: src/thorlabs_optical_power_meter.cpp.o
.PHONY : src/thorlabs_optical_power_meter.o

# target to build an object file
src/thorlabs_optical_power_meter.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/optical_power_meter_driver.dir/build.make CMakeFiles/optical_power_meter_driver.dir/src/thorlabs_optical_power_meter.cpp.o
.PHONY : src/thorlabs_optical_power_meter.cpp.o

src/thorlabs_optical_power_meter.i: src/thorlabs_optical_power_meter.cpp.i
.PHONY : src/thorlabs_optical_power_meter.i

# target to preprocess a source file
src/thorlabs_optical_power_meter.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/optical_power_meter_driver.dir/build.make CMakeFiles/optical_power_meter_driver.dir/src/thorlabs_optical_power_meter.cpp.i
.PHONY : src/thorlabs_optical_power_meter.cpp.i

src/thorlabs_optical_power_meter.s: src/thorlabs_optical_power_meter.cpp.s
.PHONY : src/thorlabs_optical_power_meter.s

# target to generate assembly for a file
src/thorlabs_optical_power_meter.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/optical_power_meter_driver.dir/build.make CMakeFiles/optical_power_meter_driver.dir/src/thorlabs_optical_power_meter.cpp.s
.PHONY : src/thorlabs_optical_power_meter.cpp.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... install"
	@echo "... install/local"
	@echo "... install/strip"
	@echo "... list_install_components"
	@echo "... package"
	@echo "... package_source"
	@echo "... rebuild_cache"
	@echo "... qt_power_meter_example_autogen"
	@echo "... optical_power_meter_driver"
	@echo "... qt_power_meter_example"
	@echo "... src/linux/power_meter_pm101x.o"
	@echo "... src/linux/power_meter_pm101x.i"
	@echo "... src/linux/power_meter_pm101x.s"
	@echo "... src/power_meter_base.o"
	@echo "... src/power_meter_base.i"
	@echo "... src/power_meter_base.s"
	@echo "... src/power_meter_pm100x.o"
	@echo "... src/power_meter_pm100x.i"
	@echo "... src/power_meter_pm100x.s"
	@echo "... src/thorlabs_optical_power_meter.o"
	@echo "... src/thorlabs_optical_power_meter.i"
	@echo "... src/thorlabs_optical_power_meter.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

