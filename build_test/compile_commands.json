[{"directory": "/home/<USER>/work/common/optical_power_meter_driver/build_test", "command": "/usr/bin/c++ -D__TL_COMPILE_64BIT__ -I/home/<USER>/work/common/optical_power_meter_driver/src/linux -I/home/<USER>/work/common/optical_power_meter_driver/include -I/home/<USER>/work/common/optical_power_meter_driver/src -isystem /home/<USER>/work/common/optical_power_meter_driver/build/_deps/rsfsc_log-src/focal/include -isystem /home/<USER>/work/common/optical_power_meter_driver/lib/opm/focal/include -O2 -g -DNDEBUG -std=c++17 -fPIC -fPIC -Wall -O3 -g1 -o CMakeFiles/optical_power_meter_driver.dir/src/power_meter_base.cpp.o -c /home/<USER>/work/common/optical_power_meter_driver/src/power_meter_base.cpp", "file": "/home/<USER>/work/common/optical_power_meter_driver/src/power_meter_base.cpp", "output": "CMakeFiles/optical_power_meter_driver.dir/src/power_meter_base.cpp.o"}, {"directory": "/home/<USER>/work/common/optical_power_meter_driver/build_test", "command": "/usr/bin/c++ -D__TL_COMPILE_64BIT__ -I/home/<USER>/work/common/optical_power_meter_driver/src/linux -I/home/<USER>/work/common/optical_power_meter_driver/include -I/home/<USER>/work/common/optical_power_meter_driver/src -isystem /home/<USER>/work/common/optical_power_meter_driver/build/_deps/rsfsc_log-src/focal/include -isystem /home/<USER>/work/common/optical_power_meter_driver/lib/opm/focal/include -O2 -g -DNDEBUG -std=c++17 -fPIC -fPIC -Wall -O3 -g1 -o CMakeFiles/optical_power_meter_driver.dir/src/power_meter_pm100x.cpp.o -c /home/<USER>/work/common/optical_power_meter_driver/src/power_meter_pm100x.cpp", "file": "/home/<USER>/work/common/optical_power_meter_driver/src/power_meter_pm100x.cpp", "output": "CMakeFiles/optical_power_meter_driver.dir/src/power_meter_pm100x.cpp.o"}, {"directory": "/home/<USER>/work/common/optical_power_meter_driver/build_test", "command": "/usr/bin/c++ -D__TL_COMPILE_64BIT__ -I/home/<USER>/work/common/optical_power_meter_driver/src/linux -I/home/<USER>/work/common/optical_power_meter_driver/include -I/home/<USER>/work/common/optical_power_meter_driver/src -isystem /home/<USER>/work/common/optical_power_meter_driver/build/_deps/rsfsc_log-src/focal/include -isystem /home/<USER>/work/common/optical_power_meter_driver/lib/opm/focal/include -O2 -g -DNDEBUG -std=c++17 -fPIC -fPIC -Wall -O3 -g1 -o CMakeFiles/optical_power_meter_driver.dir/src/thorlabs_optical_power_meter.cpp.o -c /home/<USER>/work/common/optical_power_meter_driver/src/thorlabs_optical_power_meter.cpp", "file": "/home/<USER>/work/common/optical_power_meter_driver/src/thorlabs_optical_power_meter.cpp", "output": "CMakeFiles/optical_power_meter_driver.dir/src/thorlabs_optical_power_meter.cpp.o"}, {"directory": "/home/<USER>/work/common/optical_power_meter_driver/build_test", "command": "/usr/bin/c++ -D__TL_COMPILE_64BIT__ -I/home/<USER>/work/common/optical_power_meter_driver/src/linux -I/home/<USER>/work/common/optical_power_meter_driver/include -I/home/<USER>/work/common/optical_power_meter_driver/src -isystem /home/<USER>/work/common/optical_power_meter_driver/build/_deps/rsfsc_log-src/focal/include -isystem /home/<USER>/work/common/optical_power_meter_driver/lib/opm/focal/include -O2 -g -DNDEBUG -std=c++17 -fPIC -fPIC -Wall -O3 -g1 -o CMakeFiles/optical_power_meter_driver.dir/src/linux/power_meter_pm101x.cpp.o -c /home/<USER>/work/common/optical_power_meter_driver/src/linux/power_meter_pm101x.cpp", "file": "/home/<USER>/work/common/optical_power_meter_driver/src/linux/power_meter_pm101x.cpp", "output": "CMakeFiles/optical_power_meter_driver.dir/src/linux/power_meter_pm101x.cpp.o"}, {"directory": "/home/<USER>/work/common/optical_power_meter_driver/build_test/example/qt_example", "command": "/usr/bin/c++ -DQT_CORE_LIB -DQT_GUI_LIB -DQT_NO_DEBUG -DQT_NO_KEYWORDS -DQT_WIDGETS_LIB -D__TL_COMPILE_64BIT__ -I/home/<USER>/work/common/optical_power_meter_driver/build_test/example/qt_example/qt_power_meter_example_autogen/include -I/home/<USER>/work/common/optical_power_meter_driver/example/qt_example/include -I/home/<USER>/work/common/optical_power_meter_driver/include -isystem /home/<USER>/work/common/optical_power_meter_driver/build/_deps/rsfsc_log-src/focal/include -isystem /usr/include/x86_64-linux-gnu/qt5 -isystem /usr/include/x86_64-linux-gnu/qt5/QtWidgets -isystem /usr/include/x86_64-linux-gnu/qt5/QtGui -isystem /usr/include/x86_64-linux-gnu/qt5/QtCore -isystem /usr/lib/x86_64-linux-gnu/qt5/mkspecs/linux-g++ -isystem /home/<USER>/work/common/optical_power_meter_driver/lib/opm/focal/include -O2 -g -DNDEBUG -std=c++17 -fPIE -fPIC -o CMakeFiles/qt_power_meter_example.dir/qt_power_meter_example_autogen/mocs_compilation.cpp.o -c /home/<USER>/work/common/optical_power_meter_driver/build_test/example/qt_example/qt_power_meter_example_autogen/mocs_compilation.cpp", "file": "/home/<USER>/work/common/optical_power_meter_driver/build_test/example/qt_example/qt_power_meter_example_autogen/mocs_compilation.cpp", "output": "example/qt_example/CMakeFiles/qt_power_meter_example.dir/qt_power_meter_example_autogen/mocs_compilation.cpp.o"}, {"directory": "/home/<USER>/work/common/optical_power_meter_driver/build_test/example/qt_example", "command": "/usr/bin/c++ -DQT_CORE_LIB -DQT_GUI_LIB -DQT_NO_DEBUG -DQT_NO_KEYWORDS -DQT_WIDGETS_LIB -D__TL_COMPILE_64BIT__ -I/home/<USER>/work/common/optical_power_meter_driver/build_test/example/qt_example/qt_power_meter_example_autogen/include -I/home/<USER>/work/common/optical_power_meter_driver/example/qt_example/include -I/home/<USER>/work/common/optical_power_meter_driver/include -isystem /home/<USER>/work/common/optical_power_meter_driver/build/_deps/rsfsc_log-src/focal/include -isystem /usr/include/x86_64-linux-gnu/qt5 -isystem /usr/include/x86_64-linux-gnu/qt5/QtWidgets -isystem /usr/include/x86_64-linux-gnu/qt5/QtGui -isystem /usr/include/x86_64-linux-gnu/qt5/QtCore -isystem /usr/lib/x86_64-linux-gnu/qt5/mkspecs/linux-g++ -isystem /home/<USER>/work/common/optical_power_meter_driver/lib/opm/focal/include -O2 -g -DNDEBUG -std=c++17 -fPIE -fPIC -o CMakeFiles/qt_power_meter_example.dir/qt_example.cpp.o -c /home/<USER>/work/common/optical_power_meter_driver/example/qt_example/qt_example.cpp", "file": "/home/<USER>/work/common/optical_power_meter_driver/example/qt_example/qt_example.cpp", "output": "example/qt_example/CMakeFiles/qt_power_meter_example.dir/qt_example.cpp.o"}, {"directory": "/home/<USER>/work/common/optical_power_meter_driver/build_test/example/qt_example", "command": "/usr/bin/c++ -DQT_CORE_LIB -DQT_GUI_LIB -DQT_NO_DEBUG -DQT_NO_KEYWORDS -DQT_WIDGETS_LIB -D__TL_COMPILE_64BIT__ -I/home/<USER>/work/common/optical_power_meter_driver/build_test/example/qt_example/qt_power_meter_example_autogen/include -I/home/<USER>/work/common/optical_power_meter_driver/example/qt_example/include -I/home/<USER>/work/common/optical_power_meter_driver/include -isystem /home/<USER>/work/common/optical_power_meter_driver/build/_deps/rsfsc_log-src/focal/include -isystem /usr/include/x86_64-linux-gnu/qt5 -isystem /usr/include/x86_64-linux-gnu/qt5/QtWidgets -isystem /usr/include/x86_64-linux-gnu/qt5/QtGui -isystem /usr/include/x86_64-linux-gnu/qt5/QtCore -isystem /usr/lib/x86_64-linux-gnu/qt5/mkspecs/linux-g++ -isystem /home/<USER>/work/common/optical_power_meter_driver/lib/opm/focal/include -O2 -g -DNDEBUG -std=c++17 -fPIE -fPIC -o CMakeFiles/qt_power_meter_example.dir/main_window.cpp.o -c /home/<USER>/work/common/optical_power_meter_driver/example/qt_example/main_window.cpp", "file": "/home/<USER>/work/common/optical_power_meter_driver/example/qt_example/main_window.cpp", "output": "example/qt_example/CMakeFiles/qt_power_meter_example.dir/main_window.cpp.o"}]