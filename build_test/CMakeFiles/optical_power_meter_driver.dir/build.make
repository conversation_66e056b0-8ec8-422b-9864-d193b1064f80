# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.31

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/local/bin/cmake

# The command to remove a file.
RM = /usr/local/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/work/common/optical_power_meter_driver

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/work/common/optical_power_meter_driver/build_test

# Include any dependencies generated for this target.
include CMakeFiles/optical_power_meter_driver.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include CMakeFiles/optical_power_meter_driver.dir/compiler_depend.make

# Include the progress variables for this target.
include CMakeFiles/optical_power_meter_driver.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/optical_power_meter_driver.dir/flags.make

CMakeFiles/optical_power_meter_driver.dir/codegen:
.PHONY : CMakeFiles/optical_power_meter_driver.dir/codegen

CMakeFiles/optical_power_meter_driver.dir/src/power_meter_base.cpp.o: CMakeFiles/optical_power_meter_driver.dir/flags.make
CMakeFiles/optical_power_meter_driver.dir/src/power_meter_base.cpp.o: /home/<USER>/work/common/optical_power_meter_driver/src/power_meter_base.cpp
CMakeFiles/optical_power_meter_driver.dir/src/power_meter_base.cpp.o: CMakeFiles/optical_power_meter_driver.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/work/common/optical_power_meter_driver/build_test/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/optical_power_meter_driver.dir/src/power_meter_base.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/optical_power_meter_driver.dir/src/power_meter_base.cpp.o -MF CMakeFiles/optical_power_meter_driver.dir/src/power_meter_base.cpp.o.d -o CMakeFiles/optical_power_meter_driver.dir/src/power_meter_base.cpp.o -c /home/<USER>/work/common/optical_power_meter_driver/src/power_meter_base.cpp

CMakeFiles/optical_power_meter_driver.dir/src/power_meter_base.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/optical_power_meter_driver.dir/src/power_meter_base.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/work/common/optical_power_meter_driver/src/power_meter_base.cpp > CMakeFiles/optical_power_meter_driver.dir/src/power_meter_base.cpp.i

CMakeFiles/optical_power_meter_driver.dir/src/power_meter_base.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/optical_power_meter_driver.dir/src/power_meter_base.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/work/common/optical_power_meter_driver/src/power_meter_base.cpp -o CMakeFiles/optical_power_meter_driver.dir/src/power_meter_base.cpp.s

CMakeFiles/optical_power_meter_driver.dir/src/power_meter_pm100x.cpp.o: CMakeFiles/optical_power_meter_driver.dir/flags.make
CMakeFiles/optical_power_meter_driver.dir/src/power_meter_pm100x.cpp.o: /home/<USER>/work/common/optical_power_meter_driver/src/power_meter_pm100x.cpp
CMakeFiles/optical_power_meter_driver.dir/src/power_meter_pm100x.cpp.o: CMakeFiles/optical_power_meter_driver.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/work/common/optical_power_meter_driver/build_test/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object CMakeFiles/optical_power_meter_driver.dir/src/power_meter_pm100x.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/optical_power_meter_driver.dir/src/power_meter_pm100x.cpp.o -MF CMakeFiles/optical_power_meter_driver.dir/src/power_meter_pm100x.cpp.o.d -o CMakeFiles/optical_power_meter_driver.dir/src/power_meter_pm100x.cpp.o -c /home/<USER>/work/common/optical_power_meter_driver/src/power_meter_pm100x.cpp

CMakeFiles/optical_power_meter_driver.dir/src/power_meter_pm100x.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/optical_power_meter_driver.dir/src/power_meter_pm100x.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/work/common/optical_power_meter_driver/src/power_meter_pm100x.cpp > CMakeFiles/optical_power_meter_driver.dir/src/power_meter_pm100x.cpp.i

CMakeFiles/optical_power_meter_driver.dir/src/power_meter_pm100x.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/optical_power_meter_driver.dir/src/power_meter_pm100x.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/work/common/optical_power_meter_driver/src/power_meter_pm100x.cpp -o CMakeFiles/optical_power_meter_driver.dir/src/power_meter_pm100x.cpp.s

CMakeFiles/optical_power_meter_driver.dir/src/thorlabs_optical_power_meter.cpp.o: CMakeFiles/optical_power_meter_driver.dir/flags.make
CMakeFiles/optical_power_meter_driver.dir/src/thorlabs_optical_power_meter.cpp.o: /home/<USER>/work/common/optical_power_meter_driver/src/thorlabs_optical_power_meter.cpp
CMakeFiles/optical_power_meter_driver.dir/src/thorlabs_optical_power_meter.cpp.o: CMakeFiles/optical_power_meter_driver.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/work/common/optical_power_meter_driver/build_test/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object CMakeFiles/optical_power_meter_driver.dir/src/thorlabs_optical_power_meter.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/optical_power_meter_driver.dir/src/thorlabs_optical_power_meter.cpp.o -MF CMakeFiles/optical_power_meter_driver.dir/src/thorlabs_optical_power_meter.cpp.o.d -o CMakeFiles/optical_power_meter_driver.dir/src/thorlabs_optical_power_meter.cpp.o -c /home/<USER>/work/common/optical_power_meter_driver/src/thorlabs_optical_power_meter.cpp

CMakeFiles/optical_power_meter_driver.dir/src/thorlabs_optical_power_meter.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/optical_power_meter_driver.dir/src/thorlabs_optical_power_meter.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/work/common/optical_power_meter_driver/src/thorlabs_optical_power_meter.cpp > CMakeFiles/optical_power_meter_driver.dir/src/thorlabs_optical_power_meter.cpp.i

CMakeFiles/optical_power_meter_driver.dir/src/thorlabs_optical_power_meter.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/optical_power_meter_driver.dir/src/thorlabs_optical_power_meter.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/work/common/optical_power_meter_driver/src/thorlabs_optical_power_meter.cpp -o CMakeFiles/optical_power_meter_driver.dir/src/thorlabs_optical_power_meter.cpp.s

CMakeFiles/optical_power_meter_driver.dir/src/linux/power_meter_pm101x.cpp.o: CMakeFiles/optical_power_meter_driver.dir/flags.make
CMakeFiles/optical_power_meter_driver.dir/src/linux/power_meter_pm101x.cpp.o: /home/<USER>/work/common/optical_power_meter_driver/src/linux/power_meter_pm101x.cpp
CMakeFiles/optical_power_meter_driver.dir/src/linux/power_meter_pm101x.cpp.o: CMakeFiles/optical_power_meter_driver.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/work/common/optical_power_meter_driver/build_test/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building CXX object CMakeFiles/optical_power_meter_driver.dir/src/linux/power_meter_pm101x.cpp.o"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT CMakeFiles/optical_power_meter_driver.dir/src/linux/power_meter_pm101x.cpp.o -MF CMakeFiles/optical_power_meter_driver.dir/src/linux/power_meter_pm101x.cpp.o.d -o CMakeFiles/optical_power_meter_driver.dir/src/linux/power_meter_pm101x.cpp.o -c /home/<USER>/work/common/optical_power_meter_driver/src/linux/power_meter_pm101x.cpp

CMakeFiles/optical_power_meter_driver.dir/src/linux/power_meter_pm101x.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/optical_power_meter_driver.dir/src/linux/power_meter_pm101x.cpp.i"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/work/common/optical_power_meter_driver/src/linux/power_meter_pm101x.cpp > CMakeFiles/optical_power_meter_driver.dir/src/linux/power_meter_pm101x.cpp.i

CMakeFiles/optical_power_meter_driver.dir/src/linux/power_meter_pm101x.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/optical_power_meter_driver.dir/src/linux/power_meter_pm101x.cpp.s"
	/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/work/common/optical_power_meter_driver/src/linux/power_meter_pm101x.cpp -o CMakeFiles/optical_power_meter_driver.dir/src/linux/power_meter_pm101x.cpp.s

# Object files for target optical_power_meter_driver
optical_power_meter_driver_OBJECTS = \
"CMakeFiles/optical_power_meter_driver.dir/src/power_meter_base.cpp.o" \
"CMakeFiles/optical_power_meter_driver.dir/src/power_meter_pm100x.cpp.o" \
"CMakeFiles/optical_power_meter_driver.dir/src/thorlabs_optical_power_meter.cpp.o" \
"CMakeFiles/optical_power_meter_driver.dir/src/linux/power_meter_pm101x.cpp.o"

# External object files for target optical_power_meter_driver
optical_power_meter_driver_EXTERNAL_OBJECTS =

liboptical_power_meter_driver.a: CMakeFiles/optical_power_meter_driver.dir/src/power_meter_base.cpp.o
liboptical_power_meter_driver.a: CMakeFiles/optical_power_meter_driver.dir/src/power_meter_pm100x.cpp.o
liboptical_power_meter_driver.a: CMakeFiles/optical_power_meter_driver.dir/src/thorlabs_optical_power_meter.cpp.o
liboptical_power_meter_driver.a: CMakeFiles/optical_power_meter_driver.dir/src/linux/power_meter_pm101x.cpp.o
liboptical_power_meter_driver.a: CMakeFiles/optical_power_meter_driver.dir/build.make
liboptical_power_meter_driver.a: CMakeFiles/optical_power_meter_driver.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/home/<USER>/work/common/optical_power_meter_driver/build_test/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Linking CXX static library liboptical_power_meter_driver.a"
	$(CMAKE_COMMAND) -P CMakeFiles/optical_power_meter_driver.dir/cmake_clean_target.cmake
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/optical_power_meter_driver.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/optical_power_meter_driver.dir/build: liboptical_power_meter_driver.a
.PHONY : CMakeFiles/optical_power_meter_driver.dir/build

CMakeFiles/optical_power_meter_driver.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/optical_power_meter_driver.dir/cmake_clean.cmake
.PHONY : CMakeFiles/optical_power_meter_driver.dir/clean

CMakeFiles/optical_power_meter_driver.dir/depend:
	cd /home/<USER>/work/common/optical_power_meter_driver/build_test && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/work/common/optical_power_meter_driver /home/<USER>/work/common/optical_power_meter_driver /home/<USER>/work/common/optical_power_meter_driver/build_test /home/<USER>/work/common/optical_power_meter_driver/build_test /home/<USER>/work/common/optical_power_meter_driver/build_test/CMakeFiles/optical_power_meter_driver.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : CMakeFiles/optical_power_meter_driver.dir/depend

