# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.31

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/local/bin/cmake

# The command to remove a file.
RM = /usr/local/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/work/common/optical_power_meter_driver

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/work/common/optical_power_meter_driver/build_test

#=============================================================================
# Directory level rules for directory /home/<USER>/work/common/optical_power_meter_driver/build/_deps/rsfsc_log-build

# Recursive "all" directory target.
/home/<USER>/work/common/optical_power_meter_driver/build/_deps/rsfsc_log-build/all:
.PHONY : /home/<USER>/work/common/optical_power_meter_driver/build/_deps/rsfsc_log-build/all

# Recursive "codegen" directory target.
/home/<USER>/work/common/optical_power_meter_driver/build/_deps/rsfsc_log-build/codegen:
.PHONY : /home/<USER>/work/common/optical_power_meter_driver/build/_deps/rsfsc_log-build/codegen

# Recursive "preinstall" directory target.
/home/<USER>/work/common/optical_power_meter_driver/build/_deps/rsfsc_log-build/preinstall:
.PHONY : /home/<USER>/work/common/optical_power_meter_driver/build/_deps/rsfsc_log-build/preinstall

# Recursive "clean" directory target.
/home/<USER>/work/common/optical_power_meter_driver/build/_deps/rsfsc_log-build/clean:
.PHONY : /home/<USER>/work/common/optical_power_meter_driver/build/_deps/rsfsc_log-build/clean

#=============================================================================
# Directory level rules for the build root directory

# The main recursive "all" target.
all: CMakeFiles/optical_power_meter_driver.dir/all
all: /home/<USER>/work/common/optical_power_meter_driver/build/_deps/rsfsc_log-build/all
all: example/qt_example/all
.PHONY : all

# The main recursive "codegen" target.
codegen: CMakeFiles/optical_power_meter_driver.dir/codegen
codegen: /home/<USER>/work/common/optical_power_meter_driver/build/_deps/rsfsc_log-build/codegen
codegen: example/qt_example/codegen
.PHONY : codegen

# The main recursive "preinstall" target.
preinstall: /home/<USER>/work/common/optical_power_meter_driver/build/_deps/rsfsc_log-build/preinstall
preinstall: example/qt_example/preinstall
.PHONY : preinstall

# The main recursive "clean" target.
clean: CMakeFiles/optical_power_meter_driver.dir/clean
clean: /home/<USER>/work/common/optical_power_meter_driver/build/_deps/rsfsc_log-build/clean
clean: example/qt_example/clean
.PHONY : clean

#=============================================================================
# Directory level rules for directory example/qt_example

# Recursive "all" directory target.
example/qt_example/all: example/qt_example/CMakeFiles/qt_power_meter_example.dir/all
.PHONY : example/qt_example/all

# Recursive "codegen" directory target.
example/qt_example/codegen: example/qt_example/CMakeFiles/qt_power_meter_example.dir/codegen
.PHONY : example/qt_example/codegen

# Recursive "preinstall" directory target.
example/qt_example/preinstall:
.PHONY : example/qt_example/preinstall

# Recursive "clean" directory target.
example/qt_example/clean: example/qt_example/CMakeFiles/qt_power_meter_example.dir/clean
example/qt_example/clean: example/qt_example/CMakeFiles/qt_power_meter_example_autogen.dir/clean
.PHONY : example/qt_example/clean

#=============================================================================
# Target rules for target CMakeFiles/optical_power_meter_driver.dir

# All Build rule for target.
CMakeFiles/optical_power_meter_driver.dir/all:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/optical_power_meter_driver.dir/build.make CMakeFiles/optical_power_meter_driver.dir/depend
	$(MAKE) $(MAKESILENT) -f CMakeFiles/optical_power_meter_driver.dir/build.make CMakeFiles/optical_power_meter_driver.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/work/common/optical_power_meter_driver/build_test/CMakeFiles --progress-num=1,2,3,4,5 "Built target optical_power_meter_driver"
.PHONY : CMakeFiles/optical_power_meter_driver.dir/all

# Build rule for subdir invocation for target.
CMakeFiles/optical_power_meter_driver.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/work/common/optical_power_meter_driver/build_test/CMakeFiles 5
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 CMakeFiles/optical_power_meter_driver.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/work/common/optical_power_meter_driver/build_test/CMakeFiles 0
.PHONY : CMakeFiles/optical_power_meter_driver.dir/rule

# Convenience name for target.
optical_power_meter_driver: CMakeFiles/optical_power_meter_driver.dir/rule
.PHONY : optical_power_meter_driver

# codegen rule for target.
CMakeFiles/optical_power_meter_driver.dir/codegen:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/optical_power_meter_driver.dir/build.make CMakeFiles/optical_power_meter_driver.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/work/common/optical_power_meter_driver/build_test/CMakeFiles --progress-num=1,2,3,4,5 "Finished codegen for target optical_power_meter_driver"
.PHONY : CMakeFiles/optical_power_meter_driver.dir/codegen

# clean rule for target.
CMakeFiles/optical_power_meter_driver.dir/clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/optical_power_meter_driver.dir/build.make CMakeFiles/optical_power_meter_driver.dir/clean
.PHONY : CMakeFiles/optical_power_meter_driver.dir/clean

#=============================================================================
# Target rules for target example/qt_example/CMakeFiles/qt_power_meter_example.dir

# All Build rule for target.
example/qt_example/CMakeFiles/qt_power_meter_example.dir/all: CMakeFiles/optical_power_meter_driver.dir/all
example/qt_example/CMakeFiles/qt_power_meter_example.dir/all: example/qt_example/CMakeFiles/qt_power_meter_example_autogen.dir/all
	$(MAKE) $(MAKESILENT) -f example/qt_example/CMakeFiles/qt_power_meter_example.dir/build.make example/qt_example/CMakeFiles/qt_power_meter_example.dir/depend
	$(MAKE) $(MAKESILENT) -f example/qt_example/CMakeFiles/qt_power_meter_example.dir/build.make example/qt_example/CMakeFiles/qt_power_meter_example.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/work/common/optical_power_meter_driver/build_test/CMakeFiles --progress-num=6,7,8,9 "Built target qt_power_meter_example"
.PHONY : example/qt_example/CMakeFiles/qt_power_meter_example.dir/all

# Build rule for subdir invocation for target.
example/qt_example/CMakeFiles/qt_power_meter_example.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/work/common/optical_power_meter_driver/build_test/CMakeFiles 10
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 example/qt_example/CMakeFiles/qt_power_meter_example.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/work/common/optical_power_meter_driver/build_test/CMakeFiles 0
.PHONY : example/qt_example/CMakeFiles/qt_power_meter_example.dir/rule

# Convenience name for target.
qt_power_meter_example: example/qt_example/CMakeFiles/qt_power_meter_example.dir/rule
.PHONY : qt_power_meter_example

# codegen rule for target.
example/qt_example/CMakeFiles/qt_power_meter_example.dir/codegen: example/qt_example/CMakeFiles/qt_power_meter_example_autogen.dir/all
	$(MAKE) $(MAKESILENT) -f example/qt_example/CMakeFiles/qt_power_meter_example.dir/build.make example/qt_example/CMakeFiles/qt_power_meter_example.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/work/common/optical_power_meter_driver/build_test/CMakeFiles --progress-num=6,7,8,9 "Finished codegen for target qt_power_meter_example"
.PHONY : example/qt_example/CMakeFiles/qt_power_meter_example.dir/codegen

# clean rule for target.
example/qt_example/CMakeFiles/qt_power_meter_example.dir/clean:
	$(MAKE) $(MAKESILENT) -f example/qt_example/CMakeFiles/qt_power_meter_example.dir/build.make example/qt_example/CMakeFiles/qt_power_meter_example.dir/clean
.PHONY : example/qt_example/CMakeFiles/qt_power_meter_example.dir/clean

#=============================================================================
# Target rules for target example/qt_example/CMakeFiles/qt_power_meter_example_autogen.dir

# All Build rule for target.
example/qt_example/CMakeFiles/qt_power_meter_example_autogen.dir/all: CMakeFiles/optical_power_meter_driver.dir/all
	$(MAKE) $(MAKESILENT) -f example/qt_example/CMakeFiles/qt_power_meter_example_autogen.dir/build.make example/qt_example/CMakeFiles/qt_power_meter_example_autogen.dir/depend
	$(MAKE) $(MAKESILENT) -f example/qt_example/CMakeFiles/qt_power_meter_example_autogen.dir/build.make example/qt_example/CMakeFiles/qt_power_meter_example_autogen.dir/build
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/work/common/optical_power_meter_driver/build_test/CMakeFiles --progress-num=10 "Built target qt_power_meter_example_autogen"
.PHONY : example/qt_example/CMakeFiles/qt_power_meter_example_autogen.dir/all

# Build rule for subdir invocation for target.
example/qt_example/CMakeFiles/qt_power_meter_example_autogen.dir/rule: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/work/common/optical_power_meter_driver/build_test/CMakeFiles 6
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 example/qt_example/CMakeFiles/qt_power_meter_example_autogen.dir/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/work/common/optical_power_meter_driver/build_test/CMakeFiles 0
.PHONY : example/qt_example/CMakeFiles/qt_power_meter_example_autogen.dir/rule

# Convenience name for target.
qt_power_meter_example_autogen: example/qt_example/CMakeFiles/qt_power_meter_example_autogen.dir/rule
.PHONY : qt_power_meter_example_autogen

# codegen rule for target.
example/qt_example/CMakeFiles/qt_power_meter_example_autogen.dir/codegen:
	$(MAKE) $(MAKESILENT) -f example/qt_example/CMakeFiles/qt_power_meter_example_autogen.dir/build.make example/qt_example/CMakeFiles/qt_power_meter_example_autogen.dir/codegen
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --progress-dir=/home/<USER>/work/common/optical_power_meter_driver/build_test/CMakeFiles --progress-num=10 "Finished codegen for target qt_power_meter_example_autogen"
.PHONY : example/qt_example/CMakeFiles/qt_power_meter_example_autogen.dir/codegen

# clean rule for target.
example/qt_example/CMakeFiles/qt_power_meter_example_autogen.dir/clean:
	$(MAKE) $(MAKESILENT) -f example/qt_example/CMakeFiles/qt_power_meter_example_autogen.dir/build.make example/qt_example/CMakeFiles/qt_power_meter_example_autogen.dir/clean
.PHONY : example/qt_example/CMakeFiles/qt_power_meter_example_autogen.dir/clean

#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

