# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.31

# Default target executed when no arguments are given to make.
default_target: all
.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/local/bin/cmake

# The command to remove a file.
RM = /usr/local/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/work/common/optical_power_meter_driver

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/work/common/optical_power_meter_driver/build_test

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target package
package: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Run CPack packaging tool..."
	cd /home/<USER>/work/common/optical_power_meter_driver/build_test && /usr/local/bin/cpack --config ./CPackConfig.cmake
.PHONY : package

# Special rule for the target package
package/fast: package
.PHONY : package/fast

# Special rule for the target package_source
package_source:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Run CPack packaging tool for source..."
	cd /home/<USER>/work/common/optical_power_meter_driver/build_test && /usr/local/bin/cpack --config ./CPackSourceConfig.cmake /home/<USER>/work/common/optical_power_meter_driver/build_test/CPackSourceConfig.cmake
.PHONY : package_source

# Special rule for the target package_source
package_source/fast: package_source
.PHONY : package_source/fast

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake cache editor..."
	/usr/local/bin/ccmake -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache
.PHONY : edit_cache/fast

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Running CMake to regenerate build system..."
	/usr/local/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache
.PHONY : rebuild_cache/fast

# Special rule for the target list_install_components
list_install_components:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Available install components are: \"Unspecified\""
.PHONY : list_install_components

# Special rule for the target list_install_components
list_install_components/fast: list_install_components
.PHONY : list_install_components/fast

# Special rule for the target install
install: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Install the project..."
	/usr/local/bin/cmake -P cmake_install.cmake
.PHONY : install

# Special rule for the target install
install/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Install the project..."
	/usr/local/bin/cmake -P cmake_install.cmake
.PHONY : install/fast

# Special rule for the target install/local
install/local: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing only the local directory..."
	/usr/local/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local

# Special rule for the target install/local
install/local/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing only the local directory..."
	/usr/local/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
.PHONY : install/local/fast

# Special rule for the target install/strip
install/strip: preinstall
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing the project stripped..."
	/usr/local/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip

# Special rule for the target install/strip
install/strip/fast: preinstall/fast
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --cyan "Installing the project stripped..."
	/usr/local/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
.PHONY : install/strip/fast

# The main all target
all: cmake_check_build_system
	cd /home/<USER>/work/common/optical_power_meter_driver/build_test && $(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/work/common/optical_power_meter_driver/build_test/CMakeFiles /home/<USER>/work/common/optical_power_meter_driver/build_test/example/qt_example//CMakeFiles/progress.marks
	cd /home/<USER>/work/common/optical_power_meter_driver/build_test && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 example/qt_example/all
	$(CMAKE_COMMAND) -E cmake_progress_start /home/<USER>/work/common/optical_power_meter_driver/build_test/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	cd /home/<USER>/work/common/optical_power_meter_driver/build_test && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 example/qt_example/clean
.PHONY : clean

# The main clean target
clean/fast: clean
.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	cd /home/<USER>/work/common/optical_power_meter_driver/build_test && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 example/qt_example/preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	cd /home/<USER>/work/common/optical_power_meter_driver/build_test && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 example/qt_example/preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	cd /home/<USER>/work/common/optical_power_meter_driver/build_test && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

# Convenience name for target.
example/qt_example/CMakeFiles/qt_power_meter_example.dir/rule:
	cd /home/<USER>/work/common/optical_power_meter_driver/build_test && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 example/qt_example/CMakeFiles/qt_power_meter_example.dir/rule
.PHONY : example/qt_example/CMakeFiles/qt_power_meter_example.dir/rule

# Convenience name for target.
qt_power_meter_example: example/qt_example/CMakeFiles/qt_power_meter_example.dir/rule
.PHONY : qt_power_meter_example

# fast build rule for target.
qt_power_meter_example/fast:
	cd /home/<USER>/work/common/optical_power_meter_driver/build_test && $(MAKE) $(MAKESILENT) -f example/qt_example/CMakeFiles/qt_power_meter_example.dir/build.make example/qt_example/CMakeFiles/qt_power_meter_example.dir/build
.PHONY : qt_power_meter_example/fast

# Convenience name for target.
example/qt_example/CMakeFiles/qt_power_meter_example_autogen.dir/rule:
	cd /home/<USER>/work/common/optical_power_meter_driver/build_test && $(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 example/qt_example/CMakeFiles/qt_power_meter_example_autogen.dir/rule
.PHONY : example/qt_example/CMakeFiles/qt_power_meter_example_autogen.dir/rule

# Convenience name for target.
qt_power_meter_example_autogen: example/qt_example/CMakeFiles/qt_power_meter_example_autogen.dir/rule
.PHONY : qt_power_meter_example_autogen

# fast build rule for target.
qt_power_meter_example_autogen/fast:
	cd /home/<USER>/work/common/optical_power_meter_driver/build_test && $(MAKE) $(MAKESILENT) -f example/qt_example/CMakeFiles/qt_power_meter_example_autogen.dir/build.make example/qt_example/CMakeFiles/qt_power_meter_example_autogen.dir/build
.PHONY : qt_power_meter_example_autogen/fast

main_window.o: main_window.cpp.o
.PHONY : main_window.o

# target to build an object file
main_window.cpp.o:
	cd /home/<USER>/work/common/optical_power_meter_driver/build_test && $(MAKE) $(MAKESILENT) -f example/qt_example/CMakeFiles/qt_power_meter_example.dir/build.make example/qt_example/CMakeFiles/qt_power_meter_example.dir/main_window.cpp.o
.PHONY : main_window.cpp.o

main_window.i: main_window.cpp.i
.PHONY : main_window.i

# target to preprocess a source file
main_window.cpp.i:
	cd /home/<USER>/work/common/optical_power_meter_driver/build_test && $(MAKE) $(MAKESILENT) -f example/qt_example/CMakeFiles/qt_power_meter_example.dir/build.make example/qt_example/CMakeFiles/qt_power_meter_example.dir/main_window.cpp.i
.PHONY : main_window.cpp.i

main_window.s: main_window.cpp.s
.PHONY : main_window.s

# target to generate assembly for a file
main_window.cpp.s:
	cd /home/<USER>/work/common/optical_power_meter_driver/build_test && $(MAKE) $(MAKESILENT) -f example/qt_example/CMakeFiles/qt_power_meter_example.dir/build.make example/qt_example/CMakeFiles/qt_power_meter_example.dir/main_window.cpp.s
.PHONY : main_window.cpp.s

qt_example.o: qt_example.cpp.o
.PHONY : qt_example.o

# target to build an object file
qt_example.cpp.o:
	cd /home/<USER>/work/common/optical_power_meter_driver/build_test && $(MAKE) $(MAKESILENT) -f example/qt_example/CMakeFiles/qt_power_meter_example.dir/build.make example/qt_example/CMakeFiles/qt_power_meter_example.dir/qt_example.cpp.o
.PHONY : qt_example.cpp.o

qt_example.i: qt_example.cpp.i
.PHONY : qt_example.i

# target to preprocess a source file
qt_example.cpp.i:
	cd /home/<USER>/work/common/optical_power_meter_driver/build_test && $(MAKE) $(MAKESILENT) -f example/qt_example/CMakeFiles/qt_power_meter_example.dir/build.make example/qt_example/CMakeFiles/qt_power_meter_example.dir/qt_example.cpp.i
.PHONY : qt_example.cpp.i

qt_example.s: qt_example.cpp.s
.PHONY : qt_example.s

# target to generate assembly for a file
qt_example.cpp.s:
	cd /home/<USER>/work/common/optical_power_meter_driver/build_test && $(MAKE) $(MAKESILENT) -f example/qt_example/CMakeFiles/qt_power_meter_example.dir/build.make example/qt_example/CMakeFiles/qt_power_meter_example.dir/qt_example.cpp.s
.PHONY : qt_example.cpp.s

qt_power_meter_example_autogen/mocs_compilation.o: qt_power_meter_example_autogen/mocs_compilation.cpp.o
.PHONY : qt_power_meter_example_autogen/mocs_compilation.o

# target to build an object file
qt_power_meter_example_autogen/mocs_compilation.cpp.o:
	cd /home/<USER>/work/common/optical_power_meter_driver/build_test && $(MAKE) $(MAKESILENT) -f example/qt_example/CMakeFiles/qt_power_meter_example.dir/build.make example/qt_example/CMakeFiles/qt_power_meter_example.dir/qt_power_meter_example_autogen/mocs_compilation.cpp.o
.PHONY : qt_power_meter_example_autogen/mocs_compilation.cpp.o

qt_power_meter_example_autogen/mocs_compilation.i: qt_power_meter_example_autogen/mocs_compilation.cpp.i
.PHONY : qt_power_meter_example_autogen/mocs_compilation.i

# target to preprocess a source file
qt_power_meter_example_autogen/mocs_compilation.cpp.i:
	cd /home/<USER>/work/common/optical_power_meter_driver/build_test && $(MAKE) $(MAKESILENT) -f example/qt_example/CMakeFiles/qt_power_meter_example.dir/build.make example/qt_example/CMakeFiles/qt_power_meter_example.dir/qt_power_meter_example_autogen/mocs_compilation.cpp.i
.PHONY : qt_power_meter_example_autogen/mocs_compilation.cpp.i

qt_power_meter_example_autogen/mocs_compilation.s: qt_power_meter_example_autogen/mocs_compilation.cpp.s
.PHONY : qt_power_meter_example_autogen/mocs_compilation.s

# target to generate assembly for a file
qt_power_meter_example_autogen/mocs_compilation.cpp.s:
	cd /home/<USER>/work/common/optical_power_meter_driver/build_test && $(MAKE) $(MAKESILENT) -f example/qt_example/CMakeFiles/qt_power_meter_example.dir/build.make example/qt_example/CMakeFiles/qt_power_meter_example.dir/qt_power_meter_example_autogen/mocs_compilation.cpp.s
.PHONY : qt_power_meter_example_autogen/mocs_compilation.cpp.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... install"
	@echo "... install/local"
	@echo "... install/strip"
	@echo "... list_install_components"
	@echo "... package"
	@echo "... package_source"
	@echo "... rebuild_cache"
	@echo "... qt_power_meter_example_autogen"
	@echo "... qt_power_meter_example"
	@echo "... main_window.o"
	@echo "... main_window.i"
	@echo "... main_window.s"
	@echo "... qt_example.o"
	@echo "... qt_example.i"
	@echo "... qt_example.s"
	@echo "... qt_power_meter_example_autogen/mocs_compilation.o"
	@echo "... qt_power_meter_example_autogen/mocs_compilation.i"
	@echo "... qt_power_meter_example_autogen/mocs_compilation.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	cd /home/<USER>/work/common/optical_power_meter_driver/build_test && $(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

