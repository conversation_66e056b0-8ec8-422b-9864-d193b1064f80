/usr/bin/c++ -O2 -g -DNDEBUG -Wl,--disable-new-dtags CMakeFiles/qt_power_meter_example.dir/qt_power_meter_example_autogen/mocs_compilation.cpp.o CMakeFiles/qt_power_meter_example.dir/qt_example.cpp.o CMakeFiles/qt_power_meter_example.dir/main_window.cpp.o -o qt_power_meter_example   -L/home/<USER>/work/common/optical_power_meter_driver/lib/opm/focal/bin  -L/home/<USER>/work/common/optical_power_meter_driver/lib/opm/focal  -Wl,-rpath,/home/<USER>/work/common/optical_power_meter_driver/lib/opm/focal/bin:/home/<USER>/work/common/optical_power_meter_driver/lib/opm/focal:/home/<USER>/work/common/optical_power_meter_driver/build/_deps/rsfsc_log-src/focal/lib ../../liboptical_power_meter_driver.a /home/<USER>/work/common/optical_power_meter_driver/build/_deps/rsfsc_log-src/focal/lib/librsfsc_log.so.2.2.5 -lTLPM_64 -lusb-1.0 /usr/lib/x86_64-linux-gnu/libQt5Widgets.so.5.12.8 /usr/lib/x86_64-linux-gnu/libQt5Gui.so.5.12.8 /usr/lib/x86_64-linux-gnu/libQt5Core.so.5.12.8 -ldl
