# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.31

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/local/bin/cmake

# The command to remove a file.
RM = /usr/local/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/work/common/optical_power_meter_driver

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/work/common/optical_power_meter_driver/build_test

# Include any dependencies generated for this target.
include example/qt_example/CMakeFiles/qt_power_meter_example.dir/depend.make
# Include any dependencies generated by the compiler for this target.
include example/qt_example/CMakeFiles/qt_power_meter_example.dir/compiler_depend.make

# Include the progress variables for this target.
include example/qt_example/CMakeFiles/qt_power_meter_example.dir/progress.make

# Include the compile flags for this target's objects.
include example/qt_example/CMakeFiles/qt_power_meter_example.dir/flags.make

example/qt_example/CMakeFiles/qt_power_meter_example.dir/codegen:
.PHONY : example/qt_example/CMakeFiles/qt_power_meter_example.dir/codegen

example/qt_example/CMakeFiles/qt_power_meter_example.dir/qt_power_meter_example_autogen/mocs_compilation.cpp.o: example/qt_example/CMakeFiles/qt_power_meter_example.dir/flags.make
example/qt_example/CMakeFiles/qt_power_meter_example.dir/qt_power_meter_example_autogen/mocs_compilation.cpp.o: example/qt_example/qt_power_meter_example_autogen/mocs_compilation.cpp
example/qt_example/CMakeFiles/qt_power_meter_example.dir/qt_power_meter_example_autogen/mocs_compilation.cpp.o: example/qt_example/CMakeFiles/qt_power_meter_example.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/work/common/optical_power_meter_driver/build_test/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object example/qt_example/CMakeFiles/qt_power_meter_example.dir/qt_power_meter_example_autogen/mocs_compilation.cpp.o"
	cd /home/<USER>/work/common/optical_power_meter_driver/build_test/example/qt_example && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT example/qt_example/CMakeFiles/qt_power_meter_example.dir/qt_power_meter_example_autogen/mocs_compilation.cpp.o -MF CMakeFiles/qt_power_meter_example.dir/qt_power_meter_example_autogen/mocs_compilation.cpp.o.d -o CMakeFiles/qt_power_meter_example.dir/qt_power_meter_example_autogen/mocs_compilation.cpp.o -c /home/<USER>/work/common/optical_power_meter_driver/build_test/example/qt_example/qt_power_meter_example_autogen/mocs_compilation.cpp

example/qt_example/CMakeFiles/qt_power_meter_example.dir/qt_power_meter_example_autogen/mocs_compilation.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/qt_power_meter_example.dir/qt_power_meter_example_autogen/mocs_compilation.cpp.i"
	cd /home/<USER>/work/common/optical_power_meter_driver/build_test/example/qt_example && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/work/common/optical_power_meter_driver/build_test/example/qt_example/qt_power_meter_example_autogen/mocs_compilation.cpp > CMakeFiles/qt_power_meter_example.dir/qt_power_meter_example_autogen/mocs_compilation.cpp.i

example/qt_example/CMakeFiles/qt_power_meter_example.dir/qt_power_meter_example_autogen/mocs_compilation.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/qt_power_meter_example.dir/qt_power_meter_example_autogen/mocs_compilation.cpp.s"
	cd /home/<USER>/work/common/optical_power_meter_driver/build_test/example/qt_example && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/work/common/optical_power_meter_driver/build_test/example/qt_example/qt_power_meter_example_autogen/mocs_compilation.cpp -o CMakeFiles/qt_power_meter_example.dir/qt_power_meter_example_autogen/mocs_compilation.cpp.s

example/qt_example/CMakeFiles/qt_power_meter_example.dir/qt_example.cpp.o: example/qt_example/CMakeFiles/qt_power_meter_example.dir/flags.make
example/qt_example/CMakeFiles/qt_power_meter_example.dir/qt_example.cpp.o: /home/<USER>/work/common/optical_power_meter_driver/example/qt_example/qt_example.cpp
example/qt_example/CMakeFiles/qt_power_meter_example.dir/qt_example.cpp.o: example/qt_example/CMakeFiles/qt_power_meter_example.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/work/common/optical_power_meter_driver/build_test/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object example/qt_example/CMakeFiles/qt_power_meter_example.dir/qt_example.cpp.o"
	cd /home/<USER>/work/common/optical_power_meter_driver/build_test/example/qt_example && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT example/qt_example/CMakeFiles/qt_power_meter_example.dir/qt_example.cpp.o -MF CMakeFiles/qt_power_meter_example.dir/qt_example.cpp.o.d -o CMakeFiles/qt_power_meter_example.dir/qt_example.cpp.o -c /home/<USER>/work/common/optical_power_meter_driver/example/qt_example/qt_example.cpp

example/qt_example/CMakeFiles/qt_power_meter_example.dir/qt_example.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/qt_power_meter_example.dir/qt_example.cpp.i"
	cd /home/<USER>/work/common/optical_power_meter_driver/build_test/example/qt_example && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/work/common/optical_power_meter_driver/example/qt_example/qt_example.cpp > CMakeFiles/qt_power_meter_example.dir/qt_example.cpp.i

example/qt_example/CMakeFiles/qt_power_meter_example.dir/qt_example.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/qt_power_meter_example.dir/qt_example.cpp.s"
	cd /home/<USER>/work/common/optical_power_meter_driver/build_test/example/qt_example && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/work/common/optical_power_meter_driver/example/qt_example/qt_example.cpp -o CMakeFiles/qt_power_meter_example.dir/qt_example.cpp.s

example/qt_example/CMakeFiles/qt_power_meter_example.dir/main_window.cpp.o: example/qt_example/CMakeFiles/qt_power_meter_example.dir/flags.make
example/qt_example/CMakeFiles/qt_power_meter_example.dir/main_window.cpp.o: /home/<USER>/work/common/optical_power_meter_driver/example/qt_example/main_window.cpp
example/qt_example/CMakeFiles/qt_power_meter_example.dir/main_window.cpp.o: example/qt_example/CMakeFiles/qt_power_meter_example.dir/compiler_depend.ts
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --progress-dir=/home/<USER>/work/common/optical_power_meter_driver/build_test/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object example/qt_example/CMakeFiles/qt_power_meter_example.dir/main_window.cpp.o"
	cd /home/<USER>/work/common/optical_power_meter_driver/build_test/example/qt_example && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -MD -MT example/qt_example/CMakeFiles/qt_power_meter_example.dir/main_window.cpp.o -MF CMakeFiles/qt_power_meter_example.dir/main_window.cpp.o.d -o CMakeFiles/qt_power_meter_example.dir/main_window.cpp.o -c /home/<USER>/work/common/optical_power_meter_driver/example/qt_example/main_window.cpp

example/qt_example/CMakeFiles/qt_power_meter_example.dir/main_window.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Preprocessing CXX source to CMakeFiles/qt_power_meter_example.dir/main_window.cpp.i"
	cd /home/<USER>/work/common/optical_power_meter_driver/build_test/example/qt_example && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /home/<USER>/work/common/optical_power_meter_driver/example/qt_example/main_window.cpp > CMakeFiles/qt_power_meter_example.dir/main_window.cpp.i

example/qt_example/CMakeFiles/qt_power_meter_example.dir/main_window.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green "Compiling CXX source to assembly CMakeFiles/qt_power_meter_example.dir/main_window.cpp.s"
	cd /home/<USER>/work/common/optical_power_meter_driver/build_test/example/qt_example && /usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /home/<USER>/work/common/optical_power_meter_driver/example/qt_example/main_window.cpp -o CMakeFiles/qt_power_meter_example.dir/main_window.cpp.s

# Object files for target qt_power_meter_example
qt_power_meter_example_OBJECTS = \
"CMakeFiles/qt_power_meter_example.dir/qt_power_meter_example_autogen/mocs_compilation.cpp.o" \
"CMakeFiles/qt_power_meter_example.dir/qt_example.cpp.o" \
"CMakeFiles/qt_power_meter_example.dir/main_window.cpp.o"

# External object files for target qt_power_meter_example
qt_power_meter_example_EXTERNAL_OBJECTS =

example/qt_example/qt_power_meter_example: example/qt_example/CMakeFiles/qt_power_meter_example.dir/qt_power_meter_example_autogen/mocs_compilation.cpp.o
example/qt_example/qt_power_meter_example: example/qt_example/CMakeFiles/qt_power_meter_example.dir/qt_example.cpp.o
example/qt_example/qt_power_meter_example: example/qt_example/CMakeFiles/qt_power_meter_example.dir/main_window.cpp.o
example/qt_example/qt_power_meter_example: example/qt_example/CMakeFiles/qt_power_meter_example.dir/build.make
example/qt_example/qt_power_meter_example: liboptical_power_meter_driver.a
example/qt_example/qt_power_meter_example: /home/<USER>/work/common/optical_power_meter_driver/build/_deps/rsfsc_log-src/focal/lib/librsfsc_log.so.2.2.5
example/qt_example/qt_power_meter_example: /usr/lib/x86_64-linux-gnu/libQt5Widgets.so.5.12.8
example/qt_example/qt_power_meter_example: /usr/lib/x86_64-linux-gnu/libQt5Gui.so.5.12.8
example/qt_example/qt_power_meter_example: /usr/lib/x86_64-linux-gnu/libQt5Core.so.5.12.8
example/qt_example/qt_power_meter_example: example/qt_example/CMakeFiles/qt_power_meter_example.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --green --bold --progress-dir=/home/<USER>/work/common/optical_power_meter_driver/build_test/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Linking CXX executable qt_power_meter_example"
	cd /home/<USER>/work/common/optical_power_meter_driver/build_test/example/qt_example && $(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/qt_power_meter_example.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
example/qt_example/CMakeFiles/qt_power_meter_example.dir/build: example/qt_example/qt_power_meter_example
.PHONY : example/qt_example/CMakeFiles/qt_power_meter_example.dir/build

example/qt_example/CMakeFiles/qt_power_meter_example.dir/clean:
	cd /home/<USER>/work/common/optical_power_meter_driver/build_test/example/qt_example && $(CMAKE_COMMAND) -P CMakeFiles/qt_power_meter_example.dir/cmake_clean.cmake
.PHONY : example/qt_example/CMakeFiles/qt_power_meter_example.dir/clean

example/qt_example/CMakeFiles/qt_power_meter_example.dir/depend:
	cd /home/<USER>/work/common/optical_power_meter_driver/build_test && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/work/common/optical_power_meter_driver /home/<USER>/work/common/optical_power_meter_driver/example/qt_example /home/<USER>/work/common/optical_power_meter_driver/build_test /home/<USER>/work/common/optical_power_meter_driver/build_test/example/qt_example /home/<USER>/work/common/optical_power_meter_driver/build_test/example/qt_example/CMakeFiles/qt_power_meter_example.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : example/qt_example/CMakeFiles/qt_power_meter_example.dir/depend

