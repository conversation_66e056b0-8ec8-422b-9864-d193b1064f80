{"BUILD_DIR": "/home/<USER>/work/common/optical_power_meter_driver/build_test/example/qt_example/qt_power_meter_example_autogen", "CMAKE_BINARY_DIR": "/home/<USER>/work/common/optical_power_meter_driver/build_test", "CMAKE_CURRENT_BINARY_DIR": "/home/<USER>/work/common/optical_power_meter_driver/build_test/example/qt_example", "CMAKE_CURRENT_SOURCE_DIR": "/home/<USER>/work/common/optical_power_meter_driver/example/qt_example", "CMAKE_EXECUTABLE": "/usr/local/bin/cmake", "CMAKE_LIST_FILES": ["/home/<USER>/work/common/optical_power_meter_driver/example/qt_example/CMakeLists.txt", "/usr/lib/x86_64-linux-gnu/cmake/Qt5/Qt5ConfigVersion.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt5/Qt5Config.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt5/Qt5ModuleLocation.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt5Core/Qt5CoreConfigVersion.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt5Core/Qt5CoreConfig.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt5Widgets/Qt5WidgetsConfigVersion.cmake", "/usr/lib/x86_64-linux-gnu/cmake/Qt5Widgets/Qt5WidgetsConfig.cmake"], "CMAKE_SOURCE_DIR": "/home/<USER>/work/common/optical_power_meter_driver", "CROSS_CONFIG": false, "DEP_FILE": "", "DEP_FILE_RULE_NAME": "", "HEADERS": [["/home/<USER>/work/common/optical_power_meter_driver/example/qt_example/main_window.h", "MU", "EWIEGA46WW/moc_main_window.cpp", null]], "HEADER_EXTENSIONS": ["h", "hh", "h++", "hm", "hpp", "hxx", "in", "txx"], "INCLUDE_DIR": "/home/<USER>/work/common/optical_power_meter_driver/build_test/example/qt_example/qt_power_meter_example_autogen/include", "MOC_COMPILATION_FILE": "/home/<USER>/work/common/optical_power_meter_driver/build_test/example/qt_example/qt_power_meter_example_autogen/mocs_compilation.cpp", "MOC_DEFINITIONS": ["QT_CORE_LIB", "QT_GUI_LIB", "QT_NO_DEBUG", "QT_NO_KEYWORDS", "QT_WIDGETS_LIB", "__TL_COMPILE_64BIT__"], "MOC_DEPEND_FILTERS": [["Q_PLUGIN_METADATA", "[\n][ \t]*Q_PLUGIN_METADATA[ \t]*\\([^\\)]*FILE[ \t]*\"([^\"]+)\""]], "MOC_INCLUDES": ["/home/<USER>/work/common/optical_power_meter_driver/example/qt_example/include", "/home/<USER>/work/common/optical_power_meter_driver/include", "/home/<USER>/work/common/optical_power_meter_driver/build/_deps/rsfsc_log-src/focal/include", "/usr/include/x86_64-linux-gnu/qt5", "/usr/include/x86_64-linux-gnu/qt5/QtWidgets", "/usr/include/x86_64-linux-gnu/qt5/QtGui", "/usr/include/x86_64-linux-gnu/qt5/QtCore", "/usr/lib/x86_64-linux-gnu/qt5/mkspecs/linux-g++", "/home/<USER>/work/common/optical_power_meter_driver/lib/opm/focal/include", "/usr/include/c++/9", "/usr/include/x86_64-linux-gnu/c++/9", "/usr/include/c++/9/backward", "/usr/lib/gcc/x86_64-linux-gnu/9/include", "/usr/local/include", "/usr/include/x86_64-linux-gnu", "/usr/include"], "MOC_MACRO_NAMES": ["Q_OBJECT", "Q_GADGET", "Q_NAMESPACE", "Q_NAMESPACE_EXPORT"], "MOC_OPTIONS": [], "MOC_PATH_PREFIX": false, "MOC_PREDEFS_CMD": ["/usr/bin/c++", "-std=c++17", "-dM", "-E", "-c", "/usr/local/share/cmake-3.31/Modules/CMakeCXXCompilerABI.cpp"], "MOC_PREDEFS_FILE": "/home/<USER>/work/common/optical_power_meter_driver/build_test/example/qt_example/qt_power_meter_example_autogen/moc_predefs.h", "MOC_RELAXED_MODE": false, "MOC_SKIP": [], "MULTI_CONFIG": false, "PARALLEL": 8, "PARSE_CACHE_FILE": "/home/<USER>/work/common/optical_power_meter_driver/build_test/example/qt_example/CMakeFiles/qt_power_meter_example_autogen.dir/ParseCache.txt", "QT_MOC_EXECUTABLE": "/usr/lib/qt5/bin/moc", "QT_UIC_EXECUTABLE": "/usr/lib/qt5/bin/uic", "QT_VERSION_MAJOR": 5, "QT_VERSION_MINOR": 12, "SETTINGS_FILE": "/home/<USER>/work/common/optical_power_meter_driver/build_test/example/qt_example/CMakeFiles/qt_power_meter_example_autogen.dir/AutogenUsed.txt", "SOURCES": [["/home/<USER>/work/common/optical_power_meter_driver/example/qt_example/main_window.cpp", "MU", null], ["/home/<USER>/work/common/optical_power_meter_driver/example/qt_example/qt_example.cpp", "MU", null]], "UIC_OPTIONS": [], "UIC_SEARCH_PATHS": [], "UIC_SKIP": [], "UIC_UI_FILES": [], "USE_BETTER_GRAPH": false, "VERBOSITY": 0}