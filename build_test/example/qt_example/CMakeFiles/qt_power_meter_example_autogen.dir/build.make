# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.31

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:

#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:

# Disable VCS-based implicit rules.
% : %,v

# Disable VCS-based implicit rules.
% : RCS/%

# Disable VCS-based implicit rules.
% : RCS/%,v

# Disable VCS-based implicit rules.
% : SCCS/s.%

# Disable VCS-based implicit rules.
% : s.%

.SUFFIXES: .hpux_make_needs_suffix_list

# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:
.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/local/bin/cmake

# The command to remove a file.
RM = /usr/local/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /home/<USER>/work/common/optical_power_meter_driver

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /home/<USER>/work/common/optical_power_meter_driver/build_test

# Utility rule file for qt_power_meter_example_autogen.

# Include any custom commands dependencies for this target.
include example/qt_example/CMakeFiles/qt_power_meter_example_autogen.dir/compiler_depend.make

# Include the progress variables for this target.
include example/qt_example/CMakeFiles/qt_power_meter_example_autogen.dir/progress.make

example/qt_example/CMakeFiles/qt_power_meter_example_autogen:
	@$(CMAKE_COMMAND) -E cmake_echo_color "--switch=$(COLOR)" --blue --bold --progress-dir=/home/<USER>/work/common/optical_power_meter_driver/build_test/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Automatic MOC and UIC for target qt_power_meter_example"
	cd /home/<USER>/work/common/optical_power_meter_driver/build_test/example/qt_example && /usr/local/bin/cmake -E cmake_autogen /home/<USER>/work/common/optical_power_meter_driver/build_test/example/qt_example/CMakeFiles/qt_power_meter_example_autogen.dir/AutogenInfo.json RelWithDebInfo

example/qt_example/CMakeFiles/qt_power_meter_example_autogen.dir/codegen:
.PHONY : example/qt_example/CMakeFiles/qt_power_meter_example_autogen.dir/codegen

qt_power_meter_example_autogen: example/qt_example/CMakeFiles/qt_power_meter_example_autogen
qt_power_meter_example_autogen: example/qt_example/CMakeFiles/qt_power_meter_example_autogen.dir/build.make
.PHONY : qt_power_meter_example_autogen

# Rule to build all files generated by this target.
example/qt_example/CMakeFiles/qt_power_meter_example_autogen.dir/build: qt_power_meter_example_autogen
.PHONY : example/qt_example/CMakeFiles/qt_power_meter_example_autogen.dir/build

example/qt_example/CMakeFiles/qt_power_meter_example_autogen.dir/clean:
	cd /home/<USER>/work/common/optical_power_meter_driver/build_test/example/qt_example && $(CMAKE_COMMAND) -P CMakeFiles/qt_power_meter_example_autogen.dir/cmake_clean.cmake
.PHONY : example/qt_example/CMakeFiles/qt_power_meter_example_autogen.dir/clean

example/qt_example/CMakeFiles/qt_power_meter_example_autogen.dir/depend:
	cd /home/<USER>/work/common/optical_power_meter_driver/build_test && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /home/<USER>/work/common/optical_power_meter_driver /home/<USER>/work/common/optical_power_meter_driver/example/qt_example /home/<USER>/work/common/optical_power_meter_driver/build_test /home/<USER>/work/common/optical_power_meter_driver/build_test/example/qt_example /home/<USER>/work/common/optical_power_meter_driver/build_test/example/qt_example/CMakeFiles/qt_power_meter_example_autogen.dir/DependInfo.cmake "--color=$(COLOR)"
.PHONY : example/qt_example/CMakeFiles/qt_power_meter_example_autogen.dir/depend

