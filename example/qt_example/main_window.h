/******************************************************************************
 * Copyright 2022 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
#ifndef MAIN_WINDOW_H
#define MAIN_WINDOW_H

#include "thorlabs_optical_power_meter.h"
#include <QtWidgets/QMainWindow>
#include <memory>

class QPushButton;
class QComboBox;

namespace robosense::lidar
{

class MainWindow final : public QMainWindow
{
  Q_OBJECT
public:
  explicit MainWindow(QWidget* _parent = nullptr);
  MainWindow(MainWindow&&)      = delete;
  MainWindow(const MainWindow&) = delete;
  MainWindow& operator=(MainWindow&&) = delete;
  MainWindow& operator=(const MainWindow&) = delete;
  ~MainWindow() override;

protected Q_SLOTS:
  void slotOpen();

private:
  void setupLayout();
  void init();
  QPushButton* pushbutton_open_ { nullptr };
  std::unique_ptr<ThorlabsOpticalPowerMeter> driver_ptr_ { nullptr };
};

}  // namespace robosense::lidar

#endif  // MAIN_WINDOW_H