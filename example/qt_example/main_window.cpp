/******************************************************************************
 * Copyright 2022 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#include "main_window.h"
#include "rsfsc_log/rsfsc_log.h"
#include <QString>
#include <QtCore/QCoreApplication>
#include <QtWidgets/QComboBox>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QTextBrowser>
#include <QtWidgets/QVBoxLayout>
#include <qchar.h>
#include <qpushbutton.h>

namespace robosense::lidar
{

MainWindow::MainWindow(QWidget* _parent) :
  QMainWindow(_parent),
  driver_ptr_(std::make_unique<ThorlabsOpticalPowerMeter>(ThorlabsOpticalPowerMeter::PowerMeterType::PM100X))
{
  setupLayout();
  init();
}

MainWindow::~MainWindow() { driver_ptr_->close(); }

void MainWindow::setupLayout()
{
  auto* layout = new QGridLayout;

  pushbutton_open_ = new QPushButton(QString::fromUtf8("连接"), this);
  layout->addWidget(pushbutton_open_, 0, 1, 1, 1);

  auto* main_layout = new QHBoxLayout;
  main_layout->addLayout(layout);

  QWidget* widget_main = new QWidget(this);
  widget_main->setLayout(main_layout);
  this->setCentralWidget(widget_main);

  QObject::connect(pushbutton_open_, &QPushButton::clicked, this, &MainWindow::slotOpen);
}

void MainWindow::init() {}

void MainWindow::slotOpen() {}

}  // namespace robosense::lidar
