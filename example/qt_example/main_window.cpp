/******************************************************************************
 * Copyright 2022 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#include "main_window.h"
#include "rsfsc_log/rsfsc_log.h"
#include <QString>
#include <QtCore/QCoreApplication>
#include <QtCore/QDateTime>
#include <QtCore/QTimer>
#include <QtWidgets/QComboBox>
#include <QtWidgets/QDoubleSpinBox>
#include <QtWidgets/QGridLayout>
#include <QtWidgets/QGroupBox>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QLabel>
#include <QtWidgets/QLineEdit>
#include <QtWidgets/QMessageBox>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QSpinBox>
#include <QtWidgets/QTextBrowser>
#include <QtWidgets/QTextEdit>
#include <QtWidgets/QVBoxLayout>

namespace robosense::lidar
{

MainWindow::MainWindow(QWidget* _parent) :
  QMainWindow(_parent),
  power_meter_type_combo_(nullptr),
  serial_number_edit_(nullptr),
  find_devices_button_(nullptr),
  pushbutton_open_(nullptr),
  pushbutton_close_(nullptr),
  connection_status_label_(nullptr),
  power_range_spinbox_(nullptr),
  set_power_range_button_(nullptr),
  avg_count_spinbox_(nullptr),
  set_avg_count_button_(nullptr),
  wave_length_spinbox_(nullptr),
  set_wave_length_button_(nullptr),
  measure_power_button_(nullptr),
  start_continuous_button_(nullptr),
  stop_continuous_button_(nullptr),
  current_power_label_(nullptr),
  get_error_button_(nullptr),
  log_index_spinbox_(nullptr),
  set_log_index_button_(nullptr),
  get_log_index_button_(nullptr),
  log_text_edit_(nullptr),
  measurement_timer_(nullptr),
  driver_ptr_(std::make_unique<ThorlabsOpticalPowerMeter>(ThorlabsOpticalPowerMeter::PowerMeterType::PM100X)),
  is_connected_(false),
  is_measuring_(false)
{
  setupLayout();
  init();
}

MainWindow::~MainWindow() { driver_ptr_->close(); }

void MainWindow::setupLayout()
{
  setWindowTitle("Thorlabs Optical Power Meter Control Panel");
  resize(900, 700);

  auto* main_widget = new QWidget(this);
  setCentralWidget(main_widget);
  auto* main_layout = new QVBoxLayout(main_widget);

  // Device Connection Group
  auto* connection_group  = new QGroupBox("Device Connection");
  auto* connection_layout = new QGridLayout(connection_group);

  connection_layout->addWidget(new QLabel("Power Meter Type:"), 0, 0);
  power_meter_type_combo_ = new QComboBox();
  power_meter_type_combo_->addItem("PM100X", static_cast<int>(ThorlabsOpticalPowerMeter::PowerMeterType::PM100X));
  power_meter_type_combo_->addItem("PM101X", static_cast<int>(ThorlabsOpticalPowerMeter::PowerMeterType::PM101X));
  power_meter_type_combo_->setCurrentIndex(1);  // Default to PM101X
  connection_layout->addWidget(power_meter_type_combo_, 0, 1);

  find_devices_button_ = new QPushButton("Find Devices");
  connection_layout->addWidget(find_devices_button_, 0, 2);

  connection_layout->addWidget(new QLabel("Serial Number:"), 1, 0);
  serial_number_edit_ = new QLineEdit();
  serial_number_edit_->setPlaceholderText("Enter device serial number (e.g., *********)");
  connection_layout->addWidget(serial_number_edit_, 1, 1);

  pushbutton_open_ = new QPushButton("Connect");
  connection_layout->addWidget(pushbutton_open_, 1, 2);

  connection_status_label_ = new QLabel("Status: Disconnected");
  connection_status_label_->setStyleSheet("color: red; font-weight: bold;");
  connection_layout->addWidget(connection_status_label_, 2, 0);

  pushbutton_close_ = new QPushButton("Disconnect");
  pushbutton_close_->setEnabled(false);
  connection_layout->addWidget(pushbutton_close_, 2, 1);

  main_layout->addWidget(connection_group);

  // Measurement Settings Group
  auto* settings_group  = new QGroupBox("Measurement Settings");
  auto* settings_layout = new QGridLayout(settings_group);

  settings_layout->addWidget(new QLabel("Power Range (W):"), 0, 0);
  power_range_spinbox_ = new QDoubleSpinBox();
  power_range_spinbox_->setDecimals(4);
  power_range_spinbox_->setRange(0.0001, 1.0);
  power_range_spinbox_->setValue(0.02);
  settings_layout->addWidget(power_range_spinbox_, 0, 1);

  set_power_range_button_ = new QPushButton("Set Power Range");
  set_power_range_button_->setEnabled(false);
  settings_layout->addWidget(set_power_range_button_, 0, 2);

  settings_layout->addWidget(new QLabel("Average Count:"), 1, 0);
  avg_count_spinbox_ = new QSpinBox();
  avg_count_spinbox_->setRange(1, 10000);
  avg_count_spinbox_->setValue(500);
  settings_layout->addWidget(avg_count_spinbox_, 1, 1);

  set_avg_count_button_ = new QPushButton("Set Average Count");
  set_avg_count_button_->setEnabled(false);
  settings_layout->addWidget(set_avg_count_button_, 1, 2);

  settings_layout->addWidget(new QLabel("Wave Length (nm):"), 2, 0);
  wave_length_spinbox_ = new QDoubleSpinBox();
  wave_length_spinbox_->setRange(400, 1100);
  wave_length_spinbox_->setValue(905.0);
  settings_layout->addWidget(wave_length_spinbox_, 2, 1);

  set_wave_length_button_ = new QPushButton("Set Wave Length");
  set_wave_length_button_->setEnabled(false);
  settings_layout->addWidget(set_wave_length_button_, 2, 2);

  main_layout->addWidget(settings_group);

  // Measurement Controls Group
  auto* measurement_group  = new QGroupBox("Power Measurement");
  auto* measurement_layout = new QGridLayout(measurement_group);

  current_power_label_ = new QLabel("Current Power: -- mW");
  current_power_label_->setStyleSheet("font-size: 14px; font-weight: bold; color: blue;");
  measurement_layout->addWidget(current_power_label_, 0, 0, 1, 3);

  measure_power_button_ = new QPushButton("Measure Power (Single)");
  measure_power_button_->setEnabled(false);
  measurement_layout->addWidget(measure_power_button_, 1, 0);

  start_continuous_button_ = new QPushButton("Start Continuous");
  start_continuous_button_->setEnabled(false);
  measurement_layout->addWidget(start_continuous_button_, 1, 1);

  stop_continuous_button_ = new QPushButton("Stop Continuous");
  stop_continuous_button_->setEnabled(false);
  measurement_layout->addWidget(stop_continuous_button_, 1, 2);

  main_layout->addWidget(measurement_group);

  // Log and Error Controls Group
  auto* log_group  = new QGroupBox("Log and Error Controls");
  auto* log_layout = new QGridLayout(log_group);

  get_error_button_ = new QPushButton("Get Error Message");
  log_layout->addWidget(get_error_button_, 0, 0);

  log_layout->addWidget(new QLabel("Log Index:"), 0, 1);
  log_index_spinbox_ = new QSpinBox();
  log_index_spinbox_->setRange(0, 999);
  log_index_spinbox_->setValue(0);
  log_layout->addWidget(log_index_spinbox_, 0, 2);

  set_log_index_button_ = new QPushButton("Set Log Index");
  set_log_index_button_->setEnabled(false);
  log_layout->addWidget(set_log_index_button_, 0, 3);

  get_log_index_button_ = new QPushButton("Get Log Index");
  get_log_index_button_->setEnabled(false);
  log_layout->addWidget(get_log_index_button_, 0, 4);

  main_layout->addWidget(log_group);

  // Log Text Area
  log_text_edit_ = new QTextEdit();
  log_text_edit_->setMaximumHeight(150);
  log_text_edit_->setReadOnly(true);
  main_layout->addWidget(log_text_edit_);

  // Create timer for continuous measurement
  measurement_timer_ = new QTimer(this);
  measurement_timer_->setInterval(100);  // 100ms interval

  // Connect all signals
  connectSignals();
}

void MainWindow::connectSignals()
{
  QObject::connect(find_devices_button_, &QPushButton::clicked, this, &MainWindow::slotFindDevices);
  QObject::connect(pushbutton_open_, &QPushButton::clicked, this, &MainWindow::slotOpen);
  QObject::connect(pushbutton_close_, &QPushButton::clicked, this, &MainWindow::slotClose);
  QObject::connect(set_power_range_button_, &QPushButton::clicked, this, &MainWindow::slotSetPowerRange);
  QObject::connect(set_avg_count_button_, &QPushButton::clicked, this, &MainWindow::slotSetAvgCount);
  QObject::connect(set_wave_length_button_, &QPushButton::clicked, this, &MainWindow::slotSetWaveLength);
  QObject::connect(measure_power_button_, &QPushButton::clicked, this, &MainWindow::slotMeasurePower);
  QObject::connect(start_continuous_button_, &QPushButton::clicked, this, &MainWindow::slotStartContinuousMeasurement);
  QObject::connect(stop_continuous_button_, &QPushButton::clicked, this, &MainWindow::slotStopContinuousMeasurement);
  QObject::connect(get_error_button_, &QPushButton::clicked, this, &MainWindow::slotGetErrorMsg);
  QObject::connect(set_log_index_button_, &QPushButton::clicked, this, &MainWindow::slotSetLogIndex);
  QObject::connect(get_log_index_button_, &QPushButton::clicked, this, &MainWindow::slotGetLogIndex);
  QObject::connect(measurement_timer_, &QTimer::timeout, this, &MainWindow::onMeasurementTimer);
}

void MainWindow::init() {}

void MainWindow::updateConnectionStatus(bool _connected)
{
  is_connected_ = _connected;

  if (_connected)
  {
    connection_status_label_->setText("Status: Connected");
    connection_status_label_->setStyleSheet("color: green; font-weight: bold;");

    pushbutton_open_->setEnabled(false);
    pushbutton_close_->setEnabled(true);
    set_power_range_button_->setEnabled(true);
    set_avg_count_button_->setEnabled(true);
    set_wave_length_button_->setEnabled(true);
    measure_power_button_->setEnabled(true);
    start_continuous_button_->setEnabled(true);
    set_log_index_button_->setEnabled(true);
    get_log_index_button_->setEnabled(true);
  }
  else
  {
    connection_status_label_->setText("Status: Disconnected");
    connection_status_label_->setStyleSheet("color: red; font-weight: bold;");

    pushbutton_open_->setEnabled(true);
    pushbutton_close_->setEnabled(false);
    set_power_range_button_->setEnabled(false);
    set_avg_count_button_->setEnabled(false);
    set_wave_length_button_->setEnabled(false);
    measure_power_button_->setEnabled(false);
    start_continuous_button_->setEnabled(false);
    stop_continuous_button_->setEnabled(false);
    set_log_index_button_->setEnabled(false);
    get_log_index_button_->setEnabled(false);

    current_power_label_->setText("Current Power: -- mW");
  }
}

void MainWindow::slotFindDevices()
{
  logMessage("Searching for devices...");

  auto power_meter_type =
    static_cast<ThorlabsOpticalPowerMeter::PowerMeterType>(power_meter_type_combo_->currentData().toInt());

  bool found = ThorlabsOpticalPowerMeter::findRsrc(power_meter_type);

  if (found)
  {
    logMessage("Device search completed. Check console output for available devices.");
  }
  else
  {
    logMessage("No devices found or search failed.");
  }
}

void MainWindow::slotOpen()
{
  QString serial_number = serial_number_edit_->text().trimmed();
  if (serial_number.isEmpty())
  {
    QMessageBox::warning(this, "Warning", "Please enter a serial number");
    return;
  }

  logMessage(QString("Connecting to device: %1").arg(serial_number));

  double power_range = power_range_spinbox_->value();
  double wave_length = wave_length_spinbox_->value();

  bool success = driver_ptr_->open(serial_number.toStdString(), power_range, wave_length);

  if (success)
  {
    updateConnectionStatus(true);
    logMessage(QString("Successfully connected to device: %1").arg(serial_number));

    // Set initial average count
    slotSetAvgCount();
  }
  else
  {
    logMessage(QString("Failed to connect: %1").arg(QString::fromStdString(driver_ptr_->getErrorMsg())));
  }
}

void MainWindow::slotClose()
{
  if (!is_connected_)
  {
    return;
  }

  // Stop continuous measurement if running
  if (is_measuring_)
  {
    slotStopContinuousMeasurement();
  }

  logMessage("Disconnecting from device...");

  bool success = driver_ptr_->close();
  updateConnectionStatus(false);

  if (success)
  {
    logMessage("Successfully disconnected from device");
  }
  else
  {
    logMessage(QString("Disconnect warning: %1").arg(QString::fromStdString(driver_ptr_->getErrorMsg())));
  }
}

void MainWindow::slotSetPowerRange()
{
  if (!is_connected_)
  {
    logMessage("Device not connected");
    return;
  }

  double power_range = power_range_spinbox_->value();
  logMessage(QString("Setting power range to: %1 W").arg(power_range));

  bool success = driver_ptr_->setPowerRange(power_range);

  if (success)
  {
    logMessage(QString("Power range set successfully: %1 W").arg(power_range));
  }
  else
  {
    logMessage(QString("Failed to set power range: %1").arg(QString::fromStdString(driver_ptr_->getErrorMsg())));
  }
}

void MainWindow::slotSetAvgCount()
{
  if (!is_connected_)
  {
    logMessage("Device not connected");
    return;
  }

  int avg_count = avg_count_spinbox_->value();
  logMessage(QString("Setting average count to: %1").arg(avg_count));

  bool success = driver_ptr_->setAvgCnt(avg_count);

  if (success)
  {
    logMessage(QString("Average count set successfully: %1").arg(avg_count));
  }
  else
  {
    logMessage(QString("Failed to set average count: %1").arg(QString::fromStdString(driver_ptr_->getErrorMsg())));
  }
}

void MainWindow::slotMeasurePower()
{
  if (!is_connected_)
  {
    logMessage("Device not connected");
    return;
  }

  double power = 0.0;
  bool success = driver_ptr_->measurePower(power);

  if (success)
  {
    double power_mw = power * 1000.0;
    current_power_label_->setText(QString("Current Power: %1 mW").arg(power_mw, 0, 'f', 3));
    logMessage(QString("Single measurement: %1 mW").arg(power_mw, 0, 'f', 3));
  }
  else
  {
    logMessage(QString("Failed to measure power: %1").arg(QString::fromStdString(driver_ptr_->getErrorMsg())));
  }
}

void MainWindow::slotStartContinuousMeasurement()
{
  if (!is_connected_)
  {
    logMessage("Device not connected");
    return;
  }

  is_measuring_ = true;
  measurement_timer_->start();

  start_continuous_button_->setEnabled(false);
  stop_continuous_button_->setEnabled(true);

  logMessage("Started continuous measurement");
}

void MainWindow::slotStopContinuousMeasurement()
{
  if (!is_measuring_)
  {
    return;
  }

  is_measuring_ = false;
  measurement_timer_->stop();

  start_continuous_button_->setEnabled(true);
  stop_continuous_button_->setEnabled(false);

  logMessage("Stopped continuous measurement");
}

void MainWindow::slotSetWaveLength()
{
  // Note: Wave length setting is typically done during open() call
  // This function is for demonstration purposes
  double wave_length = wave_length_spinbox_->value();
  logMessage(QString("Wave length setting: %1 nm (Note: This is set during connection)").arg(wave_length));
}

void MainWindow::slotGetErrorMsg()
{
  std::string error_msg = driver_ptr_->getErrorMsg();
  if (error_msg.empty())
  {
    logMessage("No error message available");
  }
  else
  {
    logMessage(QString("Error message: %1").arg(QString::fromStdString(error_msg)));
  }
}

void MainWindow::slotSetLogIndex()
{
  if (!is_connected_)
  {
    logMessage("Device not connected");
    return;
  }

  int log_index = log_index_spinbox_->value();
  driver_ptr_->setLogIndex(log_index);
  logMessage(QString("Log index set to: %1").arg(log_index));
}

void MainWindow::slotGetLogIndex()
{
  if (!is_connected_)
  {
    logMessage("Device not connected");
    return;
  }

  int log_index = driver_ptr_->getLogIndex();
  logMessage(QString("Current log index: %1").arg(log_index));
  log_index_spinbox_->setValue(log_index);
}

void MainWindow::onMeasurementTimer()
{
  if (!is_connected_ || !is_measuring_)
  {
    return;
  }

  double power = 0.0;
  bool success = driver_ptr_->measurePower(power);

  if (success)
  {
    double power_mw = power * 1000.0;
    current_power_label_->setText(QString("Current Power: %1 mW").arg(power_mw, 0, 'f', 3));
  }
  else
  {
    logMessage(QString("Continuous measurement failed: %1").arg(QString::fromStdString(driver_ptr_->getErrorMsg())));
    slotStopContinuousMeasurement();
  }
}

}  // namespace robosense::lidar
