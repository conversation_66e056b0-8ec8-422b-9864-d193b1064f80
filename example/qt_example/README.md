# Thorlabs Optical Power Meter Qt Example

这是一个完整的Qt GUI应用程序，用于演示ThorlabsOpticalPowerMeter类的所有功能。

## 功能特性

### 设备连接控制
- **Power Meter Type**: 选择功率计类型（PM100X或PM101X）
- **Find Devices**: 搜索可用的设备
- **Serial Number**: 输入设备序列号
- **Connect/Disconnect**: 连接和断开设备
- **Connection Status**: 显示连接状态

### 测量设置控制
- **Power Range**: 设置功率测量范围（0.0001W - 1.0W）
- **Average Count**: 设置平均计数（1-10000）
- **Wave Length**: 设置波长（400-1100nm）

### 功率测量控制
- **Single Measurement**: 单次功率测量
- **Continuous Measurement**: 连续功率测量（100ms间隔）
- **Real-time Display**: 实时显示当前功率值

### 日志和错误控制
- **Error Message**: 获取错误信息
- **Log Index**: 设置和获取日志索引
- **Log Display**: 显示操作日志和时间戳

## ThorlabsOpticalPowerMeter函数测试

### 已实现的函数测试：

1. **findRsrc()** - 设备搜索
   - 按钮：Find Devices
   - 功能：搜索指定类型的功率计设备

2. **open()** - 设备连接
   - 按钮：Connect
   - 参数：序列号、功率范围、波长
   - 功能：连接到指定设备并初始化

3. **close()** - 设备断开
   - 按钮：Disconnect
   - 功能：断开设备连接

4. **measurePower()** - 功率测量
   - 按钮：Measure Power (Single) / Start Continuous
   - 功能：测量功率值并显示

5. **setPowerRange()** - 设置功率范围
   - 按钮：Set Power Range
   - 参数：功率范围值
   - 功能：设置测量功率范围

6. **setAvgCnt()** - 设置平均计数
   - 按钮：Set Average Count
   - 参数：平均计数值
   - 功能：设置测量平均次数

7. **getErrorMsg()** - 获取错误信息
   - 按钮：Get Error Message
   - 功能：获取最后的错误信息

8. **setLogIndex()** - 设置日志索引
   - 按钮：Set Log Index
   - 参数：日志索引值
   - 功能：设置日志索引

9. **getLogIndex()** - 获取日志索引
   - 按钮：Get Log Index
   - 功能：获取当前日志索引

## 编译和运行

### 前提条件
- Qt5开发环境
- CMake 3.10+
- ThorlabsOpticalPowerMeter库

### 编译步骤
```bash
# 在项目根目录下
mkdir build
cd build
cmake .. -DBUILD_POWER_METER_QT_EXAMPLE=ON
make qt_power_meter_example
```

### 运行
```bash
./example/qt_example/qt_power_meter_example
```

## 使用说明

1. **启动应用程序**：运行编译后的可执行文件

2. **选择设备类型**：在下拉菜单中选择PM100X或PM101X

3. **搜索设备**：点击"Find Devices"按钮搜索可用设备

4. **输入序列号**：在文本框中输入设备序列号

5. **连接设备**：点击"Connect"按钮连接设备

6. **配置参数**：
   - 设置功率范围
   - 设置平均计数
   - 设置波长（在连接时使用）

7. **测量功率**：
   - 单次测量：点击"Measure Power (Single)"
   - 连续测量：点击"Start Continuous"开始，"Stop Continuous"停止

8. **查看日志**：在底部文本区域查看操作日志和错误信息

## 界面布局

```
┌─────────────────────────────────────────────────────────┐
│                Device Connection                        │
│ Power Meter Type: [PM101X ▼] [Find Devices]           │
│ Serial Number: [M01059212        ] [Connect]           │
│ Status: Connected              [Disconnect]             │
└─────────────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────────────┐
│                Measurement Settings                     │
│ Power Range (W): [0.0200] [Set Power Range]           │
│ Average Count:   [500   ] [Set Average Count]         │
│ Wave Length (nm):[905.0 ] [Set Wave Length]           │
└─────────────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────────────┐
│                Power Measurement                        │
│ Current Power: 15.234 mW                               │
│ [Measure Power] [Start Continuous] [Stop Continuous]   │
└─────────────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────────────┐
│                Log and Error Controls                   │
│ [Get Error] Log Index:[0] [Set Log Index] [Get Index] │
└─────────────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────────────┐
│                    Log Output                           │
│ [12:34:56] Device search completed...                  │
│ [12:35:01] Successfully connected to device: M01059212 │
│ [12:35:05] Single measurement: 15.234 mW              │
└─────────────────────────────────────────────────────────┘
```

## 注意事项

1. 确保设备已正确连接到计算机
2. 在Linux系统上，可能需要适当的USB权限
3. 波长设置在连接时生效，连接后修改需要重新连接
4. 连续测量会以100ms间隔更新显示
5. 所有操作都会在日志区域显示结果和时间戳
