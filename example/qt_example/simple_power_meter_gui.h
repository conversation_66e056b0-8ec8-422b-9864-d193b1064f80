/******************************************************************************
 * Copyright 2021 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#ifndef SIMPLE_POWER_METER_GUI_H
#define SIMPLE_POWER_METER_GUI_H

#include <memory>

// Forward declarations
class QWidget;
class QLineEdit;
class QPushButton;
class QLabel;
class QTextEdit;
class QTimer;
class QVBoxLayout;
class QHBoxLayout;
class QGridLayout;
class QGroupBox;
class QDoubleSpinBox;
class QSpinBox;

namespace robosense {
namespace lidar {
class ThorlabsOpticalPowerMeter;
}
}

class SimplePowerMeterGui
{
public:
    SimplePowerMeterGui();
    ~SimplePowerMeterGui();
    
    void show();

private:
    void setupUserInterface();
    void connectSignals();
    void onConnectClicked();
    void onDisconnectClicked();
    void onFindDevicesClicked();
    void onSetPowerRangeClicked();
    void onSetAvgCountClicked();
    void onStartMeasurementClicked();
    void onStopMeasurementClicked();
    void onMeasurementTimer();
    void updateConnectionStatus(bool _connected);
    void logMessage(const char* _message);

    // UI components
    QWidget* main_widget_;
    QLineEdit* serial_number_edit_;
    QDoubleSpinBox* power_range_spinbox_;
    QSpinBox* avg_count_spinbox_;
    QPushButton* connect_button_;
    QPushButton* disconnect_button_;
    QPushButton* find_devices_button_;
    QPushButton* set_power_range_button_;
    QPushButton* set_avg_count_button_;
    QPushButton* start_measurement_button_;
    QPushButton* stop_measurement_button_;
    QLabel* connection_status_label_;
    QLabel* current_power_label_;
    QTextEdit* log_text_edit_;
    
    // Power meter and measurement
    std::unique_ptr<robosense::lidar::ThorlabsOpticalPowerMeter> power_meter_;
    QTimer* measurement_timer_;
    bool is_connected_;
    bool is_measuring_;
    int measurement_count_;
    
    static constexpr int MEASUREMENT_INTERVAL_MS = 100;
};

#endif // SIMPLE_POWER_METER_GUI_H
