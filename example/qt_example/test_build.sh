#!/bin/bash

# Test script for Qt Power Meter Example
# This script tests if the application can be built and basic functionality works

echo "=== Thorlabs Optical Power Meter Qt Example Build Test ==="
echo

# Check if we're in the right directory
if [ ! -f "../../CMakeLists.txt" ]; then
    echo "Error: Please run this script from the example/qt_example directory"
    exit 1
fi

# Create build directory
echo "1. Creating build directory..."
mkdir -p ../../build_qt_test
cd ../../build_qt_test

# Configure with CMake
echo "2. Configuring with CMake..."
cmake .. -DBUILD_POWER_METER_QT_EXAMPLE=ON
if [ $? -ne 0 ]; then
    echo "Error: CMake configuration failed"
    exit 1
fi

# Build the project
echo "3. Building the project..."
make qt_power_meter_example
if [ $? -ne 0 ]; then
    echo "Error: Build failed"
    exit 1
fi

# Check if executable exists
if [ ! -f "example/qt_example/qt_power_meter_example" ]; then
    echo "Error: Executable not found"
    exit 1
fi

echo "4. Build successful!"
echo "   Executable: $(pwd)/example/qt_example/qt_power_meter_example"
echo "   Size: $(du -h example/qt_example/qt_power_meter_example | cut -f1)"

# Test basic functionality (without GUI)
echo "5. Testing basic functionality..."
echo "   Note: GUI testing requires X11 display"

# Show help or version (if available)
echo "6. Application info:"
echo "   File type: $(file example/qt_example/qt_power_meter_example)"
echo "   Dependencies:"
ldd example/qt_example/qt_power_meter_example | grep -E "(Qt5|libstdc|libc\.so)" | head -5

echo
echo "=== Build Test Completed Successfully ==="
echo "To run the application:"
echo "  cd $(pwd)"
echo "  ./example/qt_example/qt_power_meter_example"
echo
echo "Note: Make sure you have:"
echo "  - X11 display available (for GUI)"
echo "  - Thorlabs power meter connected"
echo "  - Appropriate USB permissions"
