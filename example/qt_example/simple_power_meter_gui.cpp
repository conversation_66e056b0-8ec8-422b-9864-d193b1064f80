/******************************************************************************
 * Copyright 2021 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/

#include "simple_power_meter_gui.h"
#include "rsfsc_log/rsfsc_log.h"
#include "thorlabs_optical_power_meter.h"

#include <QtCore/QString>
#include <QtCore/QTime>
#include <QtCore/QTimer>
#include <QtWidgets/QApplication>
#include <QtWidgets/QDoubleSpinBox>
#include <QtWidgets/QGridLayout>
#include <QtWidgets/QGroupBox>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QLabel>
#include <QtWidgets/QLineEdit>
#include <QtWidgets/QMessageBox>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QSpinBox>
#include <QtWidgets/QTextEdit>
#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QWidget>
#include <cmath>

SimplePowerMeterGui::SimplePowerMeterGui() :
  main_widget_(nullptr),
  power_meter_(nullptr),
  measurement_timer_(nullptr),
  is_connected_(false),
  is_measuring_(false),
  measurement_count_(0)
{
  setupUserInterface();
  connectSignals();

  // Initialize power meter
  try
  {
    power_meter_ = std::make_unique<robosense::lidar::ThorlabsOpticalPowerMeter>(
      robosense::lidar::ThorlabsOpticalPowerMeter::PowerMeterType::PM101X);
    logMessage("Power meter driver initialized successfully");
  }
  catch (const std::exception& exception)
  {
    logMessage("Failed to initialize power meter driver");
  }
}

SimplePowerMeterGui::~SimplePowerMeterGui()
{
  if (is_measuring_)
  {
    onStopMeasurementClicked();
  }
  if (is_connected_)
  {
    onDisconnectClicked();
  }
  delete main_widget_;
}

void SimplePowerMeterGui::show()
{
  if (main_widget_)
  {
    main_widget_->show();
  }
}

void SimplePowerMeterGui::setupUserInterface()
{
  main_widget_ = new QWidget();
  main_widget_->setWindowTitle("Thorlabs Optical Power Meter Control");
  main_widget_->resize(800, 600);

  auto* main_layout = new QVBoxLayout(main_widget_);

  // Connection group
  auto* connection_group  = new QGroupBox("Device Connection");
  auto* connection_layout = new QGridLayout(connection_group);

  connection_layout->addWidget(new QLabel("Serial Number:"), 0, 0);
  serial_number_edit_ = new QLineEdit();
  serial_number_edit_->setPlaceholderText("Enter device serial number (e.g., *********)");
  connection_layout->addWidget(serial_number_edit_, 0, 1);

  find_devices_button_ = new QPushButton("Find Devices");
  connection_layout->addWidget(find_devices_button_, 0, 2);

  connection_status_label_ = new QLabel("Status: Disconnected");
  connection_layout->addWidget(connection_status_label_, 1, 0);

  connect_button_ = new QPushButton("Connect");
  connection_layout->addWidget(connect_button_, 1, 1);

  disconnect_button_ = new QPushButton("Disconnect");
  disconnect_button_->setEnabled(false);
  connection_layout->addWidget(disconnect_button_, 1, 2);

  main_layout->addWidget(connection_group);

  // Settings group
  auto* settings_group  = new QGroupBox("Measurement Settings");
  auto* settings_layout = new QGridLayout(settings_group);

  settings_layout->addWidget(new QLabel("Power Range (W):"), 0, 0);
  power_range_spinbox_ = new QDoubleSpinBox();
  power_range_spinbox_->setDecimals(4);
  power_range_spinbox_->setRange(0.0001, 1.0);
  power_range_spinbox_->setValue(0.02);
  settings_layout->addWidget(power_range_spinbox_, 0, 1);

  set_power_range_button_ = new QPushButton("Set Power Range");
  set_power_range_button_->setEnabled(false);
  settings_layout->addWidget(set_power_range_button_, 0, 2);

  settings_layout->addWidget(new QLabel("Average Count:"), 1, 0);
  avg_count_spinbox_ = new QSpinBox();
  avg_count_spinbox_->setRange(1, 10000);
  avg_count_spinbox_->setValue(500);
  settings_layout->addWidget(avg_count_spinbox_, 1, 1);

  set_avg_count_button_ = new QPushButton("Set Average Count");
  set_avg_count_button_->setEnabled(false);
  settings_layout->addWidget(set_avg_count_button_, 1, 2);

  main_layout->addWidget(settings_group);

  // Measurement group
  auto* measurement_group  = new QGroupBox("Power Measurement");
  auto* measurement_layout = new QVBoxLayout(measurement_group);

  auto* measurement_control_layout = new QHBoxLayout();
  current_power_label_             = new QLabel("Current Power: -- mW");
  current_power_label_->setStyleSheet("font-size: 14px; font-weight: bold;");
  measurement_control_layout->addWidget(current_power_label_);
  measurement_control_layout->addStretch();

  start_measurement_button_ = new QPushButton("Start Measurement");
  start_measurement_button_->setEnabled(false);
  measurement_control_layout->addWidget(start_measurement_button_);

  stop_measurement_button_ = new QPushButton("Stop Measurement");
  stop_measurement_button_->setEnabled(false);
  measurement_control_layout->addWidget(stop_measurement_button_);

  measurement_layout->addLayout(measurement_control_layout);
  main_layout->addWidget(measurement_group);

  // Log text edit
  log_text_edit_ = new QTextEdit();
  log_text_edit_->setMaximumHeight(150);
  log_text_edit_->setReadOnly(true);
  main_layout->addWidget(log_text_edit_);

  // Create timer
  measurement_timer_ = new QTimer();
  measurement_timer_->setInterval(MEASUREMENT_INTERVAL_MS);
}

void SimplePowerMeterGui::connectSignals()
{
  // Connect button signals using function pointers
  QObject::connect(find_devices_button_, &QPushButton::clicked, [this]() { onFindDevicesClicked(); });
  QObject::connect(connect_button_, &QPushButton::clicked, [this]() { onConnectClicked(); });
  QObject::connect(disconnect_button_, &QPushButton::clicked, [this]() { onDisconnectClicked(); });
  QObject::connect(set_power_range_button_, &QPushButton::clicked, [this]() { onSetPowerRangeClicked(); });
  QObject::connect(set_avg_count_button_, &QPushButton::clicked, [this]() { onSetAvgCountClicked(); });
  QObject::connect(start_measurement_button_, &QPushButton::clicked, [this]() { onStartMeasurementClicked(); });
  QObject::connect(stop_measurement_button_, &QPushButton::clicked, [this]() { onStopMeasurementClicked(); });
  QObject::connect(measurement_timer_, &QTimer::timeout, [this]() { onMeasurementTimer(); });
}

void SimplePowerMeterGui::onFindDevicesClicked()
{
  if (!power_meter_)
  {
    logMessage("Power meter driver not initialized");
    return;
  }

  logMessage("Searching for devices...");
  QApplication::processEvents();

  bool found_devices = robosense::lidar::ThorlabsOpticalPowerMeter::findRsrc(
    robosense::lidar::ThorlabsOpticalPowerMeter::PowerMeterType::PM101X);

  if (found_devices)
  {
    logMessage("Device search completed. Check console output for available devices.");
  }
  else
  {
    logMessage("No devices found or search failed.");
  }
}

void SimplePowerMeterGui::onConnectClicked()
{
  if (!power_meter_)
  {
    logMessage("Power meter driver not initialized");
    return;
  }

  QString serial_number = serial_number_edit_->text().trimmed();
  if (serial_number.isEmpty())
  {
    QMessageBox::warning(main_widget_, "Warning", "Please enter a serial number");
    return;
  }

  logMessage("Connecting to device...");
  QApplication::processEvents();

  double power_range = power_range_spinbox_->value();
  bool success       = power_meter_->open(serial_number.toStdString(), power_range);

  if (success)
  {
    is_connected_ = true;
    updateConnectionStatus(true);
    logMessage("Successfully connected to device");

    // Set initial average count
    onSetAvgCountClicked();
  }
  else
  {
    logMessage("Failed to connect to device");
  }
}

void SimplePowerMeterGui::onDisconnectClicked()
{
  if (!power_meter_ || !is_connected_)
  {
    return;
  }

  // Stop measurement if running
  if (is_measuring_)
  {
    onStopMeasurementClicked();
  }

  logMessage("Disconnecting from device...");
  QApplication::processEvents();

  bool success  = power_meter_->close();
  is_connected_ = false;
  updateConnectionStatus(false);

  if (success)
  {
    logMessage("Successfully disconnected from device");
  }
  else
  {
    logMessage("Disconnect warning occurred");
  }
}

void SimplePowerMeterGui::onSetPowerRangeClicked()
{
  if (!power_meter_ || !is_connected_)
  {
    logMessage("Device not connected");
    return;
  }

  double power_range = power_range_spinbox_->value();
  logMessage("Setting power range...");
  QApplication::processEvents();

  bool success = power_meter_->setPowerRange(power_range);

  if (success)
  {
    logMessage("Power range set successfully");
  }
  else
  {
    logMessage("Failed to set power range");
  }
}

void SimplePowerMeterGui::onSetAvgCountClicked()
{
  if (!power_meter_ || !is_connected_)
  {
    logMessage("Device not connected");
    return;
  }

  int avg_count = avg_count_spinbox_->value();
  logMessage("Setting average count...");
  QApplication::processEvents();

  bool success = power_meter_->setAvgCnt(avg_count);

  if (success)
  {
    logMessage("Average count set successfully");
  }
  else
  {
    logMessage("Failed to set average count");
  }
}

void SimplePowerMeterGui::onStartMeasurementClicked()
{
  if (!power_meter_ || !is_connected_)
  {
    logMessage("Device not connected");
    return;
  }

  is_measuring_      = true;
  measurement_count_ = 0;
  measurement_timer_->start();

  start_measurement_button_->setEnabled(false);
  stop_measurement_button_->setEnabled(true);

  logMessage("Started continuous measurement");
}

void SimplePowerMeterGui::onStopMeasurementClicked()
{
  if (!is_measuring_)
  {
    return;
  }

  is_measuring_ = false;
  measurement_timer_->stop();

  start_measurement_button_->setEnabled(true);
  stop_measurement_button_->setEnabled(false);

  logMessage("Stopped measurement");
}

void SimplePowerMeterGui::onMeasurementTimer()
{
  if (!power_meter_ || !is_connected_ || !is_measuring_)
  {
    return;
  }

  double power = NAN;
  bool success = power_meter_->measurePower(power);

  if (success)
  {
    double power_mw = power * 1000.0;
    current_power_label_->setText(QString("Current Power: %1 mW").arg(power_mw, 0, 'f', 3));
    measurement_count_++;

    if (measurement_count_ % 10 == 0)
    {  // Log every 10 measurements
      logMessage(
        QString("Measurement #%1: %2 mW").arg(measurement_count_).arg(power_mw, 0, 'f', 3).toLocal8Bit().constData());
    }
  }
  else
  {
    logMessage("Failed to measure power");
  }
}

void SimplePowerMeterGui::updateConnectionStatus(bool _connected)
{
  if (_connected)
  {
    connection_status_label_->setText("Status: Connected");
    connection_status_label_->setStyleSheet("color: green; font-weight: bold;");

    connect_button_->setEnabled(false);
    disconnect_button_->setEnabled(true);
    set_power_range_button_->setEnabled(true);
    set_avg_count_button_->setEnabled(true);
    start_measurement_button_->setEnabled(true);
  }
  else
  {
    connection_status_label_->setText("Status: Disconnected");
    connection_status_label_->setStyleSheet("color: red; font-weight: bold;");

    connect_button_->setEnabled(true);
    disconnect_button_->setEnabled(false);
    set_power_range_button_->setEnabled(false);
    set_avg_count_button_->setEnabled(false);
    start_measurement_button_->setEnabled(false);
    stop_measurement_button_->setEnabled(false);

    current_power_label_->setText("Current Power: -- mW");
  }
}

void SimplePowerMeterGui::logMessage(const char* _message)
{
  if (log_text_edit_)
  {
    log_text_edit_->append(QString("[%1] %2").arg(QTime::currentTime().toString()).arg(_message));
    log_text_edit_->ensureCursorVisible();
  }
}
