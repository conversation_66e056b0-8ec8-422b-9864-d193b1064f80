﻿/******************************************************************************
 * Copyright 2022 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
#include "power_meter_pm100x.h"

#include "rsfsc_log/rsfsc_log_macro.h"
#include <array>
#include <string>

namespace robosense
{
namespace lidar
{

PowerMeterPM100X::~PowerMeterPM100X() { close(); }

bool PowerMeterPM100X::open(const std::string& _sn, const double _power_range, const double _wave_length)
{
  std::unique_lock<std::mutex> lck(getMtx());  //查找多个OPM设备时，多线程确保线程安全
  getError() = std::string();
  if (session_ != VI_NULL)
  {
    getError() = std::string("This object already connect an OPM");
    return false;
  }
  // char instrument_descriptor[TLPM_BUFFER_SIZE];
  std::array<ViChar, BUFFER_SIZE> resource_name { "" };
  ViBoolean is_found_sn = VI_FALSE;

  for (auto& iter : getAllRes())
  {
    LOG_INDEX_INFO("resource name = {}, sn = :{}", iter.data(), _sn);
    if (!_sn.empty() && (std::string(iter.data()).find(_sn) != std::string::npos))
    {
      resource_name = iter;
      is_found_sn   = VI_TRUE;
      LOG_INDEX_INFO("found, match sn : {}", _sn);
      break;  //found, match _sn
    }
  }

  if ((VI_FALSE == is_found_sn))
  {
    getError() = std::string("OPM not found or is not available");
    return false;
  }

  if (VI_SUCCESS != TLPM_init(resource_name.data(), VI_ON, VI_ON, &session_) ||
      VI_SUCCESS != TLPM_setWavelength(session_, _wave_length) ||
      VI_SUCCESS != TLPM_setPowerAutoRange(session_, VI_FALSE) ||
      VI_SUCCESS != TLPM_setPowerRange(session_, _power_range) ||
      VI_SUCCESS != TLPM_setPowerUnit(session_, TLPM_POWER_UNIT_WATT) || VI_SUCCESS != TLPM_setAvgCnt(session_, 100) ||
      VI_SUCCESS != TLPM_setDispBrightness(session_, 0.1))
  {
    getError() = std::string("OPM can not be init");
    return false;
  }
  ViInt16 self_test_result { 0 };
  std::array<ViChar, BUFFER_SIZE> self_test_description { "" };
  if (VI_SUCCESS != TLPM_selfTest(session_, &self_test_result, self_test_description.data()))
  {
    getError() = std::string("OPM self test failed, error description: ") + std::string(self_test_description.data());
    return false;
  }
  return true;
}

bool PowerMeterPM100X::close()
{
  if (session_ == VI_NULL)
  {
    getError() = std::string("No OPM to close");
    return true;
  }
  if (VI_SUCCESS != TLPM_close(session_))
  {
    getError() = std::string("Failed to close OPM");
    return false;
  }
  session_ = VI_NULL;
  return true;
}

bool PowerMeterPM100X::measurePower(double& _power)
{
  std::unique_lock<std::mutex> lck(getMtx());
  if (session_ == VI_NULL)
  {
    getError() = std::string("OPM not open");
    return false;
  }

  //NOTE:以下注销的代码线程不安全,这里去掉这部分代码, 理论是session_存在即可进行功率读取
  // ViPUInt32 count = new ViUInt32;
  // TLPM_findRsrc(session_, count);
  // if (*count < 1)
  // {
  //   delete count;
  //   return false;
  // }
  // delete count;
  ViReal64 data { std::numeric_limits<double>::quiet_NaN() };
  try
  {
    if (VI_SUCCESS != TLPM_measPower(session_, &data))
    {
      getError() = std::string("Measure Failed");
      return false;
    }
  }
  catch (const std::exception&)
  {
    getError() = std::string("OPM was plug out");
    return false;
  }

  _power = data;
  return true;
}

bool PowerMeterPM100X::setAvgCnt(int _cnt)
{
  std::unique_lock<std::mutex> lck(getMtx());
  if (session_ == VI_NULL)
  {
    getError() = std::string("OPM not open");
    return false;
  }

  try
  {
    if (VI_SUCCESS != TLPM_setAvgCnt(session_, static_cast<ViInt16>(_cnt)))
    {
      getError() = std::string("Set average time Failed");
      return false;
    }
  }
  catch (const std::exception&)
  {
    getError() = std::string("OPM was plug out");
    return false;
  }
  return true;
}

bool PowerMeterPM100X::setPowerRange(const double _power_range)
{
  std::unique_lock<std::mutex> lck(getMtx());
  if (session_ == VI_NULL)
  {
    getError() = std::string("OPM not open");
    return false;
  }

  try
  {
    if (VI_SUCCESS != TLPM_setPowerRange(session_, _power_range))
    {
      getError() = std::string("Set power range(") + std::to_string(_power_range) + (") Failed");
      return false;
    }
  }
  catch (const std::exception&)
  {
    getError() = std::string("OPM was plug out");
    return false;
  }
  return true;
}

}  // namespace lidar
}  // namespace robosense