/******************************************************************************
 * Copyright 2022 RoboSense All rights reserved.
 * Suteng Innovation Technology Co., Ltd. www.robosense.ai

 * This software is provided to you directly by RoboSense and might
 * only be used to access RoboSense LiDAR. Any compilation,
 * modification, exploration, reproduction and redistribution are
 * restricted without RoboSense's prior consent.

 * THIS SOFTWARE IS PROVIDED "AS IS" AND ANY EXPRESSED OR IMPLIED
 * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
 * OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL ROBOSENSE BE LIABLE FOR ANY DIRECT,
 * INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 * (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION)
 * HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT,
 * STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING
 * IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *****************************************************************************/
/**
 * @file   thorlabs_optical_power_meter.h
 * <AUTHOR> Chen (<EMAIL>), Vance Huang(<EMAIL>)
 * @brief
 * @version 1.2.0
 * @date 2024-06-19
 *
 * Copyright (c) 2022, RoboSense Co.,Ltd. - www.robosense.ai
 * All Rights Reserved.
 *
 * If you find any BUG or improvement, please contact the authors, so we can share your idea
 *
*/
#ifndef THORLABS_OPTICAL_POWER_METER_H
#define THORLABS_OPTICAL_POWER_METER_H

#include <memory>
#include <string>

namespace robosense
{
namespace lidar
{
class ThorlabsOpticalPowerMeter
{
public:
  enum class PowerMeterType : uint8_t
  {
    PM100X,
    PM101X
  };
  explicit ThorlabsOpticalPowerMeter(const PowerMeterType _type);
  ThorlabsOpticalPowerMeter(ThorlabsOpticalPowerMeter&&)      = delete;
  ThorlabsOpticalPowerMeter(const ThorlabsOpticalPowerMeter&) = delete;
  ThorlabsOpticalPowerMeter& operator=(ThorlabsOpticalPowerMeter&&) = delete;
  ThorlabsOpticalPowerMeter& operator=(const ThorlabsOpticalPowerMeter&) = delete;
  ~ThorlabsOpticalPowerMeter();

  static bool findRsrc(const PowerMeterType _type);
  /**
   * @brief       find the thorlabs optical power meter device which match the giving sn,
   *              the function will make initialize which some fixed params and giving param like power range
   * @param[in]   _sn           the input sn which will be matched
   * @param[in]   _power_range  the power range, it will be set after init as default power range, default(20mW)
   * @return      returns true if executes pass, return false get failed, call getErrorMsg to get error msg
   */
  bool open(const std::string& _sn, const double _power_range = 0.02, const double _wave_length = 905.0);

  /**
   * @brief       close current thorlabs optical power meter device
   * @return      returns true if executes pass, return false get failed, call getErrorMsg to get error msg
   */
  bool close();

  /**
   * @brief       measure power value
   * @param[in]   _power        the power value you want to measure
   * @return      returns true if executes pass, return false get failed, call getErrorMsg to get error msg
   */
  bool measurePower(double& _power);

  /**
   * @brief       set the thorlabs optical power meter average count, it use _cnt = 500 usually
   * @param[in]   _cnt          average count value
   * @return      returns true if executes pass, return false get failed, call getErrorMsg to get error msg
   */
  bool setAvgCnt(const int _cnt);

  /**
   * @brief       manual changes to measure power range, you can set power range close to
   *              the value you want to measure
   *              e.g. when measure value about 60mW, set _power_range = 0.08(80mW) before measurePower
   * @param[in]   _power_range  the power range, it will be set after init as default power range
   * @return      returns true if executes pass, return false get failed, call getErrorMsg to get error msg
   */
  bool setPowerRange(const double _power_range);

  /**
   * @brief       the error message info
   * @return      returns true if executes pass, return false get failed, call getErrorMsg to get error msg
   */
  [[nodiscard]] std::string getErrorMsg() const;

  void setLogIndex(const int _index);
  [[nodiscard]] int getLogIndex() const;

private:
  std::shared_ptr<void> power_meter_ { nullptr };
};
}  // namespace lidar
}  // namespace robosense

#endif  // THORLABS_OPTICAL_POWER_METER_H
