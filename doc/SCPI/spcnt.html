<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <style>
body,html { 
  margin: 0;
  font-family: Arial, Helvetica, sans-serif;
  font-size: 14px;
  box-sizing: border-box;
}

dev-name, scpi-list {
    display: none;
}

p {
  margin-left: 5px;
}

.codeSample {
  margin-left: 5px;
  font-family: Consolas,"courier new";
}

img {
  margin-left: 5px;
}

@media print {
   .pageBody {
    overflow-y: auto !important;
    height: auto !important;
  }

  .content {
    overflow: auto !important;
    max-height: initial !important;
    transition: none !important;
  }

  .noSplit {
    break-inside: avoid;
  }
  
  .header-right {
    display: none !important;
  }
 
  .content .scpiLong {
    font-size: 12px !important;
  }
  
  .collapsible {
    font-weight: bold !important;
    color-adjust: exact!important;  
    -webkit-print-color-adjust: exact!important; 
    print-color-adjust: exact!important;
  }
  
  rect {
    color-adjust: exact!important;  
    -webkit-print-color-adjust: exact!important; 
    print-color-adjust: exact!important;
  }
  
  svg {
    width: 95% !important;
  }
}

a[href^="http"]:after {
     content: " " url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAoAAAAKCAYAAACNMs+9AAAAVklEQVR4Xn3PgQkAMQhDUXfqTu7kTtkpd5RA8AInfArtQ2iRXFWT2QedAfttj2FsPIOE1eCOlEuoWWjgzYaB/IkeGOrxXhqB+uA9Bfcm0lAZuh+YIeAD+cAqSz4kCMUAAAAASUVORK5CYII=);
}

svg {
  margin-left: 5px;
}

.verInfo{
  float:right;
  text-align: right;
  width: 300px;
  margin-right: 5px;
}

.header {
  overflow: hidden;
  background-color: #f1f1f1;
  padding: 5px 5px;
  left: 0;
  top: 0;
}

.pageBody {
  overflow-y: scroll; 
  overflow-x: hidden; 
  height: calc(100vh - 50px);
}

.header a {
  float: left;
  color: black;
  text-align: center;
  padding: 8px;
  text-decoration: none;
  font-size: 18px; 
  line-height: 22px;
  border-radius: 4px;
}

.header span {
  padding: 0px;
  font-size: 25px;
  font-weight: bold;
}

.header a:hover {
  background-color: #ddd;
  color: black;
}

.header-right {
  float: right;
}

.tooltip {
  display: inline-block;
  border-bottom: 1px dotted black;
  cursor: help;
}

.collapsible {
  background-color: #777;
  background-image: linear-gradient(#777, #555);
  color: white;
  cursor: pointer;
  padding: 5px;
  margin: 0px;
  margin-top: 2px;
  width: 100%;
  border: none;
  text-align: left;
  outline: none;
  font-size: 12px;
}

.collapsible .scpi {
  float:left;
  font-size: 14px;
  width: 280px;
}

.collapsible .briefScpi {
  margin-left: 10px;
  font-size: 14px;
}

.active, .collapsible:hover {
  background-color: #555;
  background-image: linear-gradient(#555, #777);
}

.content {
  padding: 0;
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.2s ease-out;
  background-color: #f1f1f1;
  z-index: 0;
}

.content ul { 
  position: relative;
  list-style: none;
  padding: 0px;
  margin: 0px;
  margin-top: 5px;
  margin-bottom: 10px;
  font-weight: normal;
  font-size: 14px;
}

.content li {
  margin: 0;
  margin-top: 4px;
  margin-bottom: 4px;
}

.content .scpiLong {
  width: 99%;
  margin: 10px 5px 10px 5px;
  font-size: 18px;
}

.details{
  padding: 7px 5px;
  margin: 4px;
  font-size: 14px;
}

.detailListing {
  margin:  5px;
}

.detailListing ul li{
  list-style-type: circle;
  margin-left: 20px;
}

span.param , span.optParam {
  float: left;
  width: 100px;
}

span.optParam {
  font-style: italic;
}

span.paramType {
  float: left;
  font-style: italic;
  width: 140px;
}

li.highlightParam {
  font-weight: bold;
}

.info {
  background-color: #c7ffd6;
  border-left: 6px solid #389c53;
  padding: 7px 5px;
  margin: 4px;
  background-image: linear-gradient(to right, #c7ffd6, #f1f1f1);
  font-size: 14px;
}

.note {
  background-color: #ffdddd;
  border-left: 6px solid #DE1616;
  padding: 7px 5px;
  margin: 4px;
  background-image: linear-gradient(to right, #ffdddd, #f1f1f1);
  font-size: 14px;
}

.example {
  background-color: #E0E6F8;
  border-left: 6px solid #075C91;
  padding: 7px 5px;
  margin: 4px;
  font-family: monospace;
  background-image: linear-gradient(to right, #E0E6F8, #f1f1f1);
  font-size: 14px;
}

.sufixDocu, .paramDocu, .resultDocu {
  padding-left: 7px;
  font-size: 10px;
  font-weight: bold;
}

.resultDocu{
  padding-left: 7px;
  font-size: 10px;
  font-weight: bold;
}

span.scpiCmd {
  font-family: monospace;
}

div.scpiSpacer {
   display: inline-block;
   width: 180px;
  font-weight: bold;
}
    </style>
    <title>SPCNT SCPI Commands</title>
    <link rel="icon" type="image/png" sizes="16x16" href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAIAAAACACAYAAADDPmHLAAABcklEQVR4nO3cMQ7DIBAAwSPK/79MnkBlEWtnWiiQtbrCxa2Z2UPW5/YDuEsAcQKIE0CcAOIEECeAuO/pgp8E77YO5yZAnADiBBAngDgBxAkgTgBxAogTQJwA4gQQJ4A4AcQJIE4AcQKIE0CcAOIEECeAOAHECSBOAHECiBNAnADiBBAngDgBxAkgTgBxAogTQJwA4gQQJ4A4AcQJIE4Accc9gbP3adXcs9Z696rCP/9+JkCcAOIEECeAOAHECSBOAHECiBNAnADiBBAngDgBxAkgTgBxAogTQJwA4gQQJ4A4AcQJIE4AcQKIE0CcAOIEECeAOAHECSBOAHECiBNAnADiBBAngDgBxAkgTgBxAogTQJwA4gQQJ4A4AcQJIE4AcQKIE0CcAOIEECeAOAHECSBOAHECiBNAnADiBBAngDgBxAkgTgBxAogTQJwA4gQQJ4A4AcQJIE4AcWtm9u1HcI8JECeAOAHECSBOAHECiBNA3A/BiAoBsMLaFQAAAABJRU5ErkJggg==" />
    <dev-name>SPCNT</dev-name>
    <scpi-list>
		<scpi-cmd cmd-str="*CLS">Clears all SCPI event status registers</scpi-cmd>
		<scpi-cmd cmd-str="*ESE <bitmask>">Programs the 8 bit wide Standard Event Enable Register</scpi-cmd>
		<scpi-cmd cmd-str="*ESE?">Reads the 8 bit wide Standard Event Enable Register</scpi-cmd>
		<scpi-cmd cmd-str="*ESR?">Reads and clears the Standard Event Register</scpi-cmd>
		<scpi-cmd cmd-str="*IDN?">Reads the device identification string</scpi-cmd>
		<scpi-cmd cmd-str="*LST? <subsyst>">Lists supported SCPI commands</scpi-cmd>
		<scpi-cmd cmd-str="MEASure[:SCALar][:COUNts]?">Returns recently measured absolute or relative counts</scpi-cmd>
		<scpi-cmd cmd-str="*OPC">Sets the Operation Complete bit in the Standard Event Register</scpi-cmd>
		<scpi-cmd cmd-str="*OPC?">Operation complete query</scpi-cmd>
		<scpi-cmd cmd-str="*RST">Resets all SCPI and sensor parameters to default</scpi-cmd>
		<scpi-cmd cmd-str="*SRE <bitmask>">Service request enable command</scpi-cmd>
		<scpi-cmd cmd-str="*SRE?">Service request enable query</scpi-cmd>
		<scpi-cmd cmd-str="*STB?">Status byte query</scpi-cmd>
		<scpi-cmd cmd-str="*TST?">Self-test query</scpi-cmd>
		<scpi-cmd cmd-str="*WAI">Wait-to-continue command</scpi-cmd>
		<scpi-cmd cmd-str="CALibration:STRing?">Queries factory calibration string</scpi-cmd>
		<scpi-cmd cmd-str="DISPlay:BRIGhtness <brightness>">Sets display brightness in percent</scpi-cmd>
		<scpi-cmd cmd-str="DISPlay:BRIGhtness?">Queries display brightness in percent</scpi-cmd>
		<scpi-cmd cmd-str="MEASure:ARRay:COUNt?">Returns a list of bin counts as binary data</scpi-cmd>
		<scpi-cmd cmd-str="MEASure[:SCALar]:FREQuency?">Returns recently measured absolute or relative frequency in Hz</scpi-cmd>
		<scpi-cmd cmd-str="MEASure:TIME?">Returns the width of the last bin in Seconds</scpi-cmd>
		<scpi-cmd cmd-str="SENSe:CORRection:COLLect:ZERO[:INITiate]">Starts zeroing of counter</scpi-cmd>
		<scpi-cmd cmd-str="SENSe:CORRection:COLLect:ZERO:ABORt">Aborts previously started zeroing procedure</scpi-cmd>
		<scpi-cmd cmd-str="SENSe:CORRection:COLLect:ZERO:MAGNitude <zero>">Sets dark counts to a given value</scpi-cmd>
		<scpi-cmd cmd-str="SENSe:CORRection:COLLect:ZERO:MAGNitude?">Returns value of zero offset in counts</scpi-cmd>
		<scpi-cmd cmd-str="SENSe:CORRection:COLLect:ZERO:STATe?">Tests if zeroing procedure is running</scpi-cmd>
		<scpi-cmd cmd-str="SENSe:COUNt:ARRay:NPOInts <length>">Sets the amount of bins stored in array</scpi-cmd>
		<scpi-cmd cmd-str="SENSe:COUNt:ARRay:NPOInts?">Queries the amount of bins stored in array</scpi-cmd>
		<scpi-cmd cmd-str="SENSe:COUNt:GATE:APERture <time>">Sets the width of a single bin</scpi-cmd>
		<scpi-cmd cmd-str="SENSe:COUNt:GATE:APERture?">Queries the width of a single bin</scpi-cmd>
		<scpi-cmd cmd-str="SENSe:COUNt:GATE:DELAy <time>">Sets counter dead time</scpi-cmd>
		<scpi-cmd cmd-str="SENSe:COUNt:GATE:DELAy?">Queries counter dead time</scpi-cmd>
		<scpi-cmd cmd-str="SENSe:FREQuency:COLLect[:INITiate]">Starts software defined frequency count bin</scpi-cmd>
		<scpi-cmd cmd-str="SENSe:FREQuency:COLLect:ABORt">Ends software defined frequency count bin</scpi-cmd>
		<scpi-cmd cmd-str="SENSe:FREQuency:COLLect:STATe?">Tests if counter is currently collecting data</scpi-cmd>
		<scpi-cmd cmd-str="SENSe:FREQuency:THReshold  <offset>">Sets the counter input threshold</scpi-cmd>
		<scpi-cmd cmd-str="SENSe:FREQuency:THReshold?">Queries the counter input threshold</scpi-cmd>
		<scpi-cmd cmd-str="STATus:OPERation[:EVENt]?">Queries the SCPI Operation Event register</scpi-cmd>
		<scpi-cmd cmd-str="STATus:OPERation:CONDition?">Queries the SCPI Operation Condition register</scpi-cmd>
		<scpi-cmd cmd-str="STATus:OPERation:ENABle <bitmask>">Sets the SCPI Operation Enable register</scpi-cmd>
		<scpi-cmd cmd-str="STATus:OPERation:ENABle?">Queries the SCPI Operation Enable register</scpi-cmd>
		<scpi-cmd cmd-str="STATus:OPERation:NTRansition <bitmask>">Sets the SCPI Operation Negative Transition register</scpi-cmd>
		<scpi-cmd cmd-str="STATus:OPERation:NTRansition?">Queries the SCPI Operation Negative Transition register</scpi-cmd>
		<scpi-cmd cmd-str="STATus:OPERation:PTRansition <bitmask>">Sets the SCPI Operation Positive Transition register</scpi-cmd>
		<scpi-cmd cmd-str="STATus:OPERation:PTRansition?">Queries the SCPI Operation Positive Transition register</scpi-cmd>
		<scpi-cmd cmd-str="STATus:PRESet">Pre-sets all SCPI status register with default values</scpi-cmd>
		<scpi-cmd cmd-str="STATus:QUEStionable[:EVENt]?">Queries the SCPI Questionable Event register</scpi-cmd>
		<scpi-cmd cmd-str="STATus:QUEStionable:CONDition?">Queries the SCPI Questionable Condition register</scpi-cmd>
		<scpi-cmd cmd-str="STATus:QUEStionable:ENABle <bitmask>">Sets the SCPI Questionable Enable register</scpi-cmd>
		<scpi-cmd cmd-str="STATus:QUEStionable:ENABle?">Queries the SCPI Questionable Enable register</scpi-cmd>
		<scpi-cmd cmd-str="STATus:QUEStionable:NTRansition <bitmask>">Sets the SCPI Questionable Negative Transition register</scpi-cmd>
		<scpi-cmd cmd-str="STATus:QUEStionable:NTRansition?">Queries the SCPI Questionable Negative Transition register</scpi-cmd>
		<scpi-cmd cmd-str="STATus:QUEStionable:PTRansition <bitmask>">Queries the SCPI Questionable Positive Transition register</scpi-cmd>
		<scpi-cmd cmd-str="STATus:QUEStionable:PTRansition?">Queries the SCPI Questionable Positive Transition register</scpi-cmd>
		<scpi-cmd cmd-str="SYSTem:ERRor[:NEXT]?">Reads and removes oldest element of SCPI error queue</scpi-cmd>
		<scpi-cmd cmd-str="SYSTem:ERRor:COUNt?">Tests how many errors are stored in SCPI error queue</scpi-cmd>
		<scpi-cmd cmd-str="SYSTem:VERSion?">Returns SCPI version string</scpi-cmd>

    </scpi-list>
</head>
<body>
  <div class="header">
    <span>SPCNT SCPI Commands</span>
    <div class="header-right">
      <a href="#None">None</a>
      <a href="#CAL">CAL</a>
      <a href="#DISP">DISP</a>
      <a href="#MEAS">MEAS</a>
      <a href="#SENS">SENS</a>
      <a href="#STAT">STAT</a>
      <a href="#SYST">SYST</a>

    </div>
  </div>
  <div class="pageBody">
    <div id="SCPI_Desc">
      <div class="verInfo">V 1.0.0 generated 19 Jun 2023, 13:14:34</div>
      <p>This is the description of the <a target="_blank" href="https://www.thorlabs.com/">Thorlabs</a> SPCNT remote interface. This single photon counting device can be remotely controlled using the SCPI (Standard Commands for Programmable Instruments) interface.</p>
      <p>In 1975, the IEEE standardized a bus developed by Hewlett-Packard originally called HPIB (Hewlett-Packard Interface Bus), later changed to GPIB (General Purpose Interface Bus). 
        The standard was called IEEE 488 (IEEE 488.1) and it defined mechanical aspects of the bus. The later IEEE 488.2 defined its protocol properties. What was missing were set of 
        rules between the manufacturers on commands to control the instruments. Sometimes these varied even between different models from the same manufacturer.</p>
      <p>In 1990 SCPI Consortium released the first SCPI standard as an additional layer for the IEEE-488.2 standard.</p>
      <p>SCPI commands are ASCII strings, which are sent to instrument over the physical communication layer. They can perform:</p>
      <ul>
        <li> Set operations, for example the <span class="scpiCmd">*RST</span> command (resetting the instrument).</li>
        <li> Query operations, for example the <span class="scpiCmd">*IDN?</span> query (querying the instrument’s identification string).</li>
      </ul>
      <p>Some SCPI commands exist as both set commands and query commands. An example is an SPCNT's number of bins command <span class="scpiCmd">SENS:COUN:ARR:NPOI</span>. You can set it with the 
        SCPI command <span class="scpiCmd">SENS:COUN:ARR:NPOI 2</span>, and also query its current value with the <span class="scpiCmd">SENS:COUN:ARR:NPOI?</span>.</p>

      <p>The format mentioned in this manual e.g.: <span class="scpiCmd">SENSe#:AVERage[:COUNt] &lt;pres&gt;</span> is called canonical form. Here are the most important rules to remember:</p>
      <ul>
        <li>The parts within square brackets are not mandatory and can be left out.</li>
        <li>All # within the command represent a suffix positive integer number starting at 1. If there is only one suffix you may skip it because default is applied internally. 
           If there are two or more suffix it is mandatory to specify all.</li>
        <li>The capital letter parts are mandatory; the small letters can be omitted. This is called short form. An example of the above command in the short form is <span class="scpiCmd">SENS1:AVER 3</span>. 
           You can use either the short form, or the long form <span class="scpiCmd">SENSE1:AVERAGE 3</span>, but nothing in between, e.g. <span class="scpiCmd">SENS1:AVERA 3</span>.</li>
        <li>The SCPI commands are case-insensitive. You can also use the short form <span class="scpiCmd">sens1:aver 3</span> or or the long form <span class="scpiCmd">sense1:average 3</span></li>
        <li>Combine multiple commands into one string using selmicolon ';'. For example, a combined string of <span class="scpiCmd">SENS1:AVER 3</span> and <span class="scpiCmd">SENS1:CORR:WAV 850</span> is 
            <span class="scpiCmd">SENS1:AVER 3;CORR:WAV 850</span>. Notice that the second command does not have the <span class="scpiCmd">SENS1:</span> part. The reason is, that the command tree path does 
            not change within one string. If you want to reset the command tree path to the root, use the colon character at the beginning of the second command: <span class="scpiCmd">SENS1:AVER 3;:SENS2:CORR:WAV 850</span>.</li>
        <li>Create query forms by adding a question mark, mostly to the end: <span class="scpiCmd">SENS1:AVER?</span> Sometimes there is an additional parameter placed after the 
            question mark. There must be a space character between the question mark and the additional parameter. For example: <span class="scpiCmd">SENS1:CORR:WAV? MIN</span></li>
      </ul>
      
      <p>The complete SCPI standard is available here: <a  target="_blank" href="https://www.ivifoundation.org/docs/scpi-99.pdf" title="SCPI Standard PDF">SCPI-99</a></p>
    </div>
    <div id="statusRegisterSetDesc">
      <h1>Measurement mode</h1>
      <p>The SPCNT module supports different modes of measurement. The base unit is pulse counts per bin. This value can also be calculated as frequency. The binning is the time base for counting and might be generated
         internally or externally via remote interface or hardware trigger (Gated Mode). The counter supports both positive input signals and zero-balanced signals. The smallest adjustable threshold corresponds to 0V input signal.
         The following block diagram gives a brief overview of the counting module hardware. The counter evaluates pulse count and frequency for every single bin. If you have a long bin width you can poll all results with MEAS? commands.
         For a short bin width you should better use array mode to get all results.
      </p>
      <svg width="500px" viewBox="472 278 551 269" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">  <g>    <polygon style="fill: #ffffff" points="680,400 680,280 760,340 "/>    <polygon style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="680,400 680,280 760,340 "/>  </g>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="540" y1="300" x2="675.528" y2="300"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="667.764,305 677.764,300 667.764,295 "/>  </g>  <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="596" y1="300" x2="596" y2="392"/>  <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="580" y1="392" x2="612" y2="392"/>  <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="584" y1="400" x2="608" y2="400"/>  <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="590" y1="408" x2="602" y2="408"/>  <g>    <rect style="fill: #ffffff" x="590" y="322" width="14" height="40"/>    <rect style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x="590" y="322" width="14" height="40"/>  </g>  <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="804" y1="340" x2="970" y2="340"/>  <g>    <rect style="fill: #ffffff" x="860" y="320" width="60" height="38"/>    <rect style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x="860" y="320" width="60" height="38"/>    <text font-size="12.7998" style="fill: #000000;text-anchor:middle;font-family:sans-serif;font-style:normal;font-weight:normal" x="890" y="343.8">      <tspan x="890" y="343.8">Driver</tspan>    </text>  </g>  <g>    <rect style="fill: #ffffff" x="804" y="440" width="60" height="38"/>    <rect style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x="804" y="440" width="60" height="38"/>    <text font-size="12.7998" style="fill: #000000;text-anchor:middle;font-family:sans-serif;font-style:normal;font-weight:normal" x="834" y="463.8">      <tspan x="834" y="463.8">&amp;</tspan>    </text>  </g>  <g>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="540,540 540,540 834,540 834,482.472 "/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="839,490.236 834,480.236 829,490.236 "/>  </g>  <text font-size="12.7998" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="608" y="346">    <tspan x="608" y="346">50 Ohm</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="692" y1="540" x2="692" y2="482.472"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="697,490.236 692,480.236 687,490.236 "/>  </g>  <g>    <rect style="fill: #ffffff" x="624" y="440" width="128.1" height="38"/>    <rect style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x="624" y="440" width="128.1" height="38"/>    <text font-size="12.7998" style="fill: #000000;text-anchor:middle;font-family:sans-serif;font-style:normal;font-weight:normal" x="688.05" y="463.8">      <tspan x="688.05" y="463.8">Processor</tspan>    </text>  </g>  <text font-size="12.7998" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="472" y="304">    <tspan x="472" y="304">Input TTL</tspan>  </text>  <text font-size="12.7998" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="476" y="544">    <tspan x="476" y="544">TRIG IN </tspan>  </text>  <text font-size="12.7998" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="980" y="340">    <tspan x="980" y="340">Monitor</tspan>  </text>  <text font-size="12.7998" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="690" y="340">    <tspan x="690" y="340">Schmitt</tspan>    <tspan x="690" y="356">Trigger</tspan>  </text>  <g>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="656.025,440 656.025,380 675.528,380 "/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="667.764,385 677.764,380 667.764,375 "/>  </g>  <g>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="760,340 760,340 834,340 834,435.528 "/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="829,427.764 834,437.764 839,427.764 "/>  </g>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="804" y1="459" x2="756.572" y2="459"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="764.336,454 754.336,459 764.336,464 "/>  </g>  <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="550" y1="468" x2="550" y2="540"/>  <g>    <rect style="fill: #ffffff" x="543" y="482" width="14" height="40"/>    <rect style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x="543" y="482" width="14" height="40"/>  </g>  <text font-size="12.7998" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="560" y="508">    <tspan x="560" y="508">10 kOhm</tspan>  </text>  <text font-size="12.7998" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="542" y="464">    <tspan x="542" y="464">5V</tspan>  </text></svg>
      <p>The INPUT signal is 50 Ohm terminated internally. A Schmitt-Trigger is used to compare the pulse input with a configurable threshold generated by the controller. The Schmitt-Trigger output is connected 
           to an AND gate and is also output by a driver on the monitor output. The TRIG IN signal is pulled high (10 kOhm) internally and also connected to the AND gate. The output of the AND gate is evaluated by the controller by an 32-bit counter.
      </p>
      <p>The following measurement modes are supported by the counting module. All numbers are just example values for this demonstration.
      </p>
      
      <h2>Internal Binning (default)</h2>
      <svg width="800px" viewBox="200 119 862 163" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">  <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="340,180 340,120 360,120 360,120 "/>  <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="360,120 360,180 420,180 420,180 "/>  <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="420,180 420,120 440,120 440,120 "/>  <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="440,120 440,180 500,180 500,180 "/>  <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="500,180 500,120 520,120 520,120 "/>  <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="520,120 520,180 580,180 580,180 "/>  <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="580,180 580,120 600,120 600,120 "/>  <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="600,120 600,180 660,180 660,180 "/>  <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="660,180 660,120 680,120 680,120 "/>  <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="680,120 680,180 740,180 740,180 "/>  <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="740,180 740,120 760,120 760,120 "/>  <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="760,120 760,180 820,180 820,180 "/>  <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="820,180 820,120 840,120 840,120 "/>  <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="840,120 840,180 900,180 900,180 "/>  <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="900,180 900,120 920,120 920,120 "/>  <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="920,120 920,180 980,180 980,180 "/>  <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="980,180 980,120 1000,120 1000,120 "/>  <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1000,120 1000,180 1060,180 1060,180 "/>  <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="340" y1="180" x2="280" y2="180"/>  <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="1060" y1="220" x2="280" y2="220"/>  <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="300" y1="220" x2="300" y2="280"/>  <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="460" y1="220" x2="460" y2="280"/>  <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="620" y1="220" x2="620" y2="280"/>  <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="780" y1="220" x2="780" y2="280"/>  <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="940" y1="220" x2="940" y2="280"/>  <text font-size="12.8" style="fill: #000000;text-anchor:middle;font-family:sans-serif;font-style:normal;font-weight:normal" x="380" y="250">    <tspan x="380" y="250">2 Counts / </tspan>    <tspan x="380" y="266">100 ms = 20 Hz</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:middle;font-family:sans-serif;font-style:normal;font-weight:normal" x="540" y="250">    <tspan x="540" y="250">2 Counts / </tspan>    <tspan x="540" y="266">100 ms = 20 Hz</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:middle;font-family:sans-serif;font-style:normal;font-weight:normal" x="700" y="250">    <tspan x="700" y="250">2 Counts / </tspan>    <tspan x="700" y="266">100 ms = 20 Hz</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:middle;font-family:sans-serif;font-style:normal;font-weight:normal" x="860" y="250">    <tspan x="860" y="250">2 Counts / </tspan>    <tspan x="860" y="266">100 ms = 20 Hz</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="200" y="160">    <tspan x="200" y="160">INPUT</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="200" y="230">    <tspan x="200" y="230">Internal</tspan>    <tspan x="200" y="246">Binning</tspan>    <tspan x="200" y="262">Counter</tspan>    <tspan x="200" y="278">Interval</tspan>  </text></svg>
      <p>By default the internal binning is used with a bin width of 0.5 Seconds. In this mode the input pulses are counted periodically without an interruption. 
         The bin width might be configured between 0.001 and 6 Seconds with a microsecond accuracy. If you pulse the TRIG IN gate in internal binning mode, the counter does not synchronize measurement
         with the external pulses on TRIG IN. But in case of low gate level no input pulses are counted. This mode might be used to Measurement averaged input pulses with an external TRIG IN.
      </p>
      
      <h2>Internal Binning with dead time</h2>
      <svg width="800px" viewBox="200 379 942 173" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">  <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="340,440 340,380 360,380 360,380 "/>  <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="360,380 360,440 420,440 420,440 "/>  <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="420,440 420,380 440,380 440,380 "/>  <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="440,380 440,440 500,440 500,440 "/>  <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="500,440 500,380 520,380 520,380 "/>  <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="520,380 520,440 580,440 580,440 "/>  <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="580,440 580,380 600,380 600,380 "/>  <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="600,380 600,440 660,440 660,440 "/>  <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="660,440 660,380 680,380 680,380 "/>  <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="680,380 680,440 740,440 740,440 "/>  <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="740,440 740,380 760,380 760,380 "/>  <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="760,380 760,440 820,440 820,440 "/>  <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="820,440 820,380 840,380 840,380 "/>  <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="840,380 840,440 900,440 900,440 "/>  <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="900,440 900,380 920,380 920,380 "/>  <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="920,380 920,440 980,440 980,440 "/>  <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="980,440 980,380 1000,380 1000,380 "/>  <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1000,380 1000,440 1060,440 1060,440 "/>  <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="340" y1="440" x2="280" y2="440"/>  <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="340,540 340,480 460,480 460,480 "/>  <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="460,480 460,540 540,540 540,540 "/>  <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="340" y1="540" x2="280" y2="540"/>  <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="540,540 540,480 660,480 660,480 "/>  <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="660,480 660,540 740,540 740,540 "/>  <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="740,540 740,480 860,480 860,480 "/>  <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="860,480 860,540 940,540 940,540 "/>  <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="940,540 940,480 1060,480 1060,480 "/>  <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1060,480 1060,540 1140,540 1140,540 "/>  <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1060,440 1060,380 1080,380 1080,380 "/>  <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1080,380 1080,440 1140,440 1140,440 "/>  <text font-size="12.8" style="fill: #000000;text-anchor:middle;font-family:sans-serif;font-style:normal;font-weight:normal" x="400" y="510">    <tspan x="400" y="510">2 Counts /</tspan>    <tspan x="400" y="526">100 ms = 20 Hz </tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:middle;font-family:sans-serif;font-style:normal;font-weight:normal" x="600" y="510">    <tspan x="600" y="510">1 Count / </tspan>    <tspan x="600" y="526">100 ms = 10Hz</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:middle;font-family:sans-serif;font-style:normal;font-weight:normal" x="800" y="510">    <tspan x="800" y="510">2 Counts /</tspan>    <tspan x="800" y="526">100 ms = 20 Hz</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:middle;font-family:sans-serif;font-style:normal;font-weight:normal" x="500" y="510">    <tspan x="500" y="510">Pause</tspan>    <tspan x="500" y="526">50 ms</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:middle;font-family:sans-serif;font-style:normal;font-weight:normal" x="700" y="510">    <tspan x="700" y="510">Pause</tspan>    <tspan x="700" y="526">50 ms</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:middle;font-family:sans-serif;font-style:normal;font-weight:normal" x="900" y="510">    <tspan x="900" y="510">Pause</tspan>    <tspan x="900" y="526">50 ms</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:middle;font-family:sans-serif;font-style:normal;font-weight:normal" x="1000" y="510">    <tspan x="1000" y="510">1 Count / </tspan>    <tspan x="1000" y="526">100 ms = 10Hz</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="200" y="420">    <tspan x="200" y="420">INPUT</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="200" y="500">    <tspan x="200" y="500">Internal</tspan>    <tspan x="200" y="516">Binning</tspan>    <tspan x="200" y="532">Counter</tspan>    <tspan x="200" y="548">Interval</tspan>  </text></svg>
      <p>It is also possible to use internal binning with optional dead time (pause) between the measurements. During dead time pulse count is discarded. 
        The dead time can be configured between 0 (disabled) and 6 Seconds with a microsecond accuracy. If you pulse the TRIG IN gate in internal binning mode, the counter does not synchronize measurement
         with the external pulses on TRIG IN. But in case of low gate level no input pulses are counted. This mode might be used to Measurement averaged input pulses with an external TRIG IN.
      </p>
      
      <h2>Software defined Binning</h2>
      <svg width="800px" viewBox="200 619 942 211" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">  <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="340,680 340,620 360,620 360,620 "/>  <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="360,620 360,680 420,680 420,680 "/>  <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="420,680 420,620 440,620 440,620 "/>  <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="440,620 440,680 500,680 500,680 "/>  <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="500,680 500,620 520,620 520,620 "/>  <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="520,620 520,680 580,680 580,680 "/>  <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="580,680 580,620 600,620 600,620 "/>  <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="600,620 600,680 660,680 660,680 "/>  <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="660,680 660,620 680,620 680,620 "/>  <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="680,620 680,680 740,680 740,680 "/>  <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="740,680 740,620 760,620 760,620 "/>  <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="760,620 760,680 820,680 820,680 "/>  <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="820,680 820,620 840,620 840,620 "/>  <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="840,620 840,680 900,680 900,680 "/>  <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="900,680 900,620 920,620 920,620 "/>  <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="920,620 920,680 980,680 980,680 "/>  <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="980,680 980,620 1000,620 1000,620 "/>  <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1000,620 1000,680 1060,680 1060,680 "/>  <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="340" y1="680" x2="280" y2="680"/>  <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="340,780 340,720 960,720 960,720 "/>  <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="340" y1="780" x2="280" y2="780"/>  <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="960,720 960,780 1140,780 1140,780 "/>  <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1060,680 1060,620 1080,620 1080,620 "/>  <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1080,620 1080,680 1140,680 1140,680 "/>  <text font-size="12.8" style="fill: #000000;text-anchor:middle;font-family:sans-serif;font-style:normal;font-weight:normal" x="640" y="740">    <tspan x="640" y="740">8 Counts /</tspan>    <tspan x="640" y="756">484 ms = 16.53 Hz </tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="300" y="820">    <tspan x="300" y="820">Software Trigger</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="920" y="820">    <tspan x="920" y="820">Software Trigger</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke-dasharray: 2; stroke: #000000" x1="344.472" y1="780" x2="955.528" y2="780"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="352.236,775 342.236,780 352.236,785 "/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="947.764,785 957.764,780 947.764,775 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:middle;font-family:sans-serif;font-style:normal;font-weight:normal" x="640" y="810">    <tspan x="640" y="810">Time measured by counter</tspan>    <tspan x="640" y="826">Time musst be &gt;= 1ms and less than 6 Seconds</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="200" y="660">    <tspan x="200" y="660">INPUT</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="200" y="730">    <tspan x="200" y="730">Internal</tspan>    <tspan x="200" y="746">Binning</tspan>    <tspan x="200" y="762">Counter</tspan>    <tspan x="200" y="778">Interval</tspan>  </text></svg>
      <p>The software defined binning allows flexible bin width. Here counting is started and stopped via remote interface commands. The time interval must be between 0.001 and 6 Seconds. Do not apply a TRIG IN signal in this mode.</p> 
      
      <h2>Gated Mode</h2>
      <svg width="800px"viewBox="200 958 922 201" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">  <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="320,1020 320,960 340,960 340,960 "/>  <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="340,960 340,1020 400,1020 400,1020 "/>  <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="400,1020 400,960 420,960 420,960 "/>  <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="420,960 420,1020 480,1020 480,1020 "/>  <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="480,1020 480,960 500,960 500,960 "/>  <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="500,960 500,1020 560,1020 560,1020 "/>  <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="560,1020 560,960 580,960 580,960 "/>  <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="580,960 580,1020 640,1020 640,1020 "/>  <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="640,1020 640,960 660,960 660,960 "/>  <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="660,960 660,1020 720,1020 720,1020 "/>  <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="720,1020 720,960 740,960 740,960 "/>  <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="740,960 740,1020 800,1020 800,1020 "/>  <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="800,1020 800,960 820,960 820,960 "/>  <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="820,960 820,1020 880,1020 880,1020 "/>  <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="880,1020 880,960 900,960 900,960 "/>  <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="900,960 900,1020 960,1020 960,1020 "/>  <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="960,1020 960,960 980,960 980,960 "/>  <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="980,960 980,1020 1040,1020 1040,1020 "/>  <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="320" y1="1020" x2="260" y2="1020"/>  <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="320,1120 320,1060 860,1060 860,1060 "/>  <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="320" y1="1120" x2="260" y2="1120"/>  <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="860,1060 860,1120 1120,1120 1120,1120 "/>  <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1040,1020 1040,960 1060,960 1060,960 "/>  <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1060,960 1060,1020 1120,1020 1120,1020 "/>  <text font-size="12.8" style="fill: #000000;text-anchor:middle;font-family:sans-serif;font-style:normal;font-weight:normal" x="600" y="1080">    <tspan x="600" y="1080">7 Counts /</tspan>    <tspan x="600" y="1096">412 ms = 16.99 Hz </tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke-dasharray: 2; stroke: #000000" x1="324.472" y1="1120" x2="855.528" y2="1120"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="332.236,1115 322.236,1120 332.236,1125 "/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="847.764,1125 857.764,1120 847.764,1115 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:middle;font-family:sans-serif;font-style:normal;font-weight:normal" x="580" y="1140">    <tspan x="580" y="1140">Time measured by counter</tspan>    <tspan x="580" y="1156">Time musst be &gt;= 1ms and less than 6 Seconds</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="200" y="1000">    <tspan x="200" y="1000">INPUT</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="200" y="1100">    <tspan x="200" y="1100">TRIG IN</tspan>  </text></svg>
      <p>The Gated Mode allows flexible hardware defined bin width. If the TRIG IN signal is high, pulses are counted. The time interval must be between 0.001 and 6 Seconds. TRIG IN is pulled at 5V internally. You can either activliy drive or 
      blank out the TTL signal.</p>
    
      <h1>Array Mode</h1>
      <p>The array mode allows to buffer count Measurement results (not frequency) in the device memory and read them in a bulk. This mode 
         is especially useful for a short bin width when the transfer of a single measurement is not fast enough. If you use the array 
         mode, the counter enqueues the Measurement results of all bins in the internal buffer.<br/>
         During the array measurement aquisition, the normal frequency and counts are updated with every single bin and can be 
         transferred in parallel. If your remote application does not query Measurement results using MEAS? fast enough, 
         results might be lost as the old Measurement results are overwritten by new bin.<br />
         Once the buffer is full you can read out the complete buffer. The counter uses a double buffer for 
         the next Measurement results while previous results are transferred. This guarantees no results are lost, as long as your 
         application is fast enough to query all array results.<br/>
         The update rate of the array mode depends on the bin width and array length. For example if you configured a bin width 
         of 0.1 Seconds and an array length of 20, the counter buffer will be filled every 2 Seconds. The counter only transfers 
         full buffers when they are completely filled with the requested amount of data. The maximal array length is 1000 entries. 
         Long array lengths are useful for short bin width.<br />
         The buffers are always updated. There is no start/stop mechanism. Changing on of the parameters (bin width, zero, ...) 
         invalidates all previosuly buffered results. When the buffer is completely filled, the 11th bit in the OPER:COND? 
         is set and the buffer is ready for transfer. Calling MEAS:ARR:COUN? before the buffer is filled will block the 
         request until the buffer is filled. To prevent long blocking times in the application, first check the OPER:COND? 
         register to test if the buffer is filled. Then the request MEAS:ARR:COUN? will transfer the buffer without a delay.<br/>
         For transfer speed reasons the MEAS:ARR:COUN? command response is in binary representation. Length and bin counts are all 
         4 Byte wide int data types and are transferred with the following little endian byte order:
      </p>
      <svg width="500px" viewBox="728 406 625 133" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">  <g>    <rect style="fill: #90ee90" x="936.975" y="500" width="54.05" height="38"></rect>    <rect style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x="936.975" y="500" width="54.05" height="38"></rect>    <text font-size="12.8" style="fill: #000000;text-anchor:middle;font-family:sans-serif;font-style:normal;font-weight:normal" x="964" y="522.9">      <tspan x="964" y="522.9">0x23</tspan>    </text>  </g>  <g>    <rect style="fill: #90ee90" x="988.975" y="500" width="54.05" height="38"></rect>    <rect style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x="988.975" y="500" width="54.05" height="38"></rect>    <text font-size="12.8" style="fill: #000000;text-anchor:middle;font-family:sans-serif;font-style:normal;font-weight:normal" x="1016" y="522.9">      <tspan x="1016" y="522.9">0x54</tspan>    </text>  </g>  <g>    <rect style="fill: #90ee90" x="1041.52" y="500" width="52.95" height="38"></rect>    <rect style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x="1041.52" y="500" width="52.95" height="38"></rect>    <text font-size="12.8" style="fill: #000000;text-anchor:middle;font-family:sans-serif;font-style:normal;font-weight:normal" x="1068" y="522.9">      <tspan x="1068" y="522.9">0x1c</tspan>    </text>  </g>  <g>    <rect style="fill: #90ee90" x="1092.97" y="500" width="54.05" height="38"></rect>    <rect style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x="1092.97" y="500" width="54.05" height="38"></rect>    <text font-size="12.8" style="fill: #000000;text-anchor:middle;font-family:sans-serif;font-style:normal;font-weight:normal" x="1120" y="522.9">      <tspan x="1120" y="522.9">0x01</tspan>    </text>  </g>  <g>    <rect style="fill: #90ee90" x="938" y="446" width="208" height="54"></rect>    <rect style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x="938" y="446" width="208" height="54"></rect>    <text font-size="12.8" style="fill: #000000;text-anchor:middle;font-family:sans-serif;font-style:normal;font-weight:normal" x="1042" y="476.9">      <tspan x="1042" y="476.9">0x011c5423 (18633763)</tspan>    </text>  </g>  <g>    <rect style="fill: #75daff" x="730" y="408" width="208" height="38"></rect>    <rect style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x="730" y="408" width="208" height="38"></rect>    <text font-size="12.8" style="fill: #000000;text-anchor:middle;font-family:sans-serif;font-style:normal;font-weight:normal" x="834" y="430.9">      <tspan x="834" y="430.9">Response len</tspan>    </text>  </g>  <g>    <rect style="fill: #90ee90" x="938" y="408" width="208" height="38"></rect>    <rect style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x="938" y="408" width="208" height="38"></rect>    <text font-size="12.8" style="fill: #000000;text-anchor:middle;font-family:sans-serif;font-style:normal;font-weight:normal" x="1042" y="430.9">      <tspan x="1042" y="430.9">1. bin counts</tspan>    </text>  </g>  <g>    <rect style="fill: #90ee90" x="1144" y="500" width="54.05" height="38"></rect>    <rect style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x="1144" y="500" width="54.05" height="38"></rect>    <text font-size="12.8" style="fill: #000000;text-anchor:middle;font-family:sans-serif;font-style:normal;font-weight:normal" x="1171.03" y="522.9">      <tspan x="1171.03" y="522.9">0x32</tspan>    </text>  </g>  <g>    <rect style="fill: #90ee90" x="1194.97" y="500" width="54.05" height="38"></rect>    <rect style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x="1194.97" y="500" width="54.05" height="38"></rect>    <text font-size="12.8" style="fill: #000000;text-anchor:middle;font-family:sans-serif;font-style:normal;font-weight:normal" x="1222" y="522.9">      <tspan x="1222" y="522.9">0x14</tspan>    </text>  </g>  <g>    <rect style="fill: #90ee90" x="1247.52" y="500" width="52.95" height="38"></rect>    <rect style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x="1247.52" y="500" width="52.95" height="38"></rect>    <text font-size="12.8" style="fill: #000000;text-anchor:middle;font-family:sans-serif;font-style:normal;font-weight:normal" x="1274" y="522.9">      <tspan x="1274" y="522.9">0x0f</tspan>    </text>  </g>  <g>    <rect style="fill: #90ee90" x="1298" y="500" width="54.05" height="38"></rect>    <rect style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x="1298" y="500" width="54.05" height="38"></rect>    <text font-size="12.8" style="fill: #000000;text-anchor:middle;font-family:sans-serif;font-style:normal;font-weight:normal" x="1325.03" y="522.9">      <tspan x="1325.03" y="522.9">0x01</tspan>    </text>  </g>  <g>    <rect style="fill: #90ee90" x="1144" y="446" width="208" height="54"></rect>    <rect style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x="1144" y="446" width="208" height="54"></rect>    <text font-size="12.8" style="fill: #000000;text-anchor:middle;font-family:sans-serif;font-style:normal;font-weight:normal" x="1248" y="476.9">      <tspan x="1248" y="476.9">0x010f1432 (17765426)</tspan>    </text>  </g>  <g>    <rect style="fill: #90ee90" x="1144" y="408" width="208" height="38"></rect>    <rect style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x="1144" y="408" width="208" height="38"></rect>    <text font-size="12.8" style="fill: #000000;text-anchor:middle;font-family:sans-serif;font-style:normal;font-weight:normal" x="1248" y="430.9">      <tspan x="1248" y="430.9">2. bin counts</tspan>    </text>  </g>  <g>    <rect style="fill: #75daff" x="730.054" y="500" width="54.65" height="38"></rect>    <rect style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x="730.054" y="500" width="54.65" height="38"></rect>    <text font-size="12.8" style="fill: #000000;text-anchor:middle;font-family:sans-serif;font-style:normal;font-weight:normal" x="757.379" y="522.9">      <tspan x="757.379" y="522.9">0x0A</tspan>    </text>  </g>  <g>    <rect style="fill: #75daff" x="780" y="500" width="54.05" height="38"></rect>    <rect style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x="780" y="500" width="54.05" height="38"></rect>    <text font-size="12.8" style="fill: #000000;text-anchor:middle;font-family:sans-serif;font-style:normal;font-weight:normal" x="807.025" y="522.9">      <tspan x="807.025" y="522.9">0x00</tspan>    </text>  </g>  <g>    <rect style="fill: #75daff" x="832" y="500" width="54.05" height="38"></rect>    <rect style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x="832" y="500" width="54.05" height="38"></rect>    <text font-size="12.8" style="fill: #000000;text-anchor:middle;font-family:sans-serif;font-style:normal;font-weight:normal" x="859.025" y="522.9">      <tspan x="859.025" y="522.9">0x00</tspan>    </text>  </g>  <g>    <rect style="fill: #75daff" x="884" y="500" width="54.05" height="38"></rect>    <rect style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x="884" y="500" width="54.05" height="38"></rect>    <text font-size="12.8" style="fill: #000000;text-anchor:middle;font-family:sans-serif;font-style:normal;font-weight:normal" x="911.025" y="522.9">      <tspan x="911.025" y="522.9">0x00</tspan>    </text>  </g>  <g>    <rect style="fill: #75daff" x="730" y="446" width="208" height="54"></rect>    <rect style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x="730" y="446" width="208" height="54"></rect>    <text font-size="12.8" style="fill: #000000;text-anchor:middle;font-family:sans-serif;font-style:normal;font-weight:normal" x="834" y="476.9">      <tspan x="834" y="476.9">0x0000000A (10)</tspan>    </text>  </g></svg>
      <p>The first 4 Bytes define the length of the transferred array length. Without any separator the count measurement result list follows. Every entry represents the counts of one bin.</p>
      <p>The following python code should give you an example how to parse the result.</p>
      <div class="codeSample">
        inst.write('MEAS:ARR:COUN?')<br />
        vals = inst.read_bytes(4)<br />
        length = struct.unpack('&lt;I',&nbsp;bytearray(vals))[0]<br />
        vals = inst.read_bytes(4 * length)<br />
        res = []<br />
        for i in range(0, 4 * length, 4):<br />
            res.append(struct.unpack('&lt;I',  bytearray(vals[i:i+4]))[0])<br />
      </div>
       
    <div id="statusRegisterSetDesc">
      <h1>SCPI Registers</h1>
      <p>The SCPI system defines a status reporting system based on status register sets. Every state or event is mapped 
      to a single bit in the values of the register sets. The SPCNT defines the following register sets: </p>
      <ul>
        <li><b>OPER</b>ation: Measurement state monitoring.</li>
        <li><b>QUEST</b>ionalbe: Measurement result reliability status monitoring.</li>
        <li><b>STD</b>andard Byte: SCPI global system status byte.</li>
      </ul>

      <p>Every register set contains 5 registers.</p>
      <ul>
        <li><b>COND</b>ition: The Condition register monitors the actual state. For example if a sensor is currently connected or not.</li>
        <li><b>PTR</b>ansition: The Positive Transition register masks bit changes from 0 to 1 of the Condition register to the Event register.</li>
        <li><b>NTR</b>ansition: The Negative Transition register masks bit changes from 1 to 0 of the Condition register to the Event register.</li>
        <li><b>EVENT</b>: The Event register shows changes of the Condition register. Some conditions only last for a short moment and are otherwise hard to detect.</li>
        <li><b>ENAB</b>le: The Enable register masks if changes of the Event register are forwarded to a higher level hierarchy Condition register.</li>
      </ul>
      <p>The following image visualizes the logical relation between the registers.</p>
      <svg width="600px" viewBox="239 259 690 160" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">  <g>    <rect style="fill: #ffffff" x="240" y="320" width="75.2" height="38"/>    <rect style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x="240" y="320" width="75.2" height="38"/>    <text font-size="12.8" style="fill: #000000;text-anchor:middle;font-family:sans-serif;font-style:normal;font-weight:normal" x="277.6" y="343.8">      <tspan x="277.6" y="343.8">CON</tspan>    </text>  </g>  <g>    <rect style="fill: #ffffff" x="360" y="260" width="136.65" height="38"/>    <rect style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x="360" y="260" width="136.65" height="38"/>    <text font-size="12.8" style="fill: #000000;text-anchor:middle;font-family:sans-serif;font-style:normal;font-weight:normal" x="428.325" y="283.8">      <tspan x="428.325" y="283.8">PTR: CON bit 0 -&gt; 1</tspan>    </text>  </g>  <g>    <rect style="fill: #ffffff" x="359.25" y="380" width="138.15" height="38"/>    <rect style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x="359.25" y="380" width="138.15" height="38"/>    <text font-size="12.8" style="fill: #000000;text-anchor:middle;font-family:sans-serif;font-style:normal;font-weight:normal" x="428.325" y="403.8">      <tspan x="428.325" y="403.8">NTR: CON bit 1 -&gt; 0</tspan>    </text>  </g>  <g>    <rect style="fill: #ffffff" x="540" y="320" width="75.2" height="38"/>    <rect style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x="540" y="320" width="75.2" height="38"/>    <text font-size="12.8" style="fill: #000000;text-anchor:middle;font-family:sans-serif;font-style:normal;font-weight:normal" x="577.6" y="343.8">      <tspan x="577.6" y="343.8">EVENT</tspan>    </text>  </g>  <g>    <rect style="fill: #ffffff" x="680" y="320" width="75.2" height="38"/>    <rect style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x="680" y="320" width="75.2" height="38"/>    <text font-size="12.8" style="fill: #000000;text-anchor:middle;font-family:sans-serif;font-style:normal;font-weight:normal" x="717.6" y="343.8">      <tspan x="717.6" y="343.8">ENAB</tspan>    </text>  </g>  <g>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="277.6,320 277.6,279 355.528,279 "/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="347.764,284 357.764,279 347.764,274 "/>  </g>  <g>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="277.6,358 277.6,399 354.778,399 "/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="347.014,404 357.014,399 347.014,394 "/>  </g>  <g>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="496.65,279 577.6,279 577.6,315.528 "/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="572.6,307.764 577.6,317.764 582.6,307.764 "/>  </g>  <g>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="497.4,399 577.6,399 577.6,362.472 "/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="582.6,370.236 577.6,360.236 572.6,370.236 "/>  </g>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="615.2" y1="339" x2="675.528" y2="339"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="667.764,344 677.764,339 667.764,334 "/>  </g>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke-dasharray: 4; stroke: #000000" x1="755.2" y1="339" x2="847.528" y2="339"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="839.764,344 849.764,339 839.764,334 "/>  </g>  <g>    <rect style="fill: #ffffff" x="852" y="320" width="75.2" height="38"/>    <rect style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x="852" y="320" width="75.2" height="38"/>    <text font-size="12.8" style="fill: #000000;text-anchor:middle;font-family:sans-serif;font-style:normal;font-weight:normal" x="889.6" y="343.8">      <tspan x="889.6" y="343.8">CON</tspan>    </text>  </g></svg>
      <p>The Condition register always reflects the current state. For example if the counter overflows or not. The Positive Transition register masks if a
      certain condition becomes true (bit changes from 0 to 1) to set the bit at the same position in the Event register. For example to set the Event register 
      bit to 1 if a timer overflow occurs. <br/>The Negative Transition register tests if condition becomes false to update the Event register. For example to 
      set event register bit to 1 if external trigger is no longer applied. The Enable register masks if changes of the Event register should be reported to the 
      higher level hierarchy Condition register. The register sets are logically chained. The following section will provide further details about the chaining.</p>
      <p>All registers except the STB register group are 16 bit wide. The following paragraph gives a short description of bit mask in general. Bit mask use the binary
      representation of numbers to store boolean states (true, false) or (active, inactive). This is a memory saving way to store multiple conditions at once.
      If you look on a decimal number 9 for example the binary representation is 1001 = 1 * 2 ^ 4 + 1 * 2 ^ 0. This bit mask means condition at bit position 1 and 4 are true. 
      All other conditions are false as all other bits are set to 0.<br/>
      To test if a certain bit is set to 1 you can either use bit logic operations or use generic mathematic operations. Bit logic operations are available in almost every 
      programming language. In Python you would write something like condition = bitmask &amp; (1 &lt;&lt; position).<br/>
      If you want to use standard math you can use the following formula: condition = round_down(bitmask / (2 ^ bitPos)) modulus 2.</p>
 
      <p>The OPERation register set hierarchy looks the following</p>
      <svg width="600px" viewBox="818 1858 643 343" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">  <g>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1100,2022 1160,2022 1160,2180 1315.53,2180 "/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1307.76,2185 1317.76,2180 1307.76,2175 "/>  </g>  <g>    <rect style="fill: #ffffff" x="1320" y="2020" width="140" height="180"/>    <rect style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x="1320" y="2020" width="140" height="180"/>    <text font-size="14.1111" style="fill: #000000;text-anchor:end;font-family:sans-serif;font-style:normal;font-weight:700" x="1451" y="2114.28">      <tspan x="1451" y="2114.28">*STB</tspan>    </text>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1180" y="2058">    <tspan x="1180" y="2058">Protection event </tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1180" y="2078">    <tspan x="1180" y="2078">Error queue state chng</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1180" y="2098">    <tspan x="1180" y="2098">Questionable status</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="1200" y1="2060" x2="1315.53" y2="2060"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1307.76,2065 1317.76,2060 1307.76,2055 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1180" y="2118">    <tspan x="1180" y="2118">Message Available</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="1200" y1="2080" x2="1315.53" y2="2080"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1307.76,2085 1317.76,2080 1307.76,2075 "/>  </g>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="1200" y1="2120" x2="1315.53" y2="2120"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1307.76,2125 1317.76,2120 1307.76,2115 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1180" y="2138">    <tspan x="1180" y="2138">Standard Event Status</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="1200" y1="2140" x2="1315.53" y2="2140"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1307.76,2145 1317.76,2140 1307.76,2135 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1180" y="2158">    <tspan x="1180" y="2158">Service Request</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="1200" y1="2160" x2="1315.53" y2="2160"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1307.76,2165 1317.76,2160 1307.76,2155 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1180" y="2178">    <tspan x="1180" y="2178">Operation Status</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="1200" y1="2180" x2="1315.53" y2="2180"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1307.76,2185 1317.76,2180 1307.76,2175 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1324" y="2044">    <tspan x="1324" y="2044">bit 0(1)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1324" y="2064">    <tspan x="1324" y="2064">bit 1(2)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1324" y="2084">    <tspan x="1324" y="2084">bit 2(4)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1324" y="2104">    <tspan x="1324" y="2104">bit 3(8)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1324" y="2124">    <tspan x="1324" y="2124">bit 4(16)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1324" y="2144">    <tspan x="1324" y="2144">bit 5(32)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1324" y="2164">    <tspan x="1324" y="2164">bit 6(64)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1324" y="2184">    <tspan x="1324" y="2184">bit 7(128)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1180" y="2038">    <tspan x="1180" y="2038">Aux Status </tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="1200" y1="2040" x2="1315.53" y2="2040"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1307.76,2045 1317.76,2040 1307.76,2035 "/>  </g>  <g>    <rect style="fill: #ffffff" x="960" y="1860" width="140" height="324"/>    <rect style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x="960" y="1860" width="140" height="324"/>    <text font-size="14.1111" style="fill: #000000;text-anchor:end;font-family:sans-serif;font-style:normal;font-weight:700" x="1091" y="2017.46">      <tspan x="1091" y="2017.46">STAT:</tspan>      <tspan x="1091" y="2035.1">OPER</tspan>    </text>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="820" y="1870">    <tspan x="820" y="1870">reserved</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="820" y="1890">    <tspan x="820" y="1890">reserved</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="820" y="1910">    <tspan x="820" y="1910">reserved</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="840" y1="1872" x2="955.528" y2="1872"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="947.764,1877 957.764,1872 947.764,1867 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="964" y="1876">    <tspan x="964" y="1876">bit 0(1)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="820" y="1930">    <tspan x="820" y="1930">reserved</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="840" y1="1892" x2="955.528" y2="1892"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="947.764,1897 957.764,1892 947.764,1887 "/>  </g>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="840" y1="1912" x2="955.528" y2="1912"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="947.764,1917 957.764,1912 947.764,1907 "/>  </g>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="840" y1="1932" x2="955.528" y2="1932"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="947.764,1937 957.764,1932 947.764,1927 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="818.703" y="1950">    <tspan x="818.703" y="1950">Counter active</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="840" y1="1952" x2="955.528" y2="1952"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="947.764,1957 957.764,1952 947.764,1947 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="820" y="1970">    <tspan x="820" y="1970">External Trigger</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="840" y1="1972" x2="955.528" y2="1972"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="947.764,1977 957.764,1972 947.764,1967 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="820" y="1990">    <tspan x="820" y="1990">resvered</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="840" y1="1992" x2="955.528" y2="1992"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="947.764,1997 957.764,1992 947.764,1987 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="820" y="2010">    <tspan x="820" y="2010">Zeroing</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="840" y1="2012" x2="955.528" y2="2012"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="947.764,2017 957.764,2012 947.764,2007 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="820" y="2030">    <tspan x="820" y="2030">Bin counter active</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="840" y1="2032" x2="955.528" y2="2032"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="947.764,2037 957.764,2032 947.764,2027 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="820" y="2050">    <tspan x="820" y="2050">Freqency to fetch</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="840" y1="2052" x2="955.528" y2="2052"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="947.764,2057 957.764,2052 947.764,2047 "/>  </g>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="840" y1="2072" x2="955.528" y2="2072"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="947.764,2077 957.764,2072 947.764,2067 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="820" y="2090">    <tspan x="820" y="2090">Array to fetch</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="840" y1="2092" x2="955.528" y2="2092"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="947.764,2097 957.764,2092 947.764,2087 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="820" y="2110">    <tspan x="820" y="2110">reserved</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="840" y1="2112" x2="955.528" y2="2112"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="947.764,2117 957.764,2112 947.764,2107 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="820" y="2170">    <tspan x="820" y="2170">reserved</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="840" y1="2172" x2="955.528" y2="2172"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="947.764,2177 957.764,2172 947.764,2167 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="964" y="1896">    <tspan x="964" y="1896">bit 1(2)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="964" y="1916">    <tspan x="964" y="1916">bit 2(4)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="964" y="1936">    <tspan x="964" y="1936">bit 3(8)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="964" y="1956">    <tspan x="964" y="1956">bit 4(16)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="964" y="1976">    <tspan x="964" y="1976">bit 5(32)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="964" y="1996">    <tspan x="964" y="1996">bit 6(64)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="964" y="2016">    <tspan x="964" y="2016">bit 7(128)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="964" y="2036">    <tspan x="964" y="2036">bit 8(256)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="964" y="2056">    <tspan x="964" y="2056">bit 9(512)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="964" y="2076">    <tspan x="964" y="2076">bit 10(1024)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="964" y="2096">    <tspan x="964" y="2096">bit 11(2048)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="964" y="2116">    <tspan x="964" y="2116">bit 12(4096)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="964" y="2136">    <tspan x="964" y="2136">bit 13(8192)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="964" y="2156">    <tspan x="964" y="2156">bit 14(16384)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="964" y="2176">    <tspan x="964" y="2176">bit 15(32768)</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="1200" y1="2100" x2="1315.53" y2="2100"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1307.76,2105 1317.76,2100 1307.76,2095 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="820" y="2068">    <tspan x="820" y="2068">Counts to fetch</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="820" y="2130">    <tspan x="820" y="2130">reserved</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="840" y1="2132" x2="955.528" y2="2132"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="947.764,2137 957.764,2132 947.764,2127 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="820" y="2150">    <tspan x="820" y="2150">reserved</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="840" y1="2152" x2="955.528" y2="2152"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="947.764,2157 957.764,2152 947.764,2147 "/>  </g></svg>

      <p>The QUESTionable register set hierarchy looks the following</p>
      <svg width="600px" viewBox="820 1057 642 328" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">  <g>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1100,1202 1160,1202 1160,1280 1315.53,1280 "/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1307.76,1285 1317.76,1280 1307.76,1275 "/>  </g>  <g>    <rect style="fill: #ffffff" x="960" y="1060" width="140" height="324"/>    <rect style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x="960" y="1060" width="140" height="324"/>    <text font-size="14.1111" style="fill: #000000;text-anchor:end;font-family:sans-serif;font-style:normal;font-weight:700" x="1091" y="1217.46">      <tspan x="1091" y="1217.46">STAT:</tspan>      <tspan x="1091" y="1235.1">QUEST</tspan>    </text>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="820" y="1070">    <tspan x="820" y="1070">reserved</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="820" y="1090">    <tspan x="820" y="1090">reserved</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="820" y="1110">    <tspan x="820" y="1110">reserved</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="840" y1="1072" x2="955.528" y2="1072"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="947.764,1077 957.764,1072 947.764,1067 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="964" y="1076">    <tspan x="964" y="1076">bit 0(1)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="820" y="1130">    <tspan x="820" y="1130">reserved</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="840" y1="1092" x2="955.528" y2="1092"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="947.764,1097 957.764,1092 947.764,1087 "/>  </g>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="840" y1="1112" x2="955.528" y2="1112"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="947.764,1117 957.764,1112 947.764,1107 "/>  </g>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="840" y1="1132" x2="955.528" y2="1132"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="947.764,1137 957.764,1132 947.764,1127 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="820" y="1150">    <tspan x="820" y="1150">reserved</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="840" y1="1152" x2="955.528" y2="1152"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="947.764,1157 957.764,1152 947.764,1147 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="820" y="1170">    <tspan x="820" y="1170">Questionable Frequ</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="840" y1="1172" x2="955.528" y2="1172"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="947.764,1177 957.764,1172 947.764,1167 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="820" y="1190">    <tspan x="820" y="1190">reserved</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="840" y1="1192" x2="955.528" y2="1192"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="947.764,1197 957.764,1192 947.764,1187 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="820" y="1210">    <tspan x="820" y="1210">reserved</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="840" y1="1212" x2="955.528" y2="1212"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="947.764,1217 957.764,1212 947.764,1207 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="820" y="1230">    <tspan x="820" y="1230">Questionable Calibrat</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="840" y1="1232" x2="955.528" y2="1232"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="947.764,1237 957.764,1232 947.764,1227 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="820" y="1250">    <tspan x="820" y="1250">Questionable Time</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="840" y1="1252" x2="955.528" y2="1252"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="947.764,1257 957.764,1252 947.764,1247 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="820" y="1270">    <tspan x="820" y="1270">reserved</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="840" y1="1272" x2="955.528" y2="1272"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="947.764,1277 957.764,1272 947.764,1267 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="820" y="1290">    <tspan x="820" y="1290">reserved</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="840" y1="1292" x2="955.528" y2="1292"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="947.764,1297 957.764,1292 947.764,1287 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="820" y="1310">    <tspan x="820" y="1310">reserved</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="840" y1="1312" x2="955.528" y2="1312"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="947.764,1317 957.764,1312 947.764,1307 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="820" y="1370">    <tspan x="820" y="1370">reserved</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="840" y1="1372" x2="955.528" y2="1372"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="947.764,1377 957.764,1372 947.764,1367 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="964" y="1096">    <tspan x="964" y="1096">bit 1(2)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="964" y="1116">    <tspan x="964" y="1116">bit 2(4)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="964" y="1136">    <tspan x="964" y="1136">bit 3(8)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="964" y="1156">    <tspan x="964" y="1156">bit 4(16)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="964" y="1176">    <tspan x="964" y="1176">bit 5(32)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="964" y="1196">    <tspan x="964" y="1196">bit 6(64)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="964" y="1216">    <tspan x="964" y="1216">bit 7(128)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="964" y="1236">    <tspan x="964" y="1236">bit 8(256)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="964" y="1256">    <tspan x="964" y="1256">bit 9(512)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="964" y="1276">    <tspan x="964" y="1276">bit 10(1024)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="964" y="1296">    <tspan x="964" y="1296">bit 11(2048)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="964" y="1316">    <tspan x="964" y="1316">bit 12(4096)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="964" y="1336">    <tspan x="964" y="1336">bit 13(8192)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="964" y="1356">    <tspan x="964" y="1356">bit 14(16384)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="964" y="1376">    <tspan x="964" y="1376">bit 15(32768)</tspan>  </text>  <g>    <rect style="fill: #ffffff" x="1320" y="1200" width="140" height="180"/>    <rect style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x="1320" y="1200" width="140" height="180"/>    <text font-size="14.1111" style="fill: #000000;text-anchor:end;font-family:sans-serif;font-style:normal;font-weight:700" x="1451" y="1294.28">      <tspan x="1451" y="1294.28">*STB</tspan>    </text>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1180" y="1238">    <tspan x="1180" y="1238">Protection event </tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1180" y="1258">    <tspan x="1180" y="1258">Error queue state chng</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1180" y="1278">    <tspan x="1180" y="1278">Questionable status</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="1200" y1="1240" x2="1315.53" y2="1240"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1307.76,1245 1317.76,1240 1307.76,1235 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1180" y="1298">    <tspan x="1180" y="1298">Message Available</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="1200" y1="1260" x2="1315.53" y2="1260"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1307.76,1265 1317.76,1260 1307.76,1255 "/>  </g>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="1200" y1="1300" x2="1315.53" y2="1300"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1307.76,1305 1317.76,1300 1307.76,1295 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1180" y="1318">    <tspan x="1180" y="1318">Standard Event Status</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="1200" y1="1320" x2="1315.53" y2="1320"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1307.76,1325 1317.76,1320 1307.76,1315 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1180" y="1338">    <tspan x="1180" y="1338">Service Request</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="1200" y1="1340" x2="1315.53" y2="1340"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1307.76,1345 1317.76,1340 1307.76,1335 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1180" y="1358">    <tspan x="1180" y="1358">Operation Status</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="1200" y1="1360" x2="1315.53" y2="1360"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1307.76,1365 1317.76,1360 1307.76,1355 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1324" y="1224">    <tspan x="1324" y="1224">bit 0(1)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1324" y="1244">    <tspan x="1324" y="1244">bit 1(2)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1324" y="1264">    <tspan x="1324" y="1264">bit 2(4)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1324" y="1284">    <tspan x="1324" y="1284">bit 3(8)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1324" y="1304">    <tspan x="1324" y="1304">bit 4(16)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1324" y="1324">    <tspan x="1324" y="1324">bit 5(32)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1324" y="1344">    <tspan x="1324" y="1344">bit 6(64)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1324" y="1364">    <tspan x="1324" y="1364">bit 7(128)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1180" y="1218">    <tspan x="1180" y="1218">Aux Status </tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="1200" y1="1220" x2="1315.53" y2="1220"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1307.76,1225 1317.76,1220 1307.76,1215 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="820" y="1330">    <tspan x="820" y="1330">reserved</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="840" y1="1332" x2="955.528" y2="1332"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="947.764,1337 957.764,1332 947.764,1327 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="820" y="1350">    <tspan x="820" y="1350">reserved</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="840" y1="1352" x2="955.528" y2="1352"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="947.764,1357 957.764,1352 947.764,1347 "/>  </g></svg>
    </div>
    <p><a href="#" id="foldAll">Unfold all SCPI Commands</a></p>
    <hr>
    <p id="None"><a href="#CLS" data-subsystem="None" class="unfoldSection">Unfold all None Commands</a></p>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="None">
        <span class="scpi" id="CLS">CLS</span><span class="briefScpi">Clears all SCPI event status registers</span>
      </div>
      <div class="content">
        <div class="scpiLong">*CLS</div>
        <div class="details">Use this command to clear all event bits in the SCPI event status registers. After calling this command all event registers are read as 0 as long no new event changes bits back to 1. For closer details about event registers read for example description of command <a class="linkSCPI" title="Queries the SCPI Operation Event register" href="#STAT:OPER?">STAT:OPER?</a>.</div>
        <div class="example">&gt;&gt; *CLS</div>

      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="None">
        <span class="scpi" id="ESE">ESE</span><span class="briefScpi">Programs the 8 bit wide Standard Event Enable Register</span>
      </div>
      <div class="content">
        <div class="scpiLong">*ESE <div class="tooltip" data-id="0">&lt;bitmask&gt;</div></div>
        <div class="paramDocu">Parameters<ul>
              <li id="0"><span class="param">bitmask</span><span class="paramType">uint</span> <span class="paramDesc">standard event enable register bit mask.</span></li>
        </ul></div>

        <div class="example">&gt;&gt; ESE 2</div>

      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="None">
        <span class="scpi" id="ESE?">ESE?</span><span class="briefScpi">Reads the 8 bit wide Standard Event Enable Register</span>
      </div>
      <div class="content">
        <div class="scpiLong">*ESE?</div>
        <div class="resultDocu">Response<ul>
          <li><span class="param">bitmask</span><span class="paramType">uint</span> <span class="paramDesc">standard event enable register bit mask.</span></li>
        </ul></div>

        <div class="example">&gt;&gt; ESE?<br />&lt;&lt; 0</div>

      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="None">
        <span class="scpi" id="ESR?">ESR?</span><span class="briefScpi">Reads and clears the Standard Event Register</span>
      </div>
      <div class="content">
        <div class="scpiLong">*ESR?</div>
        <div class="resultDocu">Response<ul>
          <li><span class="param">bitmask</span><span class="paramType">uint</span> <span class="paramDesc">standard byte event register bit mask.</span></li>
        </ul></div>

        <div class="example">&gt;&gt; ESR?<br />&lt;&lt; 2</div>

      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="None">
        <span class="scpi" id="IDN?">IDN?</span><span class="briefScpi">Reads the device identification string</span>
      </div>
      <div class="content">
        <div class="scpiLong">*IDN?</div>
        <div class="resultDocu">Response<ul>
          <li><span class="param">manuf</span><span class="paramType">string</span> <span class="paramDesc">Device manufacturer name.</span></li>
          <li><span class="param">devName</span><span class="paramType">string</span> <span class="paramDesc">Device name.</span></li>
          <li><span class="param">serial</span><span class="paramType">string</span> <span class="paramDesc">Device serial number.</span></li>
          <li><span class="param">version</span><span class="paramType">string</span> <span class="paramDesc">Firmware software number.</span></li>
        </ul></div>

        <div class="details">Call to query device identification string. </div>
        <div class="example">&gt;&gt; *IDN?<br />&lt;&lt; Thorlabs,SPCNT,M12345678,*******</div>

      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="None">
        <span class="scpi" id="LST?">LST?</span><span class="briefScpi">Lists supported SCPI commands</span>
      </div>
      <div class="content">
        <div class="scpiLong">*LST? <div class="tooltip" data-id="2">&lt;subsyst&gt;</div></div>
        <div class="paramDocu">Parameters<ul>
              <li id="2"><span class="param">subsyst</span><span class="paramType">'',string</span> <span class="paramDesc">nothing to list all cmds. SCPI subsystem string to filter results</span></li>
        </ul></div>

        <div class="resultDocu">Response<ul>
          <li><span class="param">list</span><span class="paramType">string</span> <span class="paramDesc">List of SCPI cmds. One command per line</span></li>
        </ul></div>

        <div class="details"> The command lists all SCPI commands matching the given filter condition. The filter condition can be any SCPI subsystem like for example "SYST". If no filter condition is set the command returns all supported commands. As the result list may be longer than the maximal SCPI response, the same command has to be called multiple times with the same parameter to retrieve all  commands. </div>
        <div class="example">&gt;&gt; *LST?<br />&lt;&lt; "*IDN?"\\n"*RST"\\n</div>
        <div class="note">Result length is limited. So call multiple times with same parameters to retrieve all results.</div>

      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="None">
        <span class="scpi" id="MEAS?">MEAS?</span><span class="briefScpi">Returns recently measured absolute or relative counts</span>
      </div>
      <div class="content">
        <div class="scpiLong">MEASure[:SCALar][:COUNts]?</div>
        <div class="resultDocu">Response<ul>
          <li><span class="param">counts</span><span class="paramType">int</span> <span class="paramDesc">last measured counts per bin</span></li>
        </ul></div>

        <div class="details">Call this function to measure the recently measured counts per pin. Once called the function blocks until the next count measurement is calculated internally. The update rate depends on the bin  width <a class="linkSCPI" title="Returns the width of the last bin in Seconds" href="#MEAS:TIME?">MEAS:TIME?</a>. To measure directly the resulting frequency use <a class="linkSCPI" title="Returns recently measured absolute or relative frequency in Hz" href="#MEAS:FREQ?">MEAS:FREQ?</a>. If <a class="linkSCPI" title="Returns value of zero offset in counts" href="#SENS:CORR:COLL:ZERO:MAGN?">SENS:CORR:COLL:ZERO:MAGN?</a> is not 0, the counts are  relative to this offset and might be negative. If there is no input  signal, counts are measured as 0. You might want to use  <a class="linkSCPI" title="Queries the SCPI Operation Condition register" href="#STAT:OPER:COND?">STAT:OPER:COND?</a> to test if there is count data to fetch.  To test if counts are valid you can also test  <a class="linkSCPI" title="Queries the SCPI Questionable Condition register" href="#STAT:QUES:COND?">STAT:QUES:COND?</a> register content.</div>
        <div class="example">&gt;&gt; MEAS?<br />&lt;&lt; 450</div>

      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="None">
        <span class="scpi" id="OPC">OPC</span><span class="briefScpi">Sets the Operation Complete bit in the Standard Event Register</span>
      </div>
      <div class="content">
        <div class="scpiLong">*OPC</div>
        <div class="example">&gt;&gt; OPC 2</div>

      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="None">
        <span class="scpi" id="OPC?">OPC?</span><span class="briefScpi">Operation complete query</span>
      </div>
      <div class="content">
        <div class="scpiLong">*OPC?</div>
        <div class="resultDocu">Response<ul>
          <li><span class="param">constant</span><span class="paramType">uint</span> <span class="paramDesc">Always 1</span></li>
        </ul></div>

        <div class="details">Places a "1" into the output queue when all device operations have been completed. No real functionality on this implementation. Result is always 1. </div>
        <div class="example">&gt;&gt; OPC?<br />&lt;&lt; 1</div>

      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="None">
        <span class="scpi" id="RST">RST</span><span class="briefScpi">Resets all SCPI and sensor parameters to default</span>
      </div>
      <div class="content">
        <div class="scpiLong">*RST</div>
        <div class="details"> The command resets all SCPI parameters like zero and bin width to defaults.  To reset the SCPI registers use <a class="linkSCPI" title="Clears all SCPI event status registers" href="#CLS">CLS</a>. </div>
        <div class="example">&gt;&gt; RST</div>

      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="None">
        <span class="scpi" id="SRE">SRE</span><span class="briefScpi">Service request enable command</span>
      </div>
      <div class="content">
        <div class="scpiLong">*SRE <div class="tooltip" data-id="1">&lt;bitmask&gt;</div></div>
        <div class="paramDocu">Parameters<ul>
              <li id="1"><span class="param">bitmask</span><span class="paramType">uint</span> <span class="paramDesc">new service request enable register bit mask.</span></li>
        </ul></div>

        <div class="details">Programs the Service Request Enable Register</div>
        <div class="example">&gt;&gt; SRE 2</div>

      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="None">
        <span class="scpi" id="SRE?">SRE?</span><span class="briefScpi">Service request enable query</span>
      </div>
      <div class="content">
        <div class="scpiLong">*SRE?</div>
        <div class="resultDocu">Response<ul>
          <li><span class="param">bitmask</span><span class="paramType">uint</span> <span class="paramDesc">service request enable register bit mask.</span></li>
        </ul></div>

        <div class="details">Reads the Service Request Enable Register</div>
        <div class="example">&gt;&gt; SRE?<br />&lt;&lt; 0</div>

      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="None">
        <span class="scpi" id="STB?">STB?</span><span class="briefScpi">Status byte query</span>
      </div>
      <div class="content">
        <div class="scpiLong">*STB?</div>
        <div class="resultDocu">Response<ul>
          <li><span class="param">bitmask</span><span class="paramType">uint</span> <span class="paramDesc">status byte condition register bit mask.</span></li>
        </ul></div>

        <div class="details">Reads the Status Byte Register</div>
        <div class="example">&gt;&gt; STB?<br />&lt;&lt; 20</div>

      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="None">
        <span class="scpi" id="TST?">TST?</span><span class="briefScpi">Self-test query</span>
      </div>
      <div class="content">
        <div class="scpiLong">*TST?</div>
        <div class="resultDocu">Response<ul>
          <li><span class="param">const</span><span class="paramType">uint</span> <span class="paramDesc">Always 0.</span></li>
        </ul></div>

        <div class="details">No function always returns 0.</div>
        <div class="example">&gt;&gt; TST?<br />&lt;&lt; 0</div>

      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="None">
        <span class="scpi" id="WAI">WAI</span><span class="briefScpi">Wait-to-continue command</span>
      </div>
      <div class="content">
        <div class="scpiLong">*WAI</div>
        <div class="details">Wait until all previous commands are executed. Empty implementation.</div>
        <div class="example">&gt;&gt; WAI</div>

      </div>
    </div>
    <hr>
    <p id="CAL"><a href="#CAL:STR?" data-subsystem="CAL" class="unfoldSection">Unfold all CAL Commands</a></p>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="CAL">
        <span class="scpi" id="CAL:STR?">CAL:STR?</span><span class="briefScpi">Queries factory calibration string</span>
      </div>
      <div class="content">
        <div class="scpiLong">CALibration:STRing?</div>
        <div class="resultDocu">Response<ul>
          <li><span class="param">date</span><span class="paramType">string</span> <span class="paramDesc">Cal string with format dd-MonthStr-YYYY or 'Uncalibrated' when not calibrated.</span></li>
        </ul></div>

        <div class="details">Use this command to query the factory calibration date string of the device.</div>
        <div class="example">&gt;&gt; CAL:STR?<br />&lt;&lt; "18-Feb-2022"</div>

      </div>
    </div>
    <hr>
    <p id="DISP"><a href="#DISP:BRIG" data-subsystem="DISP" class="unfoldSection">Unfold all DISP Commands</a></p>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="DISP">
        <span class="scpi" id="DISP:BRIG">DISP:BRIG</span><span class="briefScpi">Sets display brightness in percent</span>
      </div>
      <div class="content">
        <div class="scpiLong">DISPlay:BRIGhtness <div class="tooltip" data-id="14">&lt;brightness&gt;</div></div>
        <div class="paramDocu">Parameters<ul>
              <li id="14"><span class="param">brightness</span><span class="paramType">float</span> <span class="paramDesc">brightness in percent between 0 and 1</span></li>
        </ul></div>

        <div class="example">&gt;&gt; DISP:BRIG 0.5</div>

      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="DISP">
        <span class="scpi" id="DISP:BRIG?">DISP:BRIG?</span><span class="briefScpi">Queries display brightness in percent</span>
      </div>
      <div class="content">
        <div class="scpiLong">DISPlay:BRIGhtness?</div>
        <div class="resultDocu">Response<ul>
          <li><span class="param">brightness</span><span class="paramType">float</span> <span class="paramDesc">Brightness in percent between 0 and 1</span></li>
        </ul></div>

        <div class="example">&gt;&gt; DISP:BRIG?<br />&lt;&lt; 0.91</div>

      </div>
    </div>
    <hr>
    <p id="MEAS"><a href="#MEAS:ARR:COUN?" data-subsystem="MEAS" class="unfoldSection">Unfold all MEAS Commands</a></p>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="MEAS">
        <span class="scpi" id="MEAS:ARR:COUN?">MEAS:ARR:COUN?</span><span class="briefScpi">Returns a list of bin counts as binary data</span>
      </div>
      <div class="content">
        <div class="scpiLong">MEASure:ARRay:COUNt?</div>
        <div class="resultDocu">Response<ul>
          <li><span class="param">binaryData</span><span class="paramType">binary</span> <span class="paramDesc">4 Bytes binaryLength followed by 4 bytes * binaryLength count values</span></li>
        </ul></div>

        <div class="details">Call this function to get the results of the recent n counts per bin as binary data. Use <a class="linkSCPI" title="Sets the amount of bins stored in array" href="#SENS:COUN:ARR:NPOI">SENS:COUN:ARR:NPOI</a> to configure the length of the array and <a class="linkSCPI" title="Sets the width of a single bin" href="#SENS:COUN:GATE:APER">SENS:COUN:GATE:APER</a> to change the bin width. The total time to fill the array depends on both configurations. Changing any parameter invalidates the recent array measurements. Once the array has been read by this command, the content is invalidated and the command will return length 0  as long as the new array has not been filled completely. The counter  uses a shadow array to continue bin counting in background while recent data is read back. You might want to use  <a class="linkSCPI" title="Queries the SCPI Operation Condition register" href="#STAT:OPER:COND?">STAT:OPER:COND?</a> to test if there is array data to fetch. To test if all counts are valid you can also test  <a class="linkSCPI" title="Queries the SCPI Questionable Condition register" href="#STAT:QUES:COND?">STAT:QUES:COND?</a> register content. If <a class="linkSCPI" title="Returns value of zero offset in counts" href="#SENS:CORR:COLL:ZERO:MAGN?">SENS:CORR:COLL:ZERO:MAGN?</a> is applied,  the measurement values in the array can be negative. Length and bin counts are all int32 data types and are transferred  with the following little endian byte order: <p> <svg width="900px" viewBox="728 406 625 133" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">  <g>    <rect style="fill: #90ee90" x="936.975" y="500" width="54.05" height="38"/>    <rect style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x="936.975" y="500" width="54.05" height="38"/>    <text font-size="12.8" style="fill: #000000;text-anchor:middle;font-family:sans-serif;font-style:normal;font-weight:normal" x="964" y="522.9">      <tspan x="964" y="522.9">0x23</tspan>    </text>  </g>  <g>    <rect style="fill: #90ee90" x="988.975" y="500" width="54.05" height="38"/>    <rect style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x="988.975" y="500" width="54.05" height="38"/>    <text font-size="12.8" style="fill: #000000;text-anchor:middle;font-family:sans-serif;font-style:normal;font-weight:normal" x="1016" y="522.9">      <tspan x="1016" y="522.9">0x54</tspan>    </text>  </g>  <g>    <rect style="fill: #90ee90" x="1041.52" y="500" width="52.95" height="38"/>    <rect style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x="1041.52" y="500" width="52.95" height="38"/>    <text font-size="12.8" style="fill: #000000;text-anchor:middle;font-family:sans-serif;font-style:normal;font-weight:normal" x="1068" y="522.9">      <tspan x="1068" y="522.9">0x1c</tspan>    </text>  </g>  <g>    <rect style="fill: #90ee90" x="1092.97" y="500" width="54.05" height="38"/>    <rect style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x="1092.97" y="500" width="54.05" height="38"/>    <text font-size="12.8" style="fill: #000000;text-anchor:middle;font-family:sans-serif;font-style:normal;font-weight:normal" x="1120" y="522.9">      <tspan x="1120" y="522.9">0x01</tspan>    </text>  </g>  <g>    <rect style="fill: #90ee90" x="938" y="446" width="208" height="54"/>    <rect style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x="938" y="446" width="208" height="54"/>    <text font-size="12.8" style="fill: #000000;text-anchor:middle;font-family:sans-serif;font-style:normal;font-weight:normal" x="1042" y="476.9">      <tspan x="1042" y="476.9">0x011c5423 (18633763)</tspan>    </text>  </g>  <g>    <rect style="fill: #75daff" x="730" y="408" width="208" height="38"/>    <rect style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x="730" y="408" width="208" height="38"/>    <text font-size="12.8" style="fill: #000000;text-anchor:middle;font-family:sans-serif;font-style:normal;font-weight:normal" x="834" y="430.9">      <tspan x="834" y="430.9">Response len</tspan>    </text>  </g>  <g>    <rect style="fill: #90ee90" x="938" y="408" width="208" height="38"/>    <rect style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x="938" y="408" width="208" height="38"/>    <text font-size="12.8" style="fill: #000000;text-anchor:middle;font-family:sans-serif;font-style:normal;font-weight:normal" x="1042" y="430.9">      <tspan x="1042" y="430.9">1. bin counts</tspan>    </text>  </g>  <g>    <rect style="fill: #90ee90" x="1144" y="500" width="54.05" height="38"/>    <rect style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x="1144" y="500" width="54.05" height="38"/>    <text font-size="12.8" style="fill: #000000;text-anchor:middle;font-family:sans-serif;font-style:normal;font-weight:normal" x="1171.03" y="522.9">      <tspan x="1171.03" y="522.9">0x32</tspan>    </text>  </g>  <g>    <rect style="fill: #90ee90" x="1194.97" y="500" width="54.05" height="38"/>    <rect style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x="1194.97" y="500" width="54.05" height="38"/>    <text font-size="12.8" style="fill: #000000;text-anchor:middle;font-family:sans-serif;font-style:normal;font-weight:normal" x="1222" y="522.9">      <tspan x="1222" y="522.9">0x14</tspan>    </text>  </g>  <g>    <rect style="fill: #90ee90" x="1247.52" y="500" width="52.95" height="38"/>    <rect style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x="1247.52" y="500" width="52.95" height="38"/>    <text font-size="12.8" style="fill: #000000;text-anchor:middle;font-family:sans-serif;font-style:normal;font-weight:normal" x="1274" y="522.9">      <tspan x="1274" y="522.9">0x0f</tspan>    </text>  </g>  <g>    <rect style="fill: #90ee90" x="1298" y="500" width="54.05" height="38"/>    <rect style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x="1298" y="500" width="54.05" height="38"/>    <text font-size="12.8" style="fill: #000000;text-anchor:middle;font-family:sans-serif;font-style:normal;font-weight:normal" x="1325.03" y="522.9">      <tspan x="1325.03" y="522.9">0x01</tspan>    </text>  </g>  <g>    <rect style="fill: #90ee90" x="1144" y="446" width="208" height="54"/>    <rect style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x="1144" y="446" width="208" height="54"/>    <text font-size="12.8" style="fill: #000000;text-anchor:middle;font-family:sans-serif;font-style:normal;font-weight:normal" x="1248" y="476.9">      <tspan x="1248" y="476.9">0x010f1432 (17765426)</tspan>    </text>  </g>  <g>    <rect style="fill: #90ee90" x="1144" y="408" width="208" height="38"/>    <rect style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x="1144" y="408" width="208" height="38"/>    <text font-size="12.8" style="fill: #000000;text-anchor:middle;font-family:sans-serif;font-style:normal;font-weight:normal" x="1248" y="430.9">      <tspan x="1248" y="430.9">2. bin counts</tspan>    </text>  </g>  <g>    <rect style="fill: #75daff" x="730.054" y="500" width="54.65" height="38"/>    <rect style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x="730.054" y="500" width="54.65" height="38"/>    <text font-size="12.8" style="fill: #000000;text-anchor:middle;font-family:sans-serif;font-style:normal;font-weight:normal" x="757.379" y="522.9">      <tspan x="757.379" y="522.9">0x0A</tspan>    </text>  </g>  <g>    <rect style="fill: #75daff" x="780" y="500" width="54.05" height="38"/>    <rect style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x="780" y="500" width="54.05" height="38"/>    <text font-size="12.8" style="fill: #000000;text-anchor:middle;font-family:sans-serif;font-style:normal;font-weight:normal" x="807.025" y="522.9">      <tspan x="807.025" y="522.9">0x00</tspan>    </text>  </g>  <g>    <rect style="fill: #75daff" x="832" y="500" width="54.05" height="38"/>    <rect style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x="832" y="500" width="54.05" height="38"/>    <text font-size="12.8" style="fill: #000000;text-anchor:middle;font-family:sans-serif;font-style:normal;font-weight:normal" x="859.025" y="522.9">      <tspan x="859.025" y="522.9">0x00</tspan>    </text>  </g>  <g>    <rect style="fill: #75daff" x="884" y="500" width="54.05" height="38"/>    <rect style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x="884" y="500" width="54.05" height="38"/>    <text font-size="12.8" style="fill: #000000;text-anchor:middle;font-family:sans-serif;font-style:normal;font-weight:normal" x="911.025" y="522.9">      <tspan x="911.025" y="522.9">0x00</tspan>    </text>  </g>  <g>    <rect style="fill: #75daff" x="730" y="446" width="208" height="54"/>    <rect style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x="730" y="446" width="208" height="54"/>    <text font-size="12.8" style="fill: #000000;text-anchor:middle;font-family:sans-serif;font-style:normal;font-weight:normal" x="834" y="476.9">      <tspan x="834" y="476.9">0x0000000A (10)</tspan>    </text>  </g></svg> </p> The following pseudo code should give you an example how to parse the  result. <p class="code"> val = tuple[0]<br /> val = val | (tuple[1] << 8)<br /> val = val | (tuple[2] << 16)<br /> val = val | (tuple[3] << 24)<br /> </p> For closer details about this mode read <a href="#measArray" title="Array Measurement">Array Measurement</a> section.</div>
        <div class="example">&gt;&gt; MEAS:ARR:COUN?<br />&lt;&lt; binaryData</div>

      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="MEAS">
        <span class="scpi" id="MEAS:FREQ?">MEAS:FREQ?</span><span class="briefScpi">Returns recently measured absolute or relative frequency in Hz</span>
      </div>
      <div class="content">
        <div class="scpiLong">MEASure[:SCALar]:FREQuency?</div>
        <div class="resultDocu">Response<ul>
          <li><span class="param">freqz</span><span class="paramType">float</span> <span class="paramDesc">last measured frequency in Hertz</span></li>
        </ul></div>

        <div class="details">Call this function to measure the recently measured frequency in Hz. Once called the function blocks until the next frequency measurement is calculated internally. The update rate depends on the bin width  <a class="linkSCPI" title="Returns the width of the last bin in Seconds" href="#MEAS:TIME?">MEAS:TIME?</a>. The frequency is calculated as the result of  <a class="linkSCPI" title="Returns recently measured absolute or relative counts" href="#MEAS?">MEAS?</a> and the bin width (counts / binWidth).  To get the counts per bin use <a class="linkSCPI" title="Returns recently measured absolute or relative counts" href="#MEAS?">MEAS?</a>. If <a class="linkSCPI" title="Returns value of zero offset in counts" href="#SENS:CORR:COLL:ZERO:MAGN?">SENS:CORR:COLL:ZERO:MAGN?</a> is not 0, the frequency is relative to this  offset and might be negative. If there is no input signal, frequency  is measured as 0. You might want to use  <a class="linkSCPI" title="Queries the SCPI Operation Condition register" href="#STAT:OPER:COND?">STAT:OPER:COND?</a> to test if there is frequency data to fetch. To test if frequency measurement is valid you can also test  <a class="linkSCPI" title="Queries the SCPI Questionable Condition register" href="#STAT:QUES:COND?">STAT:QUES:COND?</a> register content.</div>
        <div class="example">&gt;&gt; MEAS:FREQ?<br />&lt;&lt; 1202.23</div>

      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="MEAS">
        <span class="scpi" id="MEAS:TIME?">MEAS:TIME?</span><span class="briefScpi">Returns the width of the last bin in Seconds</span>
      </div>
      <div class="content">
        <div class="scpiLong">MEASure:TIME?</div>
        <div class="resultDocu">Response<ul>
          <li><span class="param">time</span><span class="paramType">float</span> <span class="paramDesc">last measured bin width in Seconds</span></li>
        </ul></div>

        <div class="details">Call this function to measure the width of the bins in Seconds. The bin width depends on the <a class="linkSCPI" title="Sets the width of a single bin" href="#SENS:COUN:GATE:APER">SENS:COUN:GATE:APER</a> command. For all non zero bin width values the counter internally applies the configured bin width. In this case the command returns the same as <a class="linkSCPI" title="Queries the width of a single bin" href="#SENS:COUN:GATE:APER?">SENS:COUN:GATE:APER?</a>. If the <a class="linkSCPI" title="Sets the width of a single bin" href="#SENS:COUN:GATE:APER">SENS:COUN:GATE:APER</a> is 0 the bin width is defined either by software <a class="linkSCPI" title="Starts software defined frequency count bin" href="#SENS:FREQ:COLL">SENS:FREQ:COLL</a> or by an external TTL signal. In this case this function returns the measured bin width. Time resolution are ms. Maximal bin width is 6 Seconds.</div>
        <div class="example">&gt;&gt; MEAS:COUNT?<br />&lt;&lt; 450</div>

      </div>
    </div>
    <hr>
    <p id="SENS"><a href="#SENS:CORR:COLL:ZERO" data-subsystem="SENS" class="unfoldSection">Unfold all SENS Commands</a></p>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="SENS">
        <span class="scpi" id="SENS:CORR:COLL:ZERO">SENS:CORR:COLL:ZERO</span><span class="briefScpi">Starts zeroing of counter</span>
      </div>
      <div class="content">
        <div class="scpiLong">SENSe:CORRection:COLLect:ZERO[:INITiate]</div>
        <div class="details">Use this function to start zeroing procedure in background. The procedure takes 7 times the bin width and uses the median as zero offset.  You might check for completion by <a class="linkSCPI" title="Tests if zeroing procedure is running" href="#SENS:CORR:COLL:ZERO:STAT?">SENS:CORR:COLL:ZERO:STAT?</a>. Once the procedure calculated a zero offset future count values are subtracted by  this offset internally. Starting zero multiple times in paralegal is  not supported. You might want to query the zero offset frequency by using command <a class="linkSCPI" title="Returns value of zero offset in counts" href="#SENS:CORR:COLL:ZERO:MAGN?">SENS:CORR:COLL:ZERO:MAGN?</a>. To reset zero counts or apply a custom zero count number use <a class="linkSCPI" title="Sets dark counts to a given value" href="#SENS:CORR:COLL:ZERO:MAGN">SENS:CORR:COLL:ZERO:MAGN</a>. The value is not stored persistently in the counter and will be reset to 0 after reboot.</div>
        <div class="example">&gt;&gt; SENS:CORR:COLL:ZERO</div>

      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="SENS">
        <span class="scpi" id="SENS:CORR:COLL:ZERO:ABOR">SENS:CORR:COLL:ZERO:ABOR</span><span class="briefScpi">Aborts previously started zeroing procedure</span>
      </div>
      <div class="content">
        <div class="scpiLong">SENSe:CORRection:COLLect:ZERO:ABORt</div>
        <div class="details">Use this command to abort a previously started zeroing. The zeroing of sensor is  running as asynchronous background operation. Aborting the sequence will end  the background operation and enables measuring with the old zero value. To start a new zeroing use <a class="linkSCPI" title="Starts zeroing of counter" href="#SENS:CORR:COLL:ZERO">SENS:CORR:COLL:ZERO</a> command.</div>
        <div class="example">&gt;&gt; SENS:CORR:COLL:ABOR</div>
        <div class="info">Aborting if zeroing is already complete or has never been started has no effect.</div>
      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="SENS">
        <span class="scpi" id="SENS:CORR:COLL:ZERO:MAGN">SENS:CORR:COLL:ZERO:MAGN</span><span class="briefScpi">Sets dark counts to a given value</span>
      </div>
      <div class="content">
        <div class="scpiLong">SENSe:CORRection:COLLect:ZERO:MAGNitude <div class="tooltip" data-id="3">&lt;zero&gt;</div></div>
        <div class="paramDocu">Parameters<ul>
              <li id="3"><span class="param">zero</span><span class="paramType">float</span> <span class="paramDesc">zero offset in counts</span></li>
        </ul></div>

        <div class="details">Use this command to set the zero counts value to correct dark counts. Setting zero directly will not start the asynchronous zeroing procedure. In most cases you want the counter to measure zero counts itself using <a class="linkSCPI" title="Starts zeroing of counter" href="#SENS:CORR:COLL:ZERO">SENS:CORR:COLL:ZERO</a> command. This might be used to make relative values to a fixed count number. To convert counts to a frequency in Hertz check for bin width by using <a class="linkSCPI" title="Queries the width of a single bin" href="#SENS:COUN:GATE:APER?">SENS:COUN:GATE:APER?</a> and calculate zero / binWidth.  Zero parameter is not stored persistently in the counter and will be reset to 0 after reboot.</div>
        <div class="example">&gt;&gt; SENS:CORR:COLL:ZERO:MAGN 0</div>
        <div class="info">Set to 0 to disable zero correction.</div>
      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="SENS">
        <span class="scpi" id="SENS:CORR:COLL:ZERO:MAGN?">SENS:CORR:COLL:ZERO:MAGN?</span><span class="briefScpi">Returns value of zero offset in counts</span>
      </div>
      <div class="content">
        <div class="scpiLong">SENSe:CORRection:COLLect:ZERO:MAGNitude?</div>
        <div class="resultDocu">Response<ul>
          <li><span class="param">freqz</span><span class="paramType">float</span> <span class="paramDesc">zero offset in counts</span></li>
        </ul></div>

        <div class="details">Queries the currently used measurement zero count offset.  Use <a class="linkSCPI" title="Starts zeroing of counter" href="#SENS:CORR:COLL:ZERO">SENS:CORR:COLL:ZERO</a> or <a class="linkSCPI" title="Sets dark counts to a given value" href="#SENS:CORR:COLL:ZERO:MAGN">SENS:CORR:COLL:ZERO:MAGN</a> to change the offset.</div>
        <div class="example">&gt;&gt; SENS:CORR:COLL:ZERO:MAGN?<br />&lt;&lt; 15</div>

      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="SENS">
        <span class="scpi" id="SENS:CORR:COLL:ZERO:STAT?">SENS:CORR:COLL:ZERO:STAT?</span><span class="briefScpi">Tests if zeroing procedure is running</span>
      </div>
      <div class="content">
        <div class="scpiLong">SENSe:CORRection:COLLect:ZERO:STATe?</div>
        <div class="resultDocu">Response<ul>
          <li><span class="param">state</span><span class="paramType">bool</span> <span class="paramDesc">0 if no zeroing is running, 1 otherwise.</span></li>
        </ul></div>

        <div class="details">Use this command to test if previously started zeroing procedure is running in background. The zeroing of sensor is running as asynchronous background operation. The zeroing will terminate automatically in case of error or success. Once terminated this command will return 0. If no zeroing has been started the command will also return 0. Another way to test if zeroing is complete is to poll for zeroing flag bit 7 in the Operation Status register <a class="linkSCPI" title="Queries the SCPI Operation Condition register" href="#STAT:OPER:COND?">STAT:OPER:COND?</a>.</div>
        <div class="example">&gt;&gt; SENS:CORR:COLL:ZERO:STATe?<br />&lt;&lt; 1</div>

      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="SENS">
        <span class="scpi" id="SENS:COUN:ARR:NPOI">SENS:COUN:ARR:NPOI</span><span class="briefScpi">Sets the amount of bins stored in array</span>
      </div>
      <div class="content">
        <div class="scpiLong">SENSe:COUNt:ARRay:NPOInts <div class="tooltip" data-id="5">&lt;length&gt;</div></div>
        <div class="paramDocu">Parameters<ul>
              <li id="5"><span class="param">length</span><span class="paramType">uint</span> <span class="paramDesc">amount of bins stored in array between 1 and 1000.</span></li>
        </ul></div>

        <div class="details">Call this function to change the amount of bins stored in array. This command invalidates data stored in array previously. The array length and <a class="linkSCPI" title="Sets the width of a single bin" href="#SENS:COUN:GATE:APER">SENS:COUN:GATE:APER</a> defines the update rate of <a class="linkSCPI" title="Returns a list of bin counts as binary data" href="#MEAS:ARR:COUN?">MEAS:ARR:COUN?</a>.</div>
        <div class="example">&gt;&gt; SENS:COUN:ARR:NPOI 10</div>

      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="SENS">
        <span class="scpi" id="SENS:COUN:ARR:NPOI?">SENS:COUN:ARR:NPOI?</span><span class="briefScpi">Queries the amount of bins stored in array</span>
      </div>
      <div class="content">
        <div class="scpiLong">SENSe:COUNt:ARRay:NPOInts?</div>
        <div class="resultDocu">Response<ul>
          <li><span class="param">length</span><span class="paramType">uint</span> <span class="paramDesc">amount of bins stored in array</span></li>
        </ul></div>

        <div class="details">Call this function to read the amount of bins stored in array. For closer info read <a class="linkSCPI" title="Sets the amount of bins stored in array" href="#SENS:COUN:ARR:NPOI">SENS:COUN:ARR:NPOI</a>.</div>
        <div class="example">&gt;&gt; SENS:COUN:ARR:NPOI?<br />&lt;&lt; 5</div>

      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="SENS">
        <span class="scpi" id="SENS:COUN:GATE:APER">SENS:COUN:GATE:APER</span><span class="briefScpi">Sets the width of a single bin</span>
      </div>
      <div class="content">
        <div class="scpiLong">SENSe:COUNt:GATE:APERture <div class="tooltip" data-id="6">&lt;time&gt;</div></div>
        <div class="paramDocu">Parameters<ul>
              <li id="6"><span class="param">time</span><span class="paramType">float</span> <span class="paramDesc">bin width in Seconds.</span></li>
        </ul></div>

        <div class="details">Call this function to change the width of a single bin in Seconds. The bin width defines the time span where edge counts of input signals are counted. This active time span might be followed by an optional dead time <a class="linkSCPI" title="Sets counter dead time" href="#SENS:COUN:GATE:DELA">SENS:COUN:GATE:DELA</a>. The bin width might be controlled externally by software or hardware. To use this external binning set <a class="linkSCPI" title="Sets the width of a single bin" href="#SENS:COUN:GATE:APER">SENS:COUN:GATE:APER</a> to 0.  Time resolution is one millisecond.</div>
        <div class="example">&gt;&gt; SENS:COUN:GATE:APER 0.01</div>

      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="SENS">
        <span class="scpi" id="SENS:COUN:GATE:APER?">SENS:COUN:GATE:APER?</span><span class="briefScpi">Queries the width of a single bin</span>
      </div>
      <div class="content">
        <div class="scpiLong">SENSe:COUNt:GATE:APERture?</div>
        <div class="resultDocu">Response<ul>
          <li><span class="param">length</span><span class="paramType">float</span> <span class="paramDesc">bin width in Seconds.</span></li>
        </ul></div>

        <div class="details">Call this function to read the width of a single bin in Seconds. For closer info read <a class="linkSCPI" title="Sets the width of a single bin" href="#SENS:COUN:GATE:APER">SENS:COUN:GATE:APER</a>.</div>
        <div class="example">&gt;&gt; SENS:COUN:GATE:APER?<br />&lt;&lt; 0.5</div>

      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="SENS">
        <span class="scpi" id="SENS:COUN:GATE:DELA">SENS:COUN:GATE:DELA</span><span class="briefScpi">Sets counter dead time</span>
      </div>
      <div class="content">
        <div class="scpiLong">SENSe:COUNt:GATE:DELAy <div class="tooltip" data-id="7">&lt;time&gt;</div></div>
        <div class="paramDocu">Parameters<ul>
              <li id="7"><span class="param">time</span><span class="paramType">float</span> <span class="paramDesc">counting pause time in Seconds. Set to 0 to disable pause.</span></li>
        </ul></div>

        <div class="details">Use this command to set the dead time of the counter in Seconds (Pause). This optional delay stops counter after collecting counts in an active phase. During pause the counter is disabled and gets enabled if delay passed automatically. By default this delay is not used. The dead time is not applied for manual software or hardware triggered binning. For closer details read <a class="linkSCPI" title="Sets the width of a single bin" href="#SENS:COUN:GATE:APER">SENS:COUN:GATE:APER</a>. Time resolution is one millisecond.</div>
        <div class="example">&gt;&gt; SENS:COUN:GATE:DEL 0.1</div>

      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="SENS">
        <span class="scpi" id="SENS:COUN:GATE:DELA?">SENS:COUN:GATE:DELA?</span><span class="briefScpi">Queries counter dead time</span>
      </div>
      <div class="content">
        <div class="scpiLong">SENSe:COUNt:GATE:DELAy?</div>
        <div class="resultDocu">Response<ul>
          <li><span class="param">time</span><span class="paramType">float</span> <span class="paramDesc">counting pause time in Seconds</span></li>
        </ul></div>

        <div class="details">Use this command to read the dead time of the counter in Seconds (Pause). This optional delay stops counter after collecting counts in an active phase. For closer details read <a class="linkSCPI" title="Sets counter dead time" href="#SENS:COUN:GATE:DELA">SENS:COUN:GATE:DELA</a>.</div>
        <div class="example">&gt;&gt; SENS:COUN:GATE:DEL?<br />&lt;&lt; 0.25</div>

      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="SENS">
        <span class="scpi" id="SENS:FREQ:COLL">SENS:FREQ:COLL</span><span class="briefScpi">Starts software defined frequency count bin</span>
      </div>
      <div class="content">
        <div class="scpiLong">SENSe:FREQuency:COLLect[:INITiate]</div>
        <div class="details">Use this command to start frequency counting in a software defined bin. For this mode ensure the <a class="linkSCPI" title="Sets the width of a single bin" href="#SENS:COUN:GATE:APER">SENS:COUN:GATE:APER</a> is configured as 0 and no external Trigger input signal is present. Once started the counter starts a new counting bin until <a class="linkSCPI" title="Ends software defined frequency count bin" href="#SENS:FREQ:COLL:ABOR">SENS:FREQ:COLL:ABOR</a> will be called. During this time span all pulses are counted. The maximal time span is 6 Seconds. Call <a class="linkSCPI" title="Returns the width of the last bin in Seconds" href="#MEAS:TIME?">MEAS:TIME?</a> to get the width of the time span. You can use <a class="linkSCPI" title="Returns recently measured absolute or relative frequency in Hz" href="#MEAS:FREQ?">MEAS:FREQ?</a> and <a class="linkSCPI" title="Returns recently measured absolute or relative counts" href="#MEAS?">MEAS?</a> to query the results of this measurement once completed. During measurement the counter values are not accessible. If you call <a class="linkSCPI" title="Starts software defined frequency count bin" href="#SENS:FREQ:COLL">SENS:FREQ:COLL</a> followed by <a class="linkSCPI" title="Ends software defined frequency count bin" href="#SENS:FREQ:COLL:ABOR">SENS:FREQ:COLL:ABOR</a> you can also use <a class="linkSCPI" title="Returns a list of bin counts as binary data" href="#MEAS:ARR:COUN?">MEAS:ARR:COUN?</a>. If manual mode is not configured or counter already started this command will cause an error.</div>
        <div class="example">&gt;&gt; SENS:FREQ:COLL</div>

      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="SENS">
        <span class="scpi" id="SENS:FREQ:COLL:ABOR">SENS:FREQ:COLL:ABOR</span><span class="briefScpi">Ends software defined frequency count bin</span>
      </div>
      <div class="content">
        <div class="scpiLong">SENSe:FREQuency:COLLect:ABORt</div>
        <div class="details">Use this command to end frequency counting in a software defined bin. For this mode ensure the <a class="linkSCPI" title="Sets the width of a single bin" href="#SENS:COUN:GATE:APER">SENS:COUN:GATE:APER</a> is configured as 0 and no external Trigger input signal is present. Once started by command the counter starts a new counting bin until this SCPI command is called. For closer details read <a class="linkSCPI" title="Starts software defined frequency count bin" href="#SENS:FREQ:COLL">SENS:FREQ:COLL</a>. If manual mode is not configured or counter already stopped this command will cause an error.</div>
        <div class="example">&gt;&gt; SENS:FREQ:COLL:ABOR</div>

      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="SENS">
        <span class="scpi" id="SENS:FREQ:COLL:STAT?">SENS:FREQ:COLL:STAT?</span><span class="briefScpi">Tests if counter is currently collecting data</span>
      </div>
      <div class="content">
        <div class="scpiLong">SENSe:FREQuency:COLLect:STATe?</div>
        <div class="resultDocu">Response<ul>
          <li><span class="param">state</span><span class="paramType">bool</span> <span class="paramDesc">state of counter peripheral.</span></li>
        </ul></div>

        <div class="example">&gt;&gt; SENS:FREQ:COLL:STAT?<br />&lt;&lt; 1</div>

      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="SENS">
        <span class="scpi" id="SENS:FREQ:THR">SENS:FREQ:THR</span><span class="briefScpi">Sets the counter input threshold</span>
      </div>
      <div class="content">
        <div class="scpiLong">SENSe:FREQuency:THReshold  <div class="tooltip" data-id="4">&lt;offset&gt;</div></div>
        <div class="paramDocu">Parameters<ul>
              <li id="4"><span class="param">offset</span><span class="paramType">uint</span> <span class="paramDesc">offset between 0 and 100 percent.</span></li>
        </ul></div>

        <div class="details">Use this command to set the relative input threshold voltage for the Schmitt-Trigger. The counter hardware compares the input signal with this offset to detect edges in the input signal. For Thorlabs SPDMA this offset is already configured appropriately. The offset is given percent and is relative to 1.6 V  (the input has an impedance of 50 Ohms).  If the offset is to high this counter can not count edges of the input signal.</div>
        <div class="example">&gt;&gt; SENS:FREQ:THR 15</div>

      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="SENS">
        <span class="scpi" id="SENS:FREQ:THR?">SENS:FREQ:THR?</span><span class="briefScpi">Queries the counter input threshold</span>
      </div>
      <div class="content">
        <div class="scpiLong">SENSe:FREQuency:THReshold?</div>
        <div class="resultDocu">Response<ul>
          <li><span class="param">offset</span><span class="paramType">uint</span> <span class="paramDesc">offset between 0 and 100 percent.</span></li>
        </ul></div>

        <div class="details">Use this command to read the relative input threshold voltage for the Schmitt-Trigger. For closer details read <a class="linkSCPI" title="Sets the counter input threshold" href="#SENS:FREQ:THR">SENS:FREQ:THR</a>.</div>
        <div class="example">&gt;&gt; SENS:FREQ:THR?<br />&lt;&lt; 40</div>

      </div>
    </div>
    <hr>
    <p id="STAT"><a href="#STAT:OPER?" data-subsystem="STAT" class="unfoldSection">Unfold all STAT Commands</a></p>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="STAT">
        <span class="scpi" id="STAT:OPER?">STAT:OPER?</span><span class="briefScpi">Queries the SCPI Operation Event register</span>
      </div>
      <div class="content">
        <div class="scpiLong">STATus:OPERation[:EVENt]?</div>
        <div class="resultDocu">Response<ul>
          <li><span class="param">bitmask</span><span class="paramType">uint</span> <span class="paramDesc">16 bit wide event register</span></li>
        </ul></div>

        <div class="details">Use this command to query the SCPI 16 bit wide Operation Event register bit mask. The operation register set monitors the operating state of the measurement system.<br/> Reading this event register automatically resets the register back to 0. The register content is a bit mask with a 1 bit for every event occurred since the register has been read the last time. If the same event occurs multiple times the event bit stays 1. To get the meaning of the single bits read the <a class="linkSCPI" title="Queries the SCPI Operation Condition register" href="#STAT:OPER:COND?">STAT:OPER:COND?</a> description. The event register can store a change of the Operation Condition register. The two transition registers are used to mask changes of the condition register to the event register. If a certain bit in the condition register is set from 0 to 1 and the bit at the same position in the positive transition register <a class="linkSCPI" title="Sets the SCPI Operation Positive Transition register" href="#STAT:OPER:PTR">STAT:OPER:PTR</a> is set 1, the event register at the bit position is set to 1. It is also possible to detect condition register changes from 1 back to 0 using the negative transition register <a class="linkSCPI" title="Sets the SCPI Operation Negative Transition register" href="#STAT:OPER:NTR">STAT:OPER:NTR</a> following the same logic. <br/> Using the event register makes sense for short lasting conditions like a trigger event for example. Changes of the event register can be summarized to the SCPI status register bit 7 if enabled by <a class="linkSCPI" title="Sets the SCPI Operation Enable register" href="#STAT:OPER:ENAB">STAT:OPER:ENAB</a>. To read the SCPI status register call <a class="linkSCPI" title="Status byte query" href="#STB?">STB?</a>. After reboot or <a class="linkSCPI" title="Clears all SCPI event status registers" href="#CLS">CLS</a> the events are all reset to 0.<br/> Read <a href="#statusRegisterSetDesc" title="General SCPI Status register description">SCPI register set</a> section to get closer infos about the SCPI register system in general.</div>
        <div class="example">&gt;&gt; STAT:OPER?<br />&lt;&lt; 253</div>
        <div class="note">Some events might get lost if same event occurs multiple times without the event register being read in between.</div>
        <div class="info">Reading the event register automatically resets register to 0. To get the events bit positions read <a class="linkSCPI" title="Queries the SCPI Operation Condition register" href="#STAT:OPER:COND?">STAT:OPER:COND?</a>.</div>
      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="STAT">
        <span class="scpi" id="STAT:OPER:COND?">STAT:OPER:COND?</span><span class="briefScpi">Queries the SCPI Operation Condition register</span>
      </div>
      <div class="content">
        <div class="scpiLong">STATus:OPERation:CONDition?</div>
        <div class="resultDocu">Response<ul>
          <li><span class="param">bitmask</span><span class="paramType">uint</span> <span class="paramDesc">16 bit wide condition register.</span></li>
        </ul></div>

        <div class="details">Use this command to query the SCPI 16 bit wide Operation Condition register bit mask. The operation register set monitors the operating state of the measurement system.<br/> The register content is a bit mask with a 1 bit for every condition being active at the moment. Be aware some conditions might only last for a very short time period like a trigger event for example. In this case using the event register <a class="linkSCPI" title="Queries the SCPI Operation Event register" href="#STAT:OPER?">STAT:OPER?</a> makes more sense. Configure the positive <a class="linkSCPI" title="Sets the SCPI Operation Positive Transition register" href="#STAT:OPER:PTR">STAT:OPER:PTR</a> or negative <a class="linkSCPI" title="Sets the SCPI Operation Negative Transition register" href="#STAT:OPER:NTR">STAT:OPER:NTR</a> transition register to map changes of the condition register to the event register. These are the masked events:<br/>  <svg width="200px" viewBox="1820 1526 283 327" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">  <g>    <rect style="fill: #ffffff" x="1961.36" y="1528.03" width="140" height="324"/>    <rect style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x="1961.36" y="1528.03" width="140" height="324"/>    <text font-size="14.1111" style="fill: #000000;text-anchor:end;font-family:sans-serif;font-style:normal;font-weight:700" x="2092.36" y="1685.49">      <tspan x="2092.36" y="1685.49">STAT:</tspan>      <tspan x="2092.36" y="1703.13">OPER</tspan>    </text>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1821.36" y="1538.03">    <tspan x="1821.36" y="1538.03">reserved</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1821.36" y="1558.03">    <tspan x="1821.36" y="1558.03">reserved</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1821.36" y="1578.03">    <tspan x="1821.36" y="1578.03">reserved</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="1841.36" y1="1540.03" x2="1956.89" y2="1540.03"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1949.13,1545.03 1959.13,1540.03 1949.13,1535.03 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1965.36" y="1544.03">    <tspan x="1965.36" y="1544.03">bit 0(1)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1821.36" y="1598.03">    <tspan x="1821.36" y="1598.03">reserved</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="1841.36" y1="1560.03" x2="1956.89" y2="1560.03"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1949.13,1565.03 1959.13,1560.03 1949.13,1555.03 "/>  </g>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="1841.36" y1="1580.03" x2="1956.89" y2="1580.03"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1949.13,1585.03 1959.13,1580.03 1949.13,1575.03 "/>  </g>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="1841.36" y1="1600.03" x2="1956.89" y2="1600.03"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1949.13,1605.03 1959.13,1600.03 1949.13,1595.03 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1820.06" y="1618.03">    <tspan x="1820.06" y="1618.03">Counter active</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="1841.36" y1="1620.03" x2="1956.89" y2="1620.03"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1949.13,1625.03 1959.13,1620.03 1949.13,1615.03 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1821.36" y="1638.03">    <tspan x="1821.36" y="1638.03">External Trigger</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="1841.36" y1="1640.03" x2="1956.89" y2="1640.03"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1949.13,1645.03 1959.13,1640.03 1949.13,1635.03 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1821.36" y="1658.03">    <tspan x="1821.36" y="1658.03">resvered</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="1841.36" y1="1660.03" x2="1956.89" y2="1660.03"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1949.13,1665.03 1959.13,1660.03 1949.13,1655.03 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1821.36" y="1678.03">    <tspan x="1821.36" y="1678.03">Zeroing</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="1841.36" y1="1680.03" x2="1956.89" y2="1680.03"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1949.13,1685.03 1959.13,1680.03 1949.13,1675.03 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1821.36" y="1698.03">    <tspan x="1821.36" y="1698.03">Bin counter active</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="1841.36" y1="1700.03" x2="1956.89" y2="1700.03"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1949.13,1705.03 1959.13,1700.03 1949.13,1695.03 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1821.36" y="1718.03">    <tspan x="1821.36" y="1718.03">Freqency to fetch</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="1841.36" y1="1720.03" x2="1956.89" y2="1720.03"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1949.13,1725.03 1959.13,1720.03 1949.13,1715.03 "/>  </g>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="1841.36" y1="1740.03" x2="1956.89" y2="1740.03"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1949.13,1745.03 1959.13,1740.03 1949.13,1735.03 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1821.36" y="1758.03">    <tspan x="1821.36" y="1758.03">Array to fetch</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="1841.36" y1="1760.03" x2="1956.89" y2="1760.03"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1949.13,1765.03 1959.13,1760.03 1949.13,1755.03 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1821.36" y="1778.03">    <tspan x="1821.36" y="1778.03">reserved</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="1841.36" y1="1780.03" x2="1956.89" y2="1780.03"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1949.13,1785.03 1959.13,1780.03 1949.13,1775.03 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1821.36" y="1838.03">    <tspan x="1821.36" y="1838.03">reserved</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="1841.36" y1="1840.03" x2="1956.89" y2="1840.03"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1949.13,1845.03 1959.13,1840.03 1949.13,1835.03 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1965.36" y="1564.03">    <tspan x="1965.36" y="1564.03">bit 1(2)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1965.36" y="1584.03">    <tspan x="1965.36" y="1584.03">bit 2(4)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1965.36" y="1604.03">    <tspan x="1965.36" y="1604.03">bit 3(8)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1965.36" y="1624.03">    <tspan x="1965.36" y="1624.03">bit 4(16)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1965.36" y="1644.03">    <tspan x="1965.36" y="1644.03">bit 5(32)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1965.36" y="1664.03">    <tspan x="1965.36" y="1664.03">bit 6(64)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1965.36" y="1684.03">    <tspan x="1965.36" y="1684.03">bit 7(128)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1965.36" y="1704.03">    <tspan x="1965.36" y="1704.03">bit 8(256)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1965.36" y="1724.03">    <tspan x="1965.36" y="1724.03">bit 9(512)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1965.36" y="1744.03">    <tspan x="1965.36" y="1744.03">bit 10(1024)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1965.36" y="1764.03">    <tspan x="1965.36" y="1764.03">bit 11(2048)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1965.36" y="1784.03">    <tspan x="1965.36" y="1784.03">bit 12(4096)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1965.36" y="1804.03">    <tspan x="1965.36" y="1804.03">bit 13(8192)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1965.36" y="1824.03">    <tspan x="1965.36" y="1824.03">bit 14(16384)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1965.36" y="1844.03">    <tspan x="1965.36" y="1844.03">bit 15(32768)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1821.36" y="1736.03">    <tspan x="1821.36" y="1736.03">Counts to fetch</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1821.36" y="1798.03">    <tspan x="1821.36" y="1798.03">reserved</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="1841.36" y1="1800.03" x2="1956.89" y2="1800.03"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1949.13,1805.03 1959.13,1800.03 1949.13,1795.03 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1821.36" y="1818.03">    <tspan x="1821.36" y="1818.03">reserved</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="1841.36" y1="1820.03" x2="1956.89" y2="1820.03"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1949.13,1825.03 1959.13,1820.03 1949.13,1815.03 "/>  </g></svg> <ul class="detailListing"> <li><b>bit 4:</b> Counter is active condition.</li> <li><b>bit 5:</b> External trigger condition.</li> <li><b>bit 7:</b> Zeroing algorithm running. See <a class="linkSCPI" title="Starts zeroing of counter" href="#SENS:CORR:COLL:ZERO">SENS:CORR:COLL:ZERO</a>.</li> <li><b>bit 8:</b> Binning time base counter active condition.</li> <li><b>bit 9:</b> Frequency data ready to fetch condition. See <a class="linkSCPI" title="Returns recently measured absolute or relative frequency in Hz" href="#MEAS:FREQ?">MEAS:FREQ?</a>.</li> <li><b>bit 10:</b> Count data ready to fetch condition. See <a class="linkSCPI" title="Returns recently measured absolute or relative counts" href="#MEAS?">MEAS?</a>.</li> <li><b>bit 11:</b> Array data ready to fetch condition. See <a class="linkSCPI" title="Returns a list of bin counts as binary data" href="#MEAS:ARR:COUN?">MEAS:ARR:COUN?</a>.</li> </ul> All other condition bits are reserved. Read <a href="#statusRegisterSetDesc" title="General SCPI Status register description">SCPI register set</a> section to get closer infos about the SCPI register system in general.</div>
        <div class="example">&gt;&gt; STAT:OPER:COND?<br />&lt;&lt; 2182</div>
        <div class="info">To test for short lasting conditions like a range change use the event register <a class="linkSCPI" title="Queries the SCPI Operation Event register" href="#STAT:OPER?">STAT:OPER?</a>.</div>
      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="STAT">
        <span class="scpi" id="STAT:OPER:ENAB">STAT:OPER:ENAB</span><span class="briefScpi">Sets the SCPI Operation Enable register</span>
      </div>
      <div class="content">
        <div class="scpiLong">STATus:OPERation:ENABle <div class="tooltip" data-id="8">&lt;bitmask&gt;</div></div>
        <div class="paramDocu">Parameters<ul>
              <li id="8"><span class="param">bitmask</span><span class="paramType">uint</span> <span class="paramDesc">16 bit wide bit mask for enable register.</span></li>
        </ul></div>

        <div class="details">Use this command to set the SCPI 16 bit wide Operation Enable register bit mask. The register content is a bit mask with a 1 bit for every enabled event that should be forwarded to the 8 bit wide SCPI status register bit 7. See <a class="linkSCPI" title="Status byte query" href="#STB?">STB?</a>. To get the meaning of the single bits read the <a class="linkSCPI" title="Queries the SCPI Operation Condition register" href="#STAT:OPER:COND?">STAT:OPER:COND?</a> description. This enable register acts like a filter mask for all operation events (see <a class="linkSCPI" title="Queries the SCPI Operation Event register" href="#STAT:OPER?">STAT:OPER?</a>) that should be forwarded to the SCPI status register. If the enable mask is set with all bits 1, the SCPI status register bit 7 is set to 1 whenever one or multiple events are set in <a class="linkSCPI" title="Queries the SCPI Operation Event register" href="#STAT:OPER?">STAT:OPER?</a> register.<br/> The idea behind this abstraction concept is to read the SCPI status register and check for bit 7 to change. Whenever the bit changed the user then may query this Operation Event register to get the details. Changing the enable register with active events might directly update the summarized SCPI status register bit.<br/> Read <a href="#statusRegisterSetDesc" title="General SCPI Status register description">SCPI register set</a> section to get closer infos about the SCPI register system in general.</div>
        <div class="example">&gt;&gt; STAT:OPER:ENAB 65280</div>
        <div class="note">Ensure that the positive and negative transition registers are set accordingly to change the event register. Forwarding only works if event register gets updated.</div>
        <div class="info">The default value after reboot or <a class="linkSCPI" title="Clears all SCPI event status registers" href="#CLS">CLS</a> is 0. So no events are forwarded.</div>
      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="STAT">
        <span class="scpi" id="STAT:OPER:ENAB?">STAT:OPER:ENAB?</span><span class="briefScpi">Queries the SCPI Operation Enable register</span>
      </div>
      <div class="content">
        <div class="scpiLong">STATus:OPERation:ENABle?</div>
        <div class="resultDocu">Response<ul>
          <li><span class="param">bitmask</span><span class="paramType">uint</span> <span class="paramDesc">16 bit wide enable register.</span></li>
        </ul></div>

        <div class="details">Use this command to query the SCPI 16 bit wide Operation Enable register bit mask. The register content is a bit mask with a 1 bit for every enabled event that should be forwarded to the 8 bit wide SCPI status register bit 7. To get the meaning of the single bits read the <a class="linkSCPI" title="Queries the SCPI Operation Condition register" href="#STAT:OPER:COND?">STAT:OPER:COND?</a> description. For closer details about the Operation Enable register read <a class="linkSCPI" title="Sets the SCPI Operation Enable register" href="#STAT:OPER:ENAB">STAT:OPER:ENAB</a>.<br/> Read <a href="#statusRegisterSetDesc" title="General SCPI Status register description">SCPI register set</a> section to get closer infos about the SCPI register system in general.</div>
        <div class="example">&gt;&gt; STAT:OPER:ENAB?<br />&lt;&lt; 65280</div>
        <div class="note">Ensure that the positive or negative transition registers are set accordingly to change the event register. Forwarding only works if event register gets updated.</div>
        <div class="info">The default value after reboot or <a class="linkSCPI" title="Clears all SCPI event status registers" href="#CLS">CLS</a> is 0. So no events are forwarded.</div>
      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="STAT">
        <span class="scpi" id="STAT:OPER:NTR">STAT:OPER:NTR</span><span class="briefScpi">Sets the SCPI Operation Negative Transition register</span>
      </div>
      <div class="content">
        <div class="scpiLong">STATus:OPERation:NTRansition <div class="tooltip" data-id="10">&lt;bitmask&gt;</div></div>
        <div class="paramDocu">Parameters<ul>
              <li id="10"><span class="param">bitmask</span><span class="paramType">uint</span> <span class="paramDesc">16 bit wide bit mask for positive transition register.</span></li>
        </ul></div>

        <div class="details">Use this command to set the SCPI 16 bit wide Operation Negative Transition register bit mask. The register content is a bit mask with a 1 bit for every condition change that should be mapped to the operation event register <a class="linkSCPI" title="Queries the SCPI Operation Event register" href="#STAT:OPER?">STAT:OPER?</a>. As it is the negative transition register, only condition register bit changes from 1 back to 0 update the bits in the event register. To get the meaning of the single bits read the <a class="linkSCPI" title="Queries the SCPI Operation Condition register" href="#STAT:OPER:COND?">STAT:OPER:COND?</a> description. For example if you set this register value to 128 (binary: 10000000 bit at position 7) and zeroing terminates (condition register at position 7 changes back from 1 to 0) it will also set the event register value bit at position 7 to 1.<br/> Read <a href="#statusRegisterSetDesc" title="General SCPI Status register description">SCPI register set</a> section to get closer infos about the SCPI register system in general.</div>
        <div class="example">&gt;&gt; STAT:OPER:NTR 260</div>
        <div class="info">The default value after reboot or <a class="linkSCPI" title="Clears all SCPI event status registers" href="#CLS">CLS</a> is 0. So no condition changes from bit 1 to 0 update the event register.</div>
      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="STAT">
        <span class="scpi" id="STAT:OPER:NTR?">STAT:OPER:NTR?</span><span class="briefScpi">Queries the SCPI Operation Negative Transition register</span>
      </div>
      <div class="content">
        <div class="scpiLong">STATus:OPERation:NTRansition?</div>
        <div class="resultDocu">Response<ul>
          <li><span class="param">bitmask</span><span class="paramType">uint</span> <span class="paramDesc">16 bit wide bit mask for positive transition register.</span></li>
        </ul></div>

        <div class="details">Use this command to query the SCPI 16 bit wide Operation Negative Transition register bit mask. The register content is a bit mask with a 1 bit for every condition change that should be mapped to the operation event register <a class="linkSCPI" title="Queries the SCPI Operation Event register" href="#STAT:OPER?">STAT:OPER?</a>. For closer details about Negative Transition register read description <a class="linkSCPI" title="Sets the SCPI Operation Negative Transition register" href="#STAT:OPER:NTR">STAT:OPER:NTR</a>.<br/> Read <a href="#statusRegisterSetDesc" title="General SCPI Status register description">SCPI register set</a> section to get closer infos about the SCPI register system in general.</div>
        <div class="example">&gt;&gt; STAT:OPER:NTR?<br />&lt;&lt; 260</div>
        <div class="info">The default value after reboot or <a class="linkSCPI" title="Clears all SCPI event status registers" href="#CLS">CLS</a> is 0. So no condition changes from bit 1 to 0 update the event register.</div>
      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="STAT">
        <span class="scpi" id="STAT:OPER:PTR">STAT:OPER:PTR</span><span class="briefScpi">Sets the SCPI Operation Positive Transition register</span>
      </div>
      <div class="content">
        <div class="scpiLong">STATus:OPERation:PTRansition <div class="tooltip" data-id="9">&lt;bitmask&gt;</div></div>
        <div class="paramDocu">Parameters<ul>
              <li id="9"><span class="param">bitmask</span><span class="paramType">uint</span> <span class="paramDesc">16 bit wide bit mask for positive transition register.</span></li>
        </ul></div>

        <div class="details">Use this command to set the SCPI 16 bit wide Operation Positive Transition register bit mask. The register content is a bit mask with a 1 bit for every condition change that should be mapped to the Operation Event register <a class="linkSCPI" title="Queries the SCPI Operation Event register" href="#STAT:OPER?">STAT:OPER?</a>. As it is the positive transition register, only condition register bit changes from 0 to 1 update the bits in the event register. To get the meaning of the single bits read the <a class="linkSCPI" title="Queries the SCPI Operation Condition register" href="#STAT:OPER:COND?">STAT:OPER:COND?</a> description. For example if you set this register value to 512 (binary: 1000000000 bit at position 9) and count measurement data is ready to fetch (condition register at position 9 changes back from 0 to 1) it will also set the event register value bit at position 9 to 1.<br/> Read <a href="#statusRegisterSetDesc" title="General SCPI Status register description">SCPI register set</a> section to get closer infos about the SCPI register system in general.</div>
        <div class="example">&gt;&gt; STAT:OPER:PTR 36</div>
        <div class="info">The default value after reboot or <a class="linkSCPI" title="Clears all SCPI event status registers" href="#CLS">CLS</a> is 65535 (all bits 1). So all condition changes from bit 0 to 1 update the event register.</div>
      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="STAT">
        <span class="scpi" id="STAT:OPER:PTR?">STAT:OPER:PTR?</span><span class="briefScpi">Queries the SCPI Operation Positive Transition register</span>
      </div>
      <div class="content">
        <div class="scpiLong">STATus:OPERation:PTRansition?</div>
        <div class="resultDocu">Response<ul>
          <li><span class="param">bitmask</span><span class="paramType">uint</span> <span class="paramDesc">16 bit wide bit mask for positive transition register.</span></li>
        </ul></div>

        <div class="details">Use this command to query the SCPI 16 bit wide Operation Positive Transition register bit mask. The register content is a bit mask with a 1 bit for every condition change that should be mapped to the operation event register <a class="linkSCPI" title="Queries the SCPI Operation Event register" href="#STAT:OPER?">STAT:OPER?</a>. For closer details about Positive Transition register read description <a class="linkSCPI" title="Sets the SCPI Operation Positive Transition register" href="#STAT:OPER:PTR">STAT:OPER:PTR</a>.<br/> Read <a href="#statusRegisterSetDesc" title="General SCPI Status register description">SCPI register set</a> section to get closer infos about the SCPI register system in general.</div>
        <div class="example">&gt;&gt; STAT:OPER:PTR?<br />&lt;&lt; 65280</div>
        <div class="info">The default value after reboot or <a class="linkSCPI" title="Clears all SCPI event status registers" href="#CLS">CLS</a> is 65535 (all bits 1). So all condition changes from bit 0 to 1 update the event register.</div>
      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="STAT">
        <span class="scpi" id="STAT:PRES">STAT:PRES</span><span class="briefScpi">Pre-sets all SCPI status register with default values</span>
      </div>
      <div class="content">
        <div class="scpiLong">STATus:PRESet</div>
        <div class="details">Use this command to pre set all SCPI status registers with default values.  All ENABle and NTRansition Registers are set to 0. The PTRansition registers are set to 65535. The event registers are cleared to 0.</div>
        <div class="example">&gt;&gt; STAT:PRES</div>

      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="STAT">
        <span class="scpi" id="STAT:QUES?">STAT:QUES?</span><span class="briefScpi">Queries the SCPI Questionable Event register</span>
      </div>
      <div class="content">
        <div class="scpiLong">STATus:QUEStionable[:EVENt]?</div>
        <div class="resultDocu">Response<ul>
          <li><span class="param">bitmask</span><span class="paramType">uint</span> <span class="paramDesc">16 bit wide event register bit mask.</span></li>
        </ul></div>

        <div class="details">Use this command to query the SCPI 16 bit wide Questionable Event register bit mask. The Questionable register set monitors the validity of the measurement results.<br/> Reading this event register automatically resets the register back to 0. The register content is a bit mask with a 1 bit for every event occurred since the register has been read the last time. If the same event occurs multiple times the event bit stays 1. To get the meaning of the single bits read the <a class="linkSCPI" title="Queries the SCPI Questionable Condition register" href="#STAT:QUES:COND?">STAT:QUES:COND?</a> description. The event register can store a change of the Questionable Condition register. The two transition registers are used to mask changes of the condition register to the event register. If a certain bit in the condition register is set from 0 to 1 and the bit at the same position in the positive transition register <a class="linkSCPI" title="Queries the SCPI Questionable Positive Transition register" href="#STAT:QUES:PTR">STAT:QUES:PTR</a> is set 1, the event register at the bit position is set to 1. It is also possible to detect condition register changes from 1 back to 0 using the negative transition register <a class="linkSCPI" title="Sets the SCPI Questionable Negative Transition register" href="#STAT:QUES:NTR">STAT:QUES:NTR</a> following the same logic. <br/> Using the event register makes sense for short lasting conditions. Changes of the event register can be summarized to the SCPI status register bit 3 if enabled by <a class="linkSCPI" title="Sets the SCPI Questionable Enable register" href="#STAT:QUES:ENAB">STAT:QUES:ENAB</a>. To read the SCPI status register call <a class="linkSCPI" title="Status byte query" href="#STB?">STB?</a>. After reboot or <a class="linkSCPI" title="Clears all SCPI event status registers" href="#CLS">CLS</a> the events are all reset to 0.<br/> Read <a href="#statusRegisterSetDesc" title="General SCPI Status register description">SCPI register set</a> section to get closer infos about the SCPI register system in general.</div>
        <div class="example">&gt;&gt; STAT:QUES?<br />&lt;&lt; 253</div>
        <div class="note">Some events might get lost if same event occurs multiple times without the event register being read in between.</div>
        <div class="info">Reading the event register automatically resets register to 0. To get the events bit positions read <a class="linkSCPI" title="Queries the SCPI Questionable Condition register" href="#STAT:QUES:COND?">STAT:QUES:COND?</a>.</div>
      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="STAT">
        <span class="scpi" id="STAT:QUES:COND?">STAT:QUES:COND?</span><span class="briefScpi">Queries the SCPI Questionable Condition register</span>
      </div>
      <div class="content">
        <div class="scpiLong">STATus:QUEStionable:CONDition?</div>
        <div class="resultDocu">Response<ul>
          <li><span class="param">bitmask</span><span class="paramType">uint</span> <span class="paramDesc">16 bit wide condition register bit mask.</span></li>
        </ul></div>

        <div class="details">Use this command to query the SCPI 16 bit wide Questionable Condition register bit mask. The Questionable register set monitors the validity of the measurement results.<br/> The register content is a bit mask with a 1 bit for every condition being active at the moment. Be aware some conditions might only last for a very short time period. In this case using the event register <a class="linkSCPI" title="Queries the SCPI Questionable Event register" href="#STAT:QUES?">STAT:QUES?</a> makes more sense. Configure the positive <a class="linkSCPI" title="Queries the SCPI Questionable Positive Transition register" href="#STAT:QUES:PTR">STAT:QUES:PTR</a> or negative <a class="linkSCPI" title="Sets the SCPI Questionable Negative Transition register" href="#STAT:QUES:NTR">STAT:QUES:NTR</a> transition register to map changes of the condition register to the event register. These are the masked events:<br/> <svg width="200px" viewBox="1827 1092 282 327" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">  <g>    <rect style="fill: #ffffff" x="1967.78" y="1094.67" width="140" height="324"/>    <rect style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x="1967.78" y="1094.67" width="140" height="324"/>    <text font-size="14.1111" style="fill: #000000;text-anchor:end;font-family:sans-serif;font-style:normal;font-weight:700" x="2098.78" y="1252.13">      <tspan x="2098.78" y="1252.13">STAT:</tspan>      <tspan x="2098.78" y="1269.77">QUEST</tspan>    </text>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1827.78" y="1104.67">    <tspan x="1827.78" y="1104.67">reserved</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1827.78" y="1124.67">    <tspan x="1827.78" y="1124.67">reserved</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1827.78" y="1144.67">    <tspan x="1827.78" y="1144.67">reserved</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="1847.78" y1="1106.67" x2="1963.3" y2="1106.67"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1955.54,1111.67 1965.54,1106.67 1955.54,1101.67 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1971.78" y="1110.67">    <tspan x="1971.78" y="1110.67">bit 0(1)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1827.78" y="1164.67">    <tspan x="1827.78" y="1164.67">reserved</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="1847.78" y1="1126.67" x2="1963.3" y2="1126.67"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1955.54,1131.67 1965.54,1126.67 1955.54,1121.67 "/>  </g>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="1847.78" y1="1146.67" x2="1963.3" y2="1146.67"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1955.54,1151.67 1965.54,1146.67 1955.54,1141.67 "/>  </g>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="1847.78" y1="1166.67" x2="1963.3" y2="1166.67"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1955.54,1171.67 1965.54,1166.67 1955.54,1161.67 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1827.78" y="1184.67">    <tspan x="1827.78" y="1184.67">reserved</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="1847.78" y1="1186.67" x2="1963.3" y2="1186.67"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1955.54,1191.67 1965.54,1186.67 1955.54,1181.67 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1827.78" y="1204.67">    <tspan x="1827.78" y="1204.67">Questionable Frequ</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="1847.78" y1="1206.67" x2="1963.3" y2="1206.67"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1955.54,1211.67 1965.54,1206.67 1955.54,1201.67 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1827.78" y="1224.67">    <tspan x="1827.78" y="1224.67">reserved</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="1847.78" y1="1226.67" x2="1963.3" y2="1226.67"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1955.54,1231.67 1965.54,1226.67 1955.54,1221.67 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1827.78" y="1244.67">    <tspan x="1827.78" y="1244.67">reserved</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="1847.78" y1="1246.67" x2="1963.3" y2="1246.67"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1955.54,1251.67 1965.54,1246.67 1955.54,1241.67 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1827.78" y="1264.67">    <tspan x="1827.78" y="1264.67">Questionable Calibrat</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="1847.78" y1="1266.67" x2="1963.3" y2="1266.67"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1955.54,1271.67 1965.54,1266.67 1955.54,1261.67 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1827.78" y="1284.67">    <tspan x="1827.78" y="1284.67">Questionable Time</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="1847.78" y1="1286.67" x2="1963.3" y2="1286.67"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1955.54,1291.67 1965.54,1286.67 1955.54,1281.67 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1827.78" y="1304.67">    <tspan x="1827.78" y="1304.67">reserved</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="1847.78" y1="1306.67" x2="1963.3" y2="1306.67"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1955.54,1311.67 1965.54,1306.67 1955.54,1301.67 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1827.78" y="1324.67">    <tspan x="1827.78" y="1324.67">reserved</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="1847.78" y1="1326.67" x2="1963.3" y2="1326.67"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1955.54,1331.67 1965.54,1326.67 1955.54,1321.67 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1827.78" y="1344.67">    <tspan x="1827.78" y="1344.67">reserved</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="1847.78" y1="1346.67" x2="1963.3" y2="1346.67"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1955.54,1351.67 1965.54,1346.67 1955.54,1341.67 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1827.78" y="1404.67">    <tspan x="1827.78" y="1404.67">reserved</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="1847.78" y1="1406.67" x2="1963.3" y2="1406.67"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1955.54,1411.67 1965.54,1406.67 1955.54,1401.67 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1971.78" y="1130.67">    <tspan x="1971.78" y="1130.67">bit 1(2)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1971.78" y="1150.67">    <tspan x="1971.78" y="1150.67">bit 2(4)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1971.78" y="1170.67">    <tspan x="1971.78" y="1170.67">bit 3(8)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1971.78" y="1190.67">    <tspan x="1971.78" y="1190.67">bit 4(16)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1971.78" y="1210.67">    <tspan x="1971.78" y="1210.67">bit 5(32)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1971.78" y="1230.67">    <tspan x="1971.78" y="1230.67">bit 6(64)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1971.78" y="1250.67">    <tspan x="1971.78" y="1250.67">bit 7(128)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1971.78" y="1270.67">    <tspan x="1971.78" y="1270.67">bit 8(256)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1971.78" y="1290.67">    <tspan x="1971.78" y="1290.67">bit 9(512)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1971.78" y="1310.67">    <tspan x="1971.78" y="1310.67">bit 10(1024)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1971.78" y="1330.67">    <tspan x="1971.78" y="1330.67">bit 11(2048)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1971.78" y="1350.67">    <tspan x="1971.78" y="1350.67">bit 12(4096)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1971.78" y="1370.67">    <tspan x="1971.78" y="1370.67">bit 13(8192)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1971.78" y="1390.67">    <tspan x="1971.78" y="1390.67">bit 14(16384)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1971.78" y="1410.67">    <tspan x="1971.78" y="1410.67">bit 15(32768)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1827.78" y="1364.67">    <tspan x="1827.78" y="1364.67">reserved</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="1847.78" y1="1366.67" x2="1963.3" y2="1366.67"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1955.54,1371.67 1965.54,1366.67 1955.54,1361.67 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1827.78" y="1384.67">    <tspan x="1827.78" y="1384.67">reserved</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="1847.78" y1="1386.67" x2="1963.3" y2="1386.67"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1955.54,1391.67 1965.54,1386.67 1955.54,1381.67 "/>  </g></svg> <ul class="detailListing"> <li><b>bit 5:</b> Questionable Frequency measurement. <a class="linkSCPI" title="Returns recently measured absolute or relative frequency in Hz" href="#MEAS:FREQ?">MEAS:FREQ?</a> will return 0 as no signal detected.</li> <li><b>bit 8:</b> Questionable device calibration.</li> <li><b>bit 9:</b> Timer overflow detected. Counts, frequency and array data is invalid.</li> </ul> All other condition bits are reserved. Read <a href="#statusRegisterSetDesc" title="General SCPI Status register description">SCPI register set</a> section to get closer infos about the SCPI register system in general.</div>
        <div class="example">&gt;&gt; STAT:QUES:COND?<br />&lt;&lt; 2182</div>
        <div class="info">To test for short lasting conditions better use the event register <a class="linkSCPI" title="Queries the SCPI Questionable Event register" href="#STAT:QUES?">STAT:QUES?</a>.</div>
      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="STAT">
        <span class="scpi" id="STAT:QUES:ENAB">STAT:QUES:ENAB</span><span class="briefScpi">Sets the SCPI Questionable Enable register</span>
      </div>
      <div class="content">
        <div class="scpiLong">STATus:QUEStionable:ENABle <div class="tooltip" data-id="11">&lt;bitmask&gt;</div></div>
        <div class="paramDocu">Parameters<ul>
              <li id="11"><span class="param">bitmask</span><span class="paramType">uint</span> <span class="paramDesc">16 bit wide bit mask for enable register.</span></li>
        </ul></div>

        <div class="details">Use this command to set the SCPI 16 bit wide Questionable Enable register bit mask. The register content is a bit mask with a 1 bit for every enabled event that should be forwarded to the 8 bit wide SCPI status register bit 3. See <a class="linkSCPI" title="Status byte query" href="#STB?">STB?</a>. To get the meaning of the single bits read the <a class="linkSCPI" title="Queries the SCPI Questionable Condition register" href="#STAT:QUES:COND?">STAT:QUES:COND?</a> description. This enable register acts like a filter mask for all operation events (see <a class="linkSCPI" title="Queries the SCPI Questionable Event register" href="#STAT:QUES?">STAT:QUES?</a>) that should be forwarded to the SCPI status register. If the enable mask is set with all bits 1, the SCPI status register bit 3 is set to 1 whenever one or multiple events are set in <a class="linkSCPI" title="Queries the SCPI Questionable Event register" href="#STAT:QUES?">STAT:QUES?</a> register.<br/> The idea behind this abstraction concept is to read the SCPI status register and check for bit 3 to change. Whenever the bit changed the user then may query this Questionable Event register to get the details. Changing the enable register with active events might directly update the summarized SCPI status register bit.<br/>  Read <a href="#statusRegisterSetDesc" title="General SCPI Status register description">SCPI register set</a> section to get closer infos about the SCPI register system in general.</div>
        <div class="example">&gt;&gt; STAT:QUES:ENAB 65280</div>
        <div class="note">Ensure that the positive and negative transition registers are set accordingly to change the event register. Forwarding only works if event register gets updated.</div>
        <div class="info">The default value after reboot or <a class="linkSCPI" title="Clears all SCPI event status registers" href="#CLS">CLS</a> is 0. So no events are forwarded.</div>
      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="STAT">
        <span class="scpi" id="STAT:QUES:ENAB?">STAT:QUES:ENAB?</span><span class="briefScpi">Queries the SCPI Questionable Enable register</span>
      </div>
      <div class="content">
        <div class="scpiLong">STATus:QUEStionable:ENABle?</div>
        <div class="resultDocu">Response<ul>
          <li><span class="param">bitmask</span><span class="paramType">uint</span> <span class="paramDesc">16 bit wide enable register bit mask.</span></li>
        </ul></div>

        <div class="details">Use this command to query the SCPI 16 bit wide Questionable Enable register bit mask. The register content is a bit mask with a 1 bit for every enabled event that should be forwarded to the 8 bit wide SCPI status register bit 7. To get the meaning of the single bits read the <a class="linkSCPI" title="Queries the SCPI Questionable Condition register" href="#STAT:QUES:COND?">STAT:QUES:COND?</a> description. For closer details about the Questionable Enable register read <a class="linkSCPI" title="Sets the SCPI Questionable Enable register" href="#STAT:QUES:ENAB">STAT:QUES:ENAB</a>.<br/> Read <a href="#statusRegisterSetDesc" title="General SCPI Status register description">SCPI register set</a> section to get closer infos about the SCPI register system in general.</div>
        <div class="example">&gt;&gt; STAT:QUES:ENAB<br />&lt;&lt; 65280</div>
        <div class="note">Ensure that the positive or negative transition registers are set accordingly to change the event register. Forwarding only works if event register gets updated.</div>
        <div class="info">The default value after reboot or <a class="linkSCPI" title="Clears all SCPI event status registers" href="#CLS">CLS</a> is 0. So no events are forwarded.</div>
      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="STAT">
        <span class="scpi" id="STAT:QUES:NTR">STAT:QUES:NTR</span><span class="briefScpi">Sets the SCPI Questionable Negative Transition register</span>
      </div>
      <div class="content">
        <div class="scpiLong">STATus:QUEStionable:NTRansition <div class="tooltip" data-id="13">&lt;bitmask&gt;</div></div>
        <div class="paramDocu">Parameters<ul>
              <li id="13"><span class="param">bitmask</span><span class="paramType">uint</span> <span class="paramDesc">16 bit wide bit mask for positive transition register.</span></li>
        </ul></div>

        <div class="details">Use this command to set the SCPI 16 bit wide Questionable Negative Transition register bit mask. The register content is a bit mask with a 1 bit for every condition change that should be mapped to the operation event register <a class="linkSCPI" title="Queries the SCPI Questionable Event register" href="#STAT:QUES?">STAT:QUES?</a>. As it is the negative transition register, only condition register bit changes from 1 back to 0 update the bits in the event register. To get the meaning of the single bits read the <a class="linkSCPI" title="Queries the SCPI Questionable Condition register" href="#STAT:QUES:COND?">STAT:QUES:COND?</a> description.<br/> Read <a href="#statusRegisterSetDesc" title="General SCPI Status register description">SCPI register set</a> section to get closer infos about the SCPI register system in general.</div>
        <div class="example">&gt;&gt; STAT:QUES:NTR 260</div>
        <div class="info">The default value after reboot or <a class="linkSCPI" title="Clears all SCPI event status registers" href="#CLS">CLS</a> is 0. So no condition changes from bit 1 to 0 update the event register.</div>
      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="STAT">
        <span class="scpi" id="STAT:QUES:NTR?">STAT:QUES:NTR?</span><span class="briefScpi">Queries the SCPI Questionable Negative Transition register</span>
      </div>
      <div class="content">
        <div class="scpiLong">STATus:QUEStionable:NTRansition?</div>
        <div class="resultDocu">Response<ul>
          <li><span class="param">bitmask</span><span class="paramType">uint</span> <span class="paramDesc">16 bit wide bit mask for positive transition register.</span></li>
        </ul></div>

        <div class="details">Use this command to query the SCPI 16 bit wide Questionable Negative Transition register bit mask. The register content is a bit mask with a 1 bit for every condition change that should be mapped to the operation event register <a class="linkSCPI" title="Queries the SCPI Questionable Event register" href="#STAT:QUES?">STAT:QUES?</a>. For closer details about Negative Transition register read description <a class="linkSCPI" title="Sets the SCPI Questionable Negative Transition register" href="#STAT:QUES:NTR">STAT:QUES:NTR</a>.<br/> Read <a href="#statusRegisterSetDesc" title="General SCPI Status register description">SCPI register set</a> section to get closer infos about the SCPI register system in general.</div>
        <div class="example">&gt;&gt; STAT:QUES:NTR?<br />&lt;&lt; 260</div>
        <div class="info">The default value after reboot or <a class="linkSCPI" title="Clears all SCPI event status registers" href="#CLS">CLS</a> is 0. So no condition changes from bit 1 to 0 update the event register.</div>
      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="STAT">
        <span class="scpi" id="STAT:QUES:PTR">STAT:QUES:PTR</span><span class="briefScpi">Queries the SCPI Questionable Positive Transition register</span>
      </div>
      <div class="content">
        <div class="scpiLong">STATus:QUEStionable:PTRansition <div class="tooltip" data-id="12">&lt;bitmask&gt;</div></div>
        <div class="paramDocu">Parameters<ul>
              <li id="12"><span class="param">bitmask</span><span class="paramType">uint</span> <span class="paramDesc">16 bit wide bit mask for positive transition register.</span></li>
        </ul></div>

        <div class="details">Use this command to query the SCPI 16 bit wide Questionable Positive Transition register bit mask. The register content is a bit mask with a 1 bit for every condition change that should be mapped to the operation event register <a class="linkSCPI" title="Queries the SCPI Questionable Event register" href="#STAT:QUES?">STAT:QUES?</a>. For closer details about Positive Transition register read description <a class="linkSCPI" title="Queries the SCPI Questionable Positive Transition register" href="#STAT:QUES:PTR">STAT:QUES:PTR</a>.<br/> Read <a href="#statusRegisterSetDesc" title="General SCPI Status register description">SCPI register set</a> section to get closer infos about the SCPI register system in general.</div>
        <div class="example">&gt;&gt; STAT:QUES:PTR?<br />&lt;&lt; 65280</div>
        <div class="info">The default value after reboot or <a class="linkSCPI" title="Clears all SCPI event status registers" href="#CLS">CLS</a> is 65535 (all bits 1). So all condition changes from bit 0 to 1 update the event register.</div>
      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="STAT">
        <span class="scpi" id="STAT:QUES:PTR?">STAT:QUES:PTR?</span><span class="briefScpi">Queries the SCPI Questionable Positive Transition register</span>
      </div>
      <div class="content">
        <div class="scpiLong">STATus:QUEStionable:PTRansition?</div>
        <div class="resultDocu">Response<ul>
          <li><span class="param">bitmask</span><span class="paramType">uint</span> <span class="paramDesc">16 bit wide bit mask for positive transition register.</span></li>
        </ul></div>

        <div class="details">Use this command to query the SCPI 16 bit wide Questionable Positive Transition register bit mask. The register content is a bit mask with a 1 bit for every condition change that should be mapped to the operation event register <a class="linkSCPI" title="Queries the SCPI Questionable Event register" href="#STAT:QUES?">STAT:QUES?</a>. For closer details about Positive Transition register read description <a class="linkSCPI" title="Queries the SCPI Questionable Positive Transition register" href="#STAT:QUES:PTR">STAT:QUES:PTR</a>.<br/> Read <a href="#statusRegisterSetDesc" title="General SCPI Status register description">SCPI register set</a> section to get closer infos about the SCPI register system in general.</div>
        <div class="example">&gt;&gt; STAT:QUES:PTR?<br />&lt;&lt; 65280</div>
        <div class="info">The default value after reboot or <a class="linkSCPI" title="Clears all SCPI event status registers" href="#CLS">CLS</a> is 65535 (all bits 1). So all condition changes from bit 0 to 1 update the event register.</div>
      </div>
    </div>
    <hr>
    <p id="SYST"><a href="#SYST:ERR?" data-subsystem="SYST" class="unfoldSection">Unfold all SYST Commands</a></p>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="SYST">
        <span class="scpi" id="SYST:ERR?">SYST:ERR?</span><span class="briefScpi">Reads and removes oldest element of SCPI error queue</span>
      </div>
      <div class="content">
        <div class="scpiLong">SYSTem:ERRor[:NEXT]?</div>
        <div class="resultDocu">Response<ul>
          <li><span class="param">error</span><span class="paramType">int</span> <span class="paramDesc">SPCI error code. If no error occurred result will be 0.</span></li>
          <li><span class="param">errDesc</span><span class="paramType">string</span> <span class="paramDesc">error code English description text.</span></li>
        </ul></div>

        <div class="details">Call to test if there where errors during command execution or asynchronous device errors. The device internally uses a error queue to store SCPI errors. This command returns and removes the oldest element of the queue. Call multiple times until queue is empty and function returns 0, "No error". The internal queue length is limited. So if you do not keep the queue empty you might loose errors when queue is full.</div>
        <div class="example">&gt;&gt; SYST:ERR?<br />&lt;&lt; -113,"Undefined header"</div>

      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="SYST">
        <span class="scpi" id="SYST:ERR:COUN?">SYST:ERR:COUN?</span><span class="briefScpi">Tests how many errors are stored in SCPI error queue</span>
      </div>
      <div class="content">
        <div class="scpiLong">SYSTem:ERRor:COUNt?</div>
        <div class="resultDocu">Response<ul>
          <li><span class="param">errDesc</span><span class="paramType">uint</span> <span class="paramDesc">Amount of errors stored in SCPI error queue. 0 when empty.</span></li>
        </ul></div>

        <div class="details">For closer details about SCPI error queue read <a class="linkSCPI" title="Reads and removes oldest element of SCPI error queue" href="#SYST:ERR?">SYST:ERR?</a>.</div>
        <div class="example">&gt;&gt; SYST:ERR:COUN?<br />&lt;&lt; 1</div>

      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="SYST">
        <span class="scpi" id="SYST:VERS?">SYST:VERS?</span><span class="briefScpi">Returns SCPI version string</span>
      </div>
      <div class="content">
        <div class="scpiLong">SYSTem:VERSion?</div>
        <div class="resultDocu">Response<ul>
          <li><span class="param">vers</span><span class="paramType">string</span> <span class="paramDesc">SCPI version string without quotes. See details for format.</span></li>
        </ul></div>

        <div class="details">Response format YYYY.V. YYYY is year of SCPI standard. V is the revision of standard.</div>
        <div class="example">&gt;&gt; SYST:VER?<br />&lt;&lt; 1999.0</div>

      </div>
    </div>


  </div>
  <script>
    //Add event handler to all SCPI command descriptions to hide or show all commands
    var coll = document.getElementsByClassName("collapsible");
    for (let i = 0; i < coll.length; i++) {
      coll[i].addEventListener("click", (e) => {
        let event = e.target || e.currentTarget;
        //Navigate DOM to collapsible div
        let content;
        if(event.nodeName == "SPAN") {
          content = event.parentElement.nextElementSibling;
        } else {
          content = event.nextElementSibling;
        }
          
        //Toggle fold/unfold by maxHeight style attribute
        if (content.style.maxHeight){
          content.style.maxHeight = null;
        } else {
          content.style.maxHeight = content.scrollHeight + "px";
        } 
      });
    }

    //Add event handler to all SCPI cross reference links to ensure link destination is visible
    var scpiLinks = document.getElementsByClassName("linkSCPI");
    for(let i = 0; i < scpiLinks.length; i++) {
      scpiLinks[i].addEventListener("click", (e) => {
        let event = e.target || e.currentTarget;
        if(event.hash) {
          //Get element of id stored in anchor element
          let destSpan = document.getElementById(event.hash.substring(1));
          if(destSpan) {
            //Navigate DOM to collapsible div
            let destEl = destSpan.parentElement.nextElementSibling;
            //Ensure to show div if not unfolded already
            if(destEl && !destEl.style.maxHeight) {
              destEl.style.maxHeight = destEl.scrollHeight + "px";
            }
          }
        }
      });
    }
    
    //Add event handlers to show and hide tooltips of SCPI commands
    var tooltips = document.getElementsByClassName("tooltip");    
    for (let i = 0; i < tooltips.length; i++) 
    {
      tooltips[i].addEventListener("mouseenter", (e) => {
        let event = e.target || e.currentTarget;
        document.getElementById(event.getAttribute('data-id')).classList.add("highlightParam");
      });
      tooltips[i].addEventListener("mouseleave", (e) => {
        let event = e.target || e.currentTarget;
        document.getElementById(event.getAttribute('data-id')).classList.remove("highlightParam");
      });
    }

    //Add event handler for SCPI subsystem fold or unfold operation 
    var unfolder = document.getElementsByClassName("unfoldSection");
    for (let i = 0; i < unfolder.length; i++) {
      unfolder[i].addEventListener("click", (e) => {
        let event = e.target || e.currentTarget;
        
        //Should we hide or show all entries?
        let isFold = event.text.startsWith('Unfold')
        //Get Subsystem for this event handler
        let subsystem = event.getAttribute('data-subsystem')
    
        //List of all SCPI command containers for given SCPI subsystem
        var affected = document.querySelectorAll('div[data-subsystem="'+subsystem+'"]');
    
        for(let j = 0; j < affected.length; j++) {
          //Navigate DOM to collapsible div
          let content = affected[j].nextElementSibling;
          //Test if we should hide or show all entries.
          if(isFold) {
            content.style.maxHeight = null;
          }
          else {
            content.style.maxHeight = content.scrollHeight + "px";
          }
    
          affected[j].click()
        }
    
        //Replace SCPI subsystem unfold/fold link text label
        if(isFold)
          event.text = event.text.replace("Unfold", "Fold");
        else
          event.text = event.text.replace("Fold", "Unfold");
      });
    }

    //Add event handler for global SCPI fold or unfold operation 
    document.getElementById("foldAll").addEventListener("click", (e) => {
      let event = e.target || e.currentTarget;
      //Should we hide or show all entries?
      let isFold = event.text.startsWith('Unfold');
        
      //List of all SCPI command container
      var affected = document.querySelectorAll('div[data-subsystem]');
        
      for(let j = 0; j < affected.length; j++) {
        //Navigate DOM to collapsible div
        let content = affected[j].nextElementSibling;
        //Test if we should hide or show all entries.
        if(isFold) {
          content.style.maxHeight = null;
        }
        else {
          content.style.maxHeight = content.scrollHeight + "px";
        }

        affected[j].click()
      }
        
      //Replace global unfold/fold link text label
      if(isFold)
        event.text = event.text.replace("Unfold", "Fold");
      else
        event.text = event.text.replace("Fold", "Unfold");
      
      //Replace all SCPI subsystem fold/unfold text labes
      otherFolders = document.querySelectorAll('a[data-subsystem]');
      for(let j = 0; j < otherFolders.length; j++) {
        if(isFold)
          otherFolders[j].text = otherFolders[j].text.replace("Unfold", "Fold");
        else
          otherFolders[j].text = otherFolders[j].text.replace("Fold", "Unfold");
      }
    });
    </script>
  </body>
</html>
