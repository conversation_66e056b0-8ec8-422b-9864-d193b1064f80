<!DOCTYPE html>
<html lang="en">
<head>
    <style>
body,html { 
  margin: 0;
  font-family: Arial, Helvetica, sans-serif;
  font-size: 14px;
  box-sizing: border-box;
}

p {
  margin-left: 5px;
}

.codeSample {
  margin-left: 5px;
  font-family: <PERSON>sol<PERSON>,"courier new";
}

img {
  margin-left: 5px;
}

@media print {
   .pageBody {
    overflow-y: auto !important;
    height: auto !important;
  }

  .content {
    overflow: auto !important;
    max-height: initial !important;
    transition: none !important;
  }

  .noSplit {
    break-inside: avoid;
  }
  
  .header-right {
    display: none !important;
  }
 
  .content .scpiLong {
    font-size: 12px !important;
  }
  
  .collapsible {
    font-weight: bold !important;
    color-adjust: exact!important;  
    -webkit-print-color-adjust: exact!important; 
    print-color-adjust: exact!important;
  }
  
  rect {
    color-adjust: exact!important;  
    -webkit-print-color-adjust: exact!important; 
    print-color-adjust: exact!important;
  }
  
  svg {
    width: 95% !important;
  }
}

a[href^="http"]:after {
     content: " " url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAoAAAAKCAYAAACNMs+9AAAAVklEQVR4Xn3PgQkAMQhDUXfqTu7kTtkpd5RA8AInfArtQ2iRXFWT2QedAfttj2FsPIOE1eCOlEuoWWjgzYaB/IkeGOrxXhqB+uA9Bfcm0lAZuh+YIeAD+cAqSz4kCMUAAAAASUVORK5CYII=);
}

svg {
  margin-left: 5px;
}

.verInfo{
  float:right;
  text-align: right;
  width: 300px;
  margin-right: 5px;
}

.header {
  overflow: hidden;
  background-color: #f1f1f1;
  padding: 5px 5px;
  left: 0;
  top: 0;
}

.pageBody {
  overflow-y: scroll; 
  overflow-x: hidden; 
  height: calc(100vh - 50px);
}

.header a {
  float: left;
  color: black;
  text-align: center;
  padding: 8px;
  text-decoration: none;
  font-size: 18px; 
  line-height: 22px;
  border-radius: 4px;
}

.header span {
  padding: 0px;
  font-size: 25px;
  font-weight: bold;
}

.header a:hover {
  background-color: #ddd;
  color: black;
}

.header-right {
  float: right;
}

.tooltip {
  display: inline-block;
  border-bottom: 1px dotted black;
  cursor: help;
}

.collapsible {
  background-color: #777;
  background-image: linear-gradient(#777, #555);
  color: white;
  cursor: pointer;
  padding: 5px;
  margin: 0px;
  margin-top: 2px;
  width: 100%;
  border: none;
  text-align: left;
  outline: none;
  font-size: 12px;
}

.collapsible .scpi {
  float:left;
  font-size: 14px;
  width: 280px;
}

.collapsible .briefScpi {
  margin-left: 10px;
  font-size: 14px;
}

.active, .collapsible:hover {
  background-color: #555;
  background-image: linear-gradient(#555, #777);
}

.content {
  padding: 0;
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.2s ease-out;
  background-color: #f1f1f1;
  z-index: 0;
}

.content ul { 
  position: relative;
  list-style: none;
  padding: 0px;
  margin: 0px;
  margin-top: 5px;
  margin-bottom: 10px;
  font-weight: normal;
  font-size: 14px;
}

.content li {
  margin: 0;
  margin-top: 4px;
  margin-bottom: 4px;
}

.content .scpiLong {
  width: 99%;
  margin: 10px 5px 10px 5px;
  font-size: 18px;
}

.details{
  padding: 7px 5px;
  margin: 4px;
  font-size: 14px;
}

.detailListing {
  maring:  5px;
}

.detailListing ul li{
  list-style-type: circle;
  margin-left: 20px;
}

span.param , span.optParam {
  float: left;
  width: 100px;
}

span.optParam {
  font-style: italic;
}

span.paramType {
  float: left;
  font-style: italic;
  width: 140px;
}

li.highlightParam {
  font-weight: bold;
}

.info {
  background-color: #c7ffd6;
  border-left: 6px solid #389c53;
  padding: 7px 5px;
  margin: 4px;
  background-image: linear-gradient(to right, #c7ffd6, #f1f1f1);
  font-size: 14px;
}

.note {
  background-color: #ffdddd;
  border-left: 6px solid #DE1616;
  padding: 7px 5px;
  margin: 4px;
  background-image: linear-gradient(to right, #ffdddd, #f1f1f1);
  font-size: 14px;
}

.example {
  background-color: #E0E6F8;
  border-left: 6px solid #075C91;
  padding: 7px 5px;
  margin: 4px;
  font-family: monospace;
  background-image: linear-gradient(to right, #E0E6F8, #f1f1f1);
  font-size: 14px;
}

.sufixDocu, .paramDocu, .resultDocu {
  padding-left: 7px;
  font-size: 10px;
  font-weight: bold;
}

.resultDocu{
  padding-left: 7px;
  font-size: 10px;
  font-weight: bold;
}

span.scpiCmd {
  font-family: monospace;
}

div.scpiSpacer {
   display: inline-block;
   width: 180px;
  font-weight: bold;
}
    </style>
    <meta charset="utf-8">
    <title>ERM200 SCPI Commands</title>
    <link rel="icon" type="image/png" sizes="16x16" href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAIAAAACACAYAAADDPmHLA
    AABcklEQVR4nO3cMQ7DIBAAwSPK/79MnkBlEWtnWiiQtbrCxa2Z2UPW5/YDuEsAcQKIE0CcAOIEECeAuO/pgp8E77YO5yZAnADiBBAngDgBxAkgTgBxAogTQJ
    wA4gQQJ4A4AcQJIE4AcQKIE0CcAOIEECeAOAHECSBOAHECiBNAnADiBBAngDgBxAkgTgBxAogTQJwA4gQQJ4A4AcQJIE4Accc9gbP3adXcs9Z696rCP/9+JkC
    cAOIEECeAOAHECSBOAHECiBNAnADiBBAngDgBxAkgTgBxAogTQJwA4gQQJ4A4AcQJIE4AcQKIE0CcAOIEECeAOAHECSBOAHECiBNAnADiBBAngDgBxAkgTgBx
    AogTQJwA4gQQJ4A4AcQJIE4AcQKIE0CcAOIEECeAOAHECSBOAHECiBNAnADiBBAngDgBxAkgTgBxAogTQJwA4gQQJ4A4AcQJIE4AcWtm9u1HcI8JECeAOAHEC
    SBOAHECiBNA3A/BiAoBsMLaFQAAAABJRU5ErkJggg==" />
</head>
<body>
  <div class="header">
    <span>ERM200 SCPI Commands</span>
    <div class="header-right">
      <a href="#None">None</a>
      <a href="#CAL">CAL</a>
      <a href="#DISP">DISP</a>
      <a href="#MEAS">MEAS</a>
      <a href="#SENS">SENS</a>
      <a href="#STAT">STAT</a>
      <a href="#SYST">SYST</a>

    </div>
  </div>
  <div class="pageBody">
    <div id="SCPI_Desc">
      <div class="verInfo">V 0.3.1 generated 31 May 2023, 15:07:07</div>
      <p>This is the description of the <a target="_blank" href="https://www.thorlabs.com/">Thorlabs</a> ERM200 remote interface. This ER Meter can be remotely controlled using the SCPI (Standard Commands for Programmable Instruments) interface.</p>
      <p>In 1975, the IEEE standardized a bus developed by Hewlett-Packard originally called HPIB (Hewlett-Packard Interface Bus), later changed to GPIB (General Purpose Interface Bus). 
        The standard was called IEEE 488 (IEEE 488.1) and it defined mechanical aspects of the bus. The later IEEE 488.2 defined its protocol properties. What was missing were set of 
        rules between the manufacturers on commands to control the instruments. Sometimes these varied even between different models from the same manufacturer.</p>
      <p>In 1990 SCPI Consortium released the first SCPI standard as an additional layer for the IEEE-488.2 standard.</p>
      <p>SCPI commands are ASCII strings, which are sent to instrument over the physical communication layer. They can perform:</p>
      <ul>
        <li> Set operations, for example the <span class="scpiCmd">*RST</span> command (resetting the instrument).</li>
        <li> Query operations, for example the <span class="scpiCmd">*IDN?</span> query (querying the instrument’s identification string).</li>
      </ul>
      <p>Some SCPI commands exist as both set commands and query commands. An example is an ER200's averaging command <span class="scpiCmd">SENS:AVER</span>. You can set it with the 
        SCPI command <span class="scpiCmd">SENS:AVER 2</span>, and also query its current value with the <span class="scpiCmd">SENS:AVER?</span>.</p>

      <p>The format mentioned in this manual e.g.: <span class="scpiCmd">SENSe#:AVERage[:COUNt] &lt;pres&gt;</span> is called canonical form. Here are the most important rules to remember:</p>
      <ul>
        <li>The parts within square brackets are not mandatory and can be left out.</li>
        <li>All # within the command represent a suffix positive integer number starting at 1. If there is only one suffix you may skip it because default is applied internally. 
           If there are two or more suffix it is mandatory to specify all.</li>
        <li>The capital letter parts are mandatory; the small letters can be omitted. This is called short form. An example of the above command in the short form is <span class="scpiCmd">SENS1:AVER 3</span>. 
           You can use either the short form, or the long form <span class="scpiCmd">SENSE1:AVERAGE 3</span>, but nothing in between, e.g. <span class="scpiCmd">SENS1:AVERA 3</span>.</li>
        <li>The SCPI commands are case-insensitive. You can also use the short form <span class="scpiCmd">sens1:aver 3</span> or or the long form <span class="scpiCmd">sense1:average 3</span></li>
        <li>Combine multiple commands into one string using selmicolon ';'. For example, a combined string of <span class="scpiCmd">SENS1:AVER 3</span> and <span class="scpiCmd">SENS1:CORR:WAV 850</span> is 
            <span class="scpiCmd">SENS1:AVER 3;CORR:WAV 850</span>. Notice that the second command does not have the <span class="scpiCmd">SENS1:</span> part. The reason is, that the command tree path does 
            not change within one string. If you want to reset the command tree path to the root, use the colon character at the beginning of the second command: <span class="scpiCmd">SENS1:AVER 3;:SENS2:CORR:WAV 850</span>.</li>
        <li>Create query forms by adding a question mark, mostly to the end: <span class="scpiCmd">SENS1:AVER?</span> Sometimes there is an additional parameter placed after the 
            question mark. There must be a space character between the question mark and the additional parameter. For example: <span class="scpiCmd">SENS1:CORR:WAV? MIN</span></li>
      </ul>
      
      <p>The complete SCPI standard is available here: <a  target="_blank" href="https://www.ivifoundation.org/docs/scpi-99.pdf" title="SCPI Standard PDF">SCPI-99</a></p>
    </div>
    <div id="statusRegisterSetDesc">
    <h1>SCPI Registers</h1>
      <p>The SCPI system defines a status reporting system based on status register sets. Every state or event is mapped 
      to a single bit in the values of the register sets. The ERM200 defines the following register sets: </p>
      <ul>
        <li><b>OPER</b>ation: Measurement state monitoring.</li>
        <li><b>QUEST</b>ionalbe: Measure result reliability status monitoring.</li>
        <li><b>STD</b>andard Byte: SCPI global system status byte.</li>
      </ul>

      <p>Every register set contains 5 registers.</p>
      <ul>
        <li><b>COND</b>ition: The Condition register monitors the actual state. For example if a sensor is currently connected or not.</li>
        <li><b>PTR</b>ansition: The Positive Transition register masks bit changes from 0 to 1 of the Condition register to the Event register.</li>
        <li><b>NTR</b>ansition: The Negative Transition register masks bit changes from 1 to 0 of the Condition register to the Event register.</li>
        <li><b>EVENT</b>: The Event register shows changes of the Condition register. Some conditions only last for a short moment and are otherwise hard to detect.</li>
        <li><b>ENAB</b>le: The Enable register masks if changes of the Event registers (TODO JS registers plural?) are forwarded to a higher level hierarchy Condition register.</li>
      </ul>
      <p>The following image visualizes the logical relation between the registers.</p>
      <svg width="600px" viewBox="239 259 690 160" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">  <g>    <rect style="fill: #ffffff" x="240" y="320" width="75.2" height="38"/>    <rect style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x="240" y="320" width="75.2" height="38"/>    <text font-size="12.8" style="fill: #000000;text-anchor:middle;font-family:sans-serif;font-style:normal;font-weight:normal" x="277.6" y="343.8">      <tspan x="277.6" y="343.8">CON</tspan>    </text>  </g>  <g>    <rect style="fill: #ffffff" x="360" y="260" width="136.65" height="38"/>    <rect style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x="360" y="260" width="136.65" height="38"/>    <text font-size="12.8" style="fill: #000000;text-anchor:middle;font-family:sans-serif;font-style:normal;font-weight:normal" x="428.325" y="283.8">      <tspan x="428.325" y="283.8">PTR: CON bit 0 -&gt; 1</tspan>    </text>  </g>  <g>    <rect style="fill: #ffffff" x="359.25" y="380" width="138.15" height="38"/>    <rect style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x="359.25" y="380" width="138.15" height="38"/>    <text font-size="12.8" style="fill: #000000;text-anchor:middle;font-family:sans-serif;font-style:normal;font-weight:normal" x="428.325" y="403.8">      <tspan x="428.325" y="403.8">NTR: CON bit 1 -&gt; 0</tspan>    </text>  </g>  <g>    <rect style="fill: #ffffff" x="540" y="320" width="75.2" height="38"/>    <rect style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x="540" y="320" width="75.2" height="38"/>    <text font-size="12.8" style="fill: #000000;text-anchor:middle;font-family:sans-serif;font-style:normal;font-weight:normal" x="577.6" y="343.8">      <tspan x="577.6" y="343.8">EVENT</tspan>    </text>  </g>  <g>    <rect style="fill: #ffffff" x="680" y="320" width="75.2" height="38"/>    <rect style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x="680" y="320" width="75.2" height="38"/>    <text font-size="12.8" style="fill: #000000;text-anchor:middle;font-family:sans-serif;font-style:normal;font-weight:normal" x="717.6" y="343.8">      <tspan x="717.6" y="343.8">ENAB</tspan>    </text>  </g>  <g>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="277.6,320 277.6,279 355.528,279 "/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="347.764,284 357.764,279 347.764,274 "/>  </g>  <g>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="277.6,358 277.6,399 354.778,399 "/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="347.014,404 357.014,399 347.014,394 "/>  </g>  <g>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="496.65,279 577.6,279 577.6,315.528 "/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="572.6,307.764 577.6,317.764 582.6,307.764 "/>  </g>  <g>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="497.4,399 577.6,399 577.6,362.472 "/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="582.6,370.236 577.6,360.236 572.6,370.236 "/>  </g>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="615.2" y1="339" x2="675.528" y2="339"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="667.764,344 677.764,339 667.764,334 "/>  </g>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke-dasharray: 4; stroke: #000000" x1="755.2" y1="339" x2="847.528" y2="339"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="839.764,344 849.764,339 839.764,334 "/>  </g>  <g>    <rect style="fill: #ffffff" x="852" y="320" width="75.2" height="38"/>    <rect style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x="852" y="320" width="75.2" height="38"/>    <text font-size="12.8" style="fill: #000000;text-anchor:middle;font-family:sans-serif;font-style:normal;font-weight:normal" x="889.6" y="343.8">      <tspan x="889.6" y="343.8">CON</tspan>    </text>  </g></svg>
      <p>The Condition register always reflects the current state. For example if the motor is running or not. The Positive Transition register masks if a
      certain condition becomes true (bit changes from 0 to 1) to set the bit at the same position in the Event register. For example to set the Event register 
      bit to 1 if motor starts running. <br/>The Negative Transition register tests if condition becomes false to update the Event register. For example to 
      set event register bit to 1 if the motor stops turning. The Enable register masks if changes of the Event register should be reported to the 
      higher level hierarchy Condition register. The register sets are logically chained. The following section will provide further details about the chaining.</p>
      <p>All registers except the STB register group are 16 bit wide. The following paragraph gives a short description of bit mask in general. Bit mask use the binary
      representation of numbers to store boolean states (true, false) or (active, inactive). This is a memory saving way to store multiple conditions at once.
      If you look on a decimal number 9 for example the binary representation is 1001 = 1 * 2 ^ 4 + 1 * 2 ^ 0. This bit mask means condition at bit position 1 and 4 are true. 
      All other conditions are false as all other bits are set to 0.<br/>
      To test if a certain bit is set to 1 you can either use bit logic operations or use generic mathematic operations. Bit logic operations are available in almost every 
      programming language. In Python you would write something like condition = bitmask &amp; (1 &lt;&lt; position).<br/>
      If you want to use standard math you can use the following formula: condition = round_down(bitmask / (2 ^ bitPos)) modulus 2.</p>
     
      <p>The OPERation register set hierarchy looks the following</p>
      <svg width="600px" viewBox="818 1858 643 343" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">  <g>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1100,2022 1160,2022 1160,2180 1315.53,2180 "/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1307.76,2185 1317.76,2180 1307.76,2175 "/>  </g>  <g>    <rect style="fill: #ffffff" x="1320" y="2020" width="140" height="180"/>    <rect style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x="1320" y="2020" width="140" height="180"/>    <text font-size="14.1111" style="fill: #000000;text-anchor:end;font-family:sans-serif;font-style:normal;font-weight:700" x="1451" y="2114.28">      <tspan x="1451" y="2114.28">*STB</tspan>    </text>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1180" y="2058">    <tspan x="1180" y="2058">Protection event </tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1180" y="2078">    <tspan x="1180" y="2078">Error queue state chng</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1180" y="2098">    <tspan x="1180" y="2098">Questionable status</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="1200" y1="2060" x2="1315.53" y2="2060"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1307.76,2065 1317.76,2060 1307.76,2055 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1180" y="2118">    <tspan x="1180" y="2118">Message Available</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="1200" y1="2080" x2="1315.53" y2="2080"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1307.76,2085 1317.76,2080 1307.76,2075 "/>  </g>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="1200" y1="2120" x2="1315.53" y2="2120"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1307.76,2125 1317.76,2120 1307.76,2115 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1180" y="2138">    <tspan x="1180" y="2138">Standard Event Status</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="1200" y1="2140" x2="1315.53" y2="2140"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1307.76,2145 1317.76,2140 1307.76,2135 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1180" y="2158">    <tspan x="1180" y="2158">Service Request</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="1200" y1="2160" x2="1315.53" y2="2160"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1307.76,2165 1317.76,2160 1307.76,2155 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1180" y="2178">    <tspan x="1180" y="2178">Operation Status</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="1200" y1="2180" x2="1315.53" y2="2180"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1307.76,2185 1317.76,2180 1307.76,2175 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1324" y="2044">    <tspan x="1324" y="2044">bit 0(1)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1324" y="2064">    <tspan x="1324" y="2064">bit 1(2)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1324" y="2084">    <tspan x="1324" y="2084">bit 2(4)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1324" y="2104">    <tspan x="1324" y="2104">bit 3(8)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1324" y="2124">    <tspan x="1324" y="2124">bit 4(16)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1324" y="2144">    <tspan x="1324" y="2144">bit 5(32)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1324" y="2164">    <tspan x="1324" y="2164">bit 6(64)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1324" y="2184">    <tspan x="1324" y="2184">bit 7(128)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1180" y="2038">    <tspan x="1180" y="2038">Aux Status </tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="1200" y1="2040" x2="1315.53" y2="2040"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1307.76,2045 1317.76,2040 1307.76,2035 "/>  </g>  <g>    <rect style="fill: #ffffff" x="960" y="1860" width="140" height="324"/>    <rect style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x="960" y="1860" width="140" height="324"/>    <text font-size="14.1111" style="fill: #000000;text-anchor:end;font-family:sans-serif;font-style:normal;font-weight:700" x="1091" y="2017.46">      <tspan x="1091" y="2017.46">STAT:</tspan>      <tspan x="1091" y="2035.1">OPER</tspan>    </text>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="820" y="1870">    <tspan x="820" y="1870">reserved</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="820" y="1890">    <tspan x="820" y="1890">Powering Up</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="820" y="1910">    <tspan x="820" y="1910">reserved</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="840" y1="1872" x2="955.528" y2="1872"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="947.764,1877 957.764,1872 947.764,1867 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="964" y="1876">    <tspan x="964" y="1876">bit 0(1)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="820" y="1930">    <tspan x="820" y="1930">reserved</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="840" y1="1892" x2="955.528" y2="1892"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="947.764,1897 957.764,1892 947.764,1887 "/>  </g>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="840" y1="1912" x2="955.528" y2="1912"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="947.764,1917 957.764,1912 947.764,1907 "/>  </g>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="840" y1="1932" x2="955.528" y2="1932"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="947.764,1937 957.764,1932 947.764,1927 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="818.704" y="1950">    <tspan x="818.704" y="1950">Measurement active</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="840" y1="1952" x2="955.528" y2="1952"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="947.764,1957 957.764,1952 947.764,1947 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="820" y="1970">    <tspan x="820" y="1970">reserved</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="840" y1="1972" x2="955.528" y2="1972"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="947.764,1977 957.764,1972 947.764,1967 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="820" y="1990">    <tspan x="820" y="1990">resvered</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="840" y1="1992" x2="955.528" y2="1992"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="947.764,1997 957.764,1992 947.764,1987 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="820" y="2010">    <tspan x="820" y="2010">reserved</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="840" y1="2012" x2="955.528" y2="2012"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="947.764,2017 957.764,2012 947.764,2007 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="820" y="2030">    <tspan x="820" y="2030">Motor started</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="840" y1="2032" x2="955.528" y2="2032"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="947.764,2037 957.764,2032 947.764,2027 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="820" y="2050">    <tspan x="820" y="2050">Encoder rotating</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="840" y1="2052" x2="955.528" y2="2052"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="947.764,2057 957.764,2052 947.764,2047 "/>  </g>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="840" y1="2072" x2="955.528" y2="2072"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="947.764,2077 957.764,2072 947.764,2067 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="820" y="2090">    <tspan x="820" y="2090">Power data to fetch</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="840" y1="2092" x2="955.528" y2="2092"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="947.764,2097 957.764,2092 947.764,2087 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="820" y="2110">    <tspan x="820" y="2110">reserved</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="840" y1="2112" x2="955.528" y2="2112"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="947.764,2117 957.764,2112 947.764,2107 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="820" y="2170">    <tspan x="820" y="2170">reserved</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="840" y1="2172" x2="955.528" y2="2172"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="947.764,2177 957.764,2172 947.764,2167 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="964" y="1896">    <tspan x="964" y="1896">bit 1(2)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="964" y="1916">    <tspan x="964" y="1916">bit 2(4)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="964" y="1936">    <tspan x="964" y="1936">bit 3(8)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="964" y="1956">    <tspan x="964" y="1956">bit 4(16)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="964" y="1976">    <tspan x="964" y="1976">bit 5(32)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="964" y="1996">    <tspan x="964" y="1996">bit 6(64)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="964" y="2016">    <tspan x="964" y="2016">bit 7(128)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="964" y="2036">    <tspan x="964" y="2036">bit 8(256)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="964" y="2056">    <tspan x="964" y="2056">bit 9(512)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="964" y="2076">    <tspan x="964" y="2076">bit 10(1024)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="964" y="2096">    <tspan x="964" y="2096">bit 11(2048)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="964" y="2116">    <tspan x="964" y="2116">bit 12(4096)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="964" y="2136">    <tspan x="964" y="2136">bit 13(8192)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="964" y="2156">    <tspan x="964" y="2156">bit 14(16384)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="964" y="2176">    <tspan x="964" y="2176">bit 15(32768)</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="1200" y1="2100" x2="1315.53" y2="2100"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1307.76,2105 1317.76,2100 1307.76,2095 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="820" y="2068">    <tspan x="820" y="2068">ER data to fetch</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="820" y="2130">    <tspan x="820" y="2130">reserved</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="840" y1="2132" x2="955.528" y2="2132"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="947.764,2137 957.764,2132 947.764,2127 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="820" y="2150">    <tspan x="820" y="2150">reserved</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="840" y1="2152" x2="955.528" y2="2152"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="947.764,2157 957.764,2152 947.764,2147 "/>  </g></svg>

      <p>The QUESTionable register set hierarchy looks the following</p>
      <svg width="600px" viewBox="820 1058 642 327" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">  <g>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1100,1202 1160,1202 1160,1280 1315.53,1280 "/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1307.76,1285 1317.76,1280 1307.76,1275 "/>  </g>  <g>    <rect style="fill: #ffffff" x="960" y="1060" width="140" height="324"/>    <rect style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x="960" y="1060" width="140" height="324"/>    <text font-size="14.1111" style="fill: #000000;text-anchor:end;font-family:sans-serif;font-style:normal;font-weight:700" x="1091" y="1217.46">      <tspan x="1091" y="1217.46">STAT:</tspan>      <tspan x="1091" y="1235.1">QUEST</tspan>    </text>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="820" y="1070">    <tspan x="820" y="1070">reserved</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="820" y="1090">    <tspan x="820" y="1090">reserved</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="820" y="1110">    <tspan x="820" y="1110">reserved</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="840" y1="1072" x2="955.528" y2="1072"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="947.764,1077 957.764,1072 947.764,1067 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="964" y="1076">    <tspan x="964" y="1076">bit 0(1)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="820" y="1130">    <tspan x="820" y="1130">Questionalbe Power</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="840" y1="1092" x2="955.528" y2="1092"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="947.764,1097 957.764,1092 947.764,1087 "/>  </g>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="840" y1="1112" x2="955.528" y2="1112"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="947.764,1117 957.764,1112 947.764,1107 "/>  </g>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="840" y1="1132" x2="955.528" y2="1132"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="947.764,1137 957.764,1132 947.764,1127 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="820" y="1150">    <tspan x="820" y="1150">reserved</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="840" y1="1152" x2="955.528" y2="1152"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="947.764,1157 957.764,1152 947.764,1147 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="820" y="1170">    <tspan x="820" y="1170">reserved</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="840" y1="1172" x2="955.528" y2="1172"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="947.764,1177 957.764,1172 947.764,1167 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="820" y="1190">    <tspan x="820" y="1190">reserved</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="840" y1="1192" x2="955.528" y2="1192"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="947.764,1197 957.764,1192 947.764,1187 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="820" y="1210">    <tspan x="820" y="1210">reserved</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="840" y1="1212" x2="955.528" y2="1212"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="947.764,1217 957.764,1212 947.764,1207 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="820" y="1230">    <tspan x="820" y="1230">Questionable Calibrat</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="840" y1="1232" x2="955.528" y2="1232"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="947.764,1237 957.764,1232 947.764,1227 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="820" y="1250">    <tspan x="820" y="1250">reserved</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="840" y1="1252" x2="955.528" y2="1252"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="947.764,1257 957.764,1252 947.764,1247 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="820" y="1270">    <tspan x="820" y="1270">reserved</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="840" y1="1272" x2="955.528" y2="1272"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="947.764,1277 957.764,1272 947.764,1267 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="820" y="1290">    <tspan x="820" y="1290">reserved</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="840" y1="1292" x2="955.528" y2="1292"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="947.764,1297 957.764,1292 947.764,1287 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="820" y="1310">    <tspan x="820" y="1310">reserved</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="840" y1="1312" x2="955.528" y2="1312"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="947.764,1317 957.764,1312 947.764,1307 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="820" y="1370">    <tspan x="820" y="1370">reserved</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="840" y1="1372" x2="955.528" y2="1372"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="947.764,1377 957.764,1372 947.764,1367 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="964" y="1096">    <tspan x="964" y="1096">bit 1(2)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="964" y="1116">    <tspan x="964" y="1116">bit 2(4)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="964" y="1136">    <tspan x="964" y="1136">bit 3(8)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="964" y="1156">    <tspan x="964" y="1156">bit 4(16)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="964" y="1176">    <tspan x="964" y="1176">bit 5(32)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="964" y="1196">    <tspan x="964" y="1196">bit 6(64)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="964" y="1216">    <tspan x="964" y="1216">bit 7(128)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="964" y="1236">    <tspan x="964" y="1236">bit 8(256)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="964" y="1256">    <tspan x="964" y="1256">bit 9(512)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="964" y="1276">    <tspan x="964" y="1276">bit 10(1024)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="964" y="1296">    <tspan x="964" y="1296">bit 11(2048)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="964" y="1316">    <tspan x="964" y="1316">bit 12(4096)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="964" y="1336">    <tspan x="964" y="1336">bit 13(8192)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="964" y="1356">    <tspan x="964" y="1356">bit 14(16384)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="964" y="1376">    <tspan x="964" y="1376">bit 15(32768)</tspan>  </text>  <g>    <rect style="fill: #ffffff" x="1320" y="1200" width="140" height="180"/>    <rect style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x="1320" y="1200" width="140" height="180"/>    <text font-size="14.1111" style="fill: #000000;text-anchor:end;font-family:sans-serif;font-style:normal;font-weight:700" x="1451" y="1294.28">      <tspan x="1451" y="1294.28">*STB</tspan>    </text>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1180" y="1238">    <tspan x="1180" y="1238">Protection event </tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1180" y="1258">    <tspan x="1180" y="1258">Error queue state chng</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1180" y="1278">    <tspan x="1180" y="1278">Questionable status</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="1200" y1="1240" x2="1315.53" y2="1240"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1307.76,1245 1317.76,1240 1307.76,1235 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1180" y="1298">    <tspan x="1180" y="1298">Message Available</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="1200" y1="1260" x2="1315.53" y2="1260"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1307.76,1265 1317.76,1260 1307.76,1255 "/>  </g>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="1200" y1="1300" x2="1315.53" y2="1300"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1307.76,1305 1317.76,1300 1307.76,1295 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1180" y="1318">    <tspan x="1180" y="1318">Standard Event Status</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="1200" y1="1320" x2="1315.53" y2="1320"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1307.76,1325 1317.76,1320 1307.76,1315 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1180" y="1338">    <tspan x="1180" y="1338">Service Request</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="1200" y1="1340" x2="1315.53" y2="1340"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1307.76,1345 1317.76,1340 1307.76,1335 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1180" y="1358">    <tspan x="1180" y="1358">Operation Status</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="1200" y1="1360" x2="1315.53" y2="1360"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1307.76,1365 1317.76,1360 1307.76,1355 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1324" y="1224">    <tspan x="1324" y="1224">bit 0(1)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1324" y="1244">    <tspan x="1324" y="1244">bit 1(2)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1324" y="1264">    <tspan x="1324" y="1264">bit 2(4)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1324" y="1284">    <tspan x="1324" y="1284">bit 3(8)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1324" y="1304">    <tspan x="1324" y="1304">bit 4(16)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1324" y="1324">    <tspan x="1324" y="1324">bit 5(32)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1324" y="1344">    <tspan x="1324" y="1344">bit 6(64)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1324" y="1364">    <tspan x="1324" y="1364">bit 7(128)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1180" y="1218">    <tspan x="1180" y="1218">Aux Status </tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="1200" y1="1220" x2="1315.53" y2="1220"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1307.76,1225 1317.76,1220 1307.76,1215 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="820" y="1330">    <tspan x="820" y="1330">reserved</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="840" y1="1332" x2="955.528" y2="1332"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="947.764,1337 957.764,1332 947.764,1327 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="820" y="1350">    <tspan x="820" y="1350">reserved</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="840" y1="1352" x2="955.528" y2="1352"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="947.764,1357 957.764,1352 947.764,1347 "/>  </g></svg>

    </div>

    <p><a href="#" id="foldAll">Unfold all SCPI Commands</a></p>
    <hr>
    <p id="None"><a href="#CLS" data-subsystem="None" class="unfoldSection">Unfold all None Commands</a></p>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="None">
        <span class="scpi" id="CLS">CLS</span><span class="briefScpi">Clears all SCPI event status registers</span>
      </div>
      <div class="content">
        <div class="scpiLong">*CLS</div>
        <div class="details">Use this command to clear all event bits in the SCPI event status registers. After calling this command all event registers are read as 0 as long no new event changes bits back to 1. For closer details about event registers read for example description of command <a class="linkSCPI" title="Queries the SCPI Operation Event register" href="#STAT:OPER?">STAT:OPER?</a>.</div>
        <div class="example">&gt;&gt; *CLS</div>

      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="None">
        <span class="scpi" id="ESE">ESE</span><span class="briefScpi">Programs the 8 bit wide Standard Event Enable Register</span>
      </div>
      <div class="content">
        <div class="scpiLong">*ESE <div class="tooltip" data-id="0">&lt;bitmask&gt;</div></div>
        <div class="paramDocu">Parameters<ul>
              <li id="0"><span class="param">bitmask</span><span class="paramType">uint</span> <span class="paramDesc">standard even enable register bit mask.</span></li>
        </ul></div>

        <div class="example">&gt;&gt; ESE 2</div>

      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="None">
        <span class="scpi" id="ESE?">ESE?</span><span class="briefScpi">Reads the 8 bit wide Standard Event Enable Register</span>
      </div>
      <div class="content">
        <div class="scpiLong">*ESE?</div>
        <div class="resultDocu">Response<ul>
          <li><span class="param">bitmask</span><span class="paramType">uint</span> <span class="paramDesc">standard even enable register bit mask.</span></li>
        </ul></div>

        <div class="example">&gt;&gt; ESE?<br />&lt;&lt; 0</div>

      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="None">
        <span class="scpi" id="ESR?">ESR?</span><span class="briefScpi">Reads and clears the Standard Event Register</span>
      </div>
      <div class="content">
        <div class="scpiLong">*ESR?</div>
        <div class="resultDocu">Response<ul>
          <li><span class="param">bitmask</span><span class="paramType">uint</span> <span class="paramDesc">standard byte event register bit mask.</span></li>
        </ul></div>

        <div class="example">&gt;&gt; ESR?<br />&lt;&lt; 2</div>

      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="None">
        <span class="scpi" id="IDN?">IDN?</span><span class="briefScpi">Reads the device identification string</span>
      </div>
      <div class="content">
        <div class="scpiLong">*IDN?</div>
        <div class="resultDocu">Response<ul>
          <li><span class="param">manuf</span><span class="paramType">string</span> <span class="paramDesc">Device manufacturer name.</span></li>
          <li><span class="param">devName</span><span class="paramType">string</span> <span class="paramDesc">Device name.</span></li>
          <li><span class="param">serial</span><span class="paramType">string</span> <span class="paramDesc">Device serial number.</span></li>
          <li><span class="param">version</span><span class="paramType">string</span> <span class="paramDesc">Firmware software number.</span></li>
        </ul></div>

        <div class="details">Call to query device identification string. </div>
        <div class="example">&gt;&gt; *IDN?<br />&lt;&lt; Thorlabs,PM5020,T00000002,*******</div>

      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="None">
        <span class="scpi" id="LST?">LST?</span><span class="briefScpi">Lists supported SCPI commands</span>
      </div>
      <div class="content">
        <div class="scpiLong">*LST? <div class="tooltip" data-id="2">&lt;subsyst&gt;</div></div>
        <div class="paramDocu">Parameters<ul>
              <li id="2"><span class="param">subsyst</span><span class="paramType">'',string</span> <span class="paramDesc">nothing to list all commands. SCPI subsystem string to filter results</span></li>
        </ul></div>

        <div class="resultDocu">Response<ul>
          <li><span class="param">list</span><span class="paramType">string</span> <span class="paramDesc">List of SCPI commands. One command per line</span></li>
        </ul></div>

        <div class="details"> The command lists all SCPI commands matching the given filter condition. The filter condition can be any SCPI subsystem like for example "SYST". If no filter condition is set the command returns all supported commands. As the result list may be longer than the maximal SCPI response, the same command has to be called multiple times with the same parameter to retrieve all commands.</div>
        <div class="example">&gt;&gt; *LST?<br />&lt;&lt; "*IDN?"\\n"*RST"\\n</div>
        <div class="note">Result length is limited. So call multiple times with same parameters to retrieve all results.</div>

      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="None">
        <span class="scpi" id="OPC">OPC</span><span class="briefScpi">Sets the Operation Complete bit in the Standard Event Register</span>
      </div>
      <div class="content">
        <div class="scpiLong">*OPC</div>
        <div class="example">&gt;&gt; OPC 2</div>

      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="None">
        <span class="scpi" id="OPC?">OPC?</span><span class="briefScpi">Operation complete query</span>
      </div>
      <div class="content">
        <div class="scpiLong">*OPC?</div>
        <div class="resultDocu">Response<ul>
          <li><span class="param">constant</span><span class="paramType">uint</span> <span class="paramDesc">Always 1</span></li>
        </ul></div>

        <div class="details">Places a "1" into the output queue when all device operations have been completed No real functionality on this implementation. Result is always 1. </div>
        <div class="example">&gt;&gt; OPC?<br />&lt;&lt; 1</div>

      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="None">
        <span class="scpi" id="RST">RST</span><span class="briefScpi">Resets all SCPI and sensor parameters to default</span>
      </div>
      <div class="content">
        <div class="scpiLong">*RST</div>
        <div class="details"> The command resets all SCPI parameters like zero and bin to defaults.  To reset the SCPI registers use <a class="linkSCPI" title="Clears all SCPI event status registers" href="#CLS">CLS</a>. </div>
        <div class="example">&gt;&gt; RST</div>

      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="None">
        <span class="scpi" id="SRE">SRE</span><span class="briefScpi">Service request enable command</span>
      </div>
      <div class="content">
        <div class="scpiLong">*SRE <div class="tooltip" data-id="1">&lt;bitmask&gt;</div></div>
        <div class="paramDocu">Parameters<ul>
              <li id="1"><span class="param">bitmask</span><span class="paramType">uint</span> <span class="paramDesc">new service request enable register bit mask.</span></li>
        </ul></div>

        <div class="details">Programs the Service Request Enable Register</div>
        <div class="example">&gt;&gt; SRE 2</div>

      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="None">
        <span class="scpi" id="SRE?">SRE?</span><span class="briefScpi">Service request enable query</span>
      </div>
      <div class="content">
        <div class="scpiLong">*SRE?</div>
        <div class="resultDocu">Response<ul>
          <li><span class="param">bitmask</span><span class="paramType">uint</span> <span class="paramDesc">service request enable register bit mask.</span></li>
        </ul></div>

        <div class="details">Reads the Service Request Enable Register</div>
        <div class="example">&gt;&gt; SRE?<br />&lt;&lt; 0</div>

      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="None">
        <span class="scpi" id="STB?">STB?</span><span class="briefScpi">Status byte query</span>
      </div>
      <div class="content">
        <div class="scpiLong">*STB?</div>
        <div class="resultDocu">Response<ul>
          <li><span class="param">bitmask</span><span class="paramType">uint</span> <span class="paramDesc">status byte condition register bit mask.</span></li>
        </ul></div>

        <div class="details">Reads the Status Byte Register</div>
        <div class="example">&gt;&gt; STB?<br />&lt;&lt; 20</div>

      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="None">
        <span class="scpi" id="TST?">TST?</span><span class="briefScpi">Self-test query</span>
      </div>
      <div class="content">
        <div class="scpiLong">*TST?</div>
        <div class="resultDocu">Response<ul>
          <li><span class="param">state</span><span class="paramType">uint</span> <span class="paramDesc">0 if system test was ok. 1 in case of error</span></li>
        </ul></div>

        <div class="details">Tests if motor of ERM is spinning as expected.</div>
        <div class="example">&gt;&gt; TST?<br />&lt;&lt; 0</div>

      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="None">
        <span class="scpi" id="WAI">WAI</span><span class="briefScpi">Wait-to-continue command</span>
      </div>
      <div class="content">
        <div class="scpiLong">*WAI</div>
        <div class="details">Wait until all previous commands are executed. Empty implementation.</div>
        <div class="example">&gt;&gt; WAI</div>

      </div>
    </div>
    <hr>
    <p id="CAL"><a href="#CAL:STR?" data-subsystem="CAL" class="unfoldSection">Unfold all CAL Commands</a></p>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="CAL">
        <span class="scpi" id="CAL:STR?">CAL:STR?</span><span class="briefScpi">Queries factory calibration string</span>
      </div>
      <div class="content">
        <div class="scpiLong">CALibration:STRing?</div>
        <div class="resultDocu">Response<ul>
          <li><span class="param">date</span><span class="paramType">string</span> <span class="paramDesc">Cal string with format dd-MonthStr-YYYY or 'Uncalibrated' when not calibrated.</span></li>
        </ul></div>

        <div class="details">Use this command to query the factory calibration date string of the device. The format is dd-MMM-YYYY or might be "Uncalibrated" if not calibrated or calibration data is read back invalid. You might also query the <a class="linkSCPI" title="Queries the SCPI Questionable Condition register" href="#STAT:QUES:COND?">STAT:QUES:COND?</a> register to check if calibration is valid.</div>
        <div class="example">&gt;&gt; CAL:STR?<br />&lt;&lt; "18-Feb-2022"</div>

      </div>
    </div>
    <hr>
    <p id="DISP"><a href="#DISP:BRIG" data-subsystem="DISP" class="unfoldSection">Unfold all DISP Commands</a></p>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="DISP">
        <span class="scpi" id="DISP:BRIG">DISP:BRIG</span><span class="briefScpi">Sets display brightness in percent</span>
      </div>
      <div class="content">
        <div class="scpiLong">DISPlay:BRIGhtness <div class="tooltip" data-id="10">&lt;brightness&gt;</div></div>
        <div class="paramDocu">Parameters<ul>
              <li id="10"><span class="param">brightness</span><span class="paramType">float</span> <span class="paramDesc">brightness in percent between 0 and 1</span></li>
        </ul></div>

        <div class="example">&gt;&gt; DISP:BRIG 0.5</div>

      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="DISP">
        <span class="scpi" id="DISP:BRIG?">DISP:BRIG?</span><span class="briefScpi">Queries display brightness in percent</span>
      </div>
      <div class="content">
        <div class="scpiLong">DISPlay:BRIGhtness?</div>
        <div class="resultDocu">Response<ul>
          <li><span class="param">brightness</span><span class="paramType">float</span> <span class="paramDesc">Brightness in percent between 0 and 1</span></li>
        </ul></div>

        <div class="example">&gt;&gt; DISP:BRIG?<br />&lt;&lt; 0.91</div>

      </div>
    </div>
    <hr>
    <p id="MEAS"><a href="#MEAS?" data-subsystem="MEAS" class="unfoldSection">Unfold all MEAS Commands</a></p>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="MEAS">
        <span class="scpi" id="MEAS?">MEAS?</span><span class="briefScpi">Starts and returns a new ER or ERmin measurement</span>
      </div>
      <div class="content">
        <div class="scpiLong">MEASure[:SCALar][:ER]? <div class="tooltip" data-id="3">&lt;option&gt;</div></div>
        <div class="paramDocu">Parameters<ul>
           <li id="3"><span class="optParam">option</span><span class="paramType">MIN,MAX</span> <span class="paramDesc">Optional! nothing to query actual ER and Phi. MIN to get minimal ER_min and Phi_min. MAX to get maximal ER_max and Phi_max.</span></li>
        </ul></div>

        <div class="resultDocu">Response<ul>
          <li><span class="param">er</span><span class="paramType">float</span> <span class="paramDesc">Extinction Ratio in dB</span></li>
          <li><span class="param">phi</span><span class="paramType">float</span> <span class="paramDesc">Phase shift in degree</span></li>
        </ul></div>

        <div class="details"> Call this command to start a new measurement of the Extinction Ratio (ER). Once the measurement has been taken the command will return the ER value in dB and the phase shift in degree. The value is updated at 10 Hz. An optional floating averaging window <a class="linkSCPI" title="Sets the averaging for ER measurement" href="#SENS:AVER">SENS:AVER</a> might be used to low pass filter the measurement value.<br /> The same function with the optional MIN parameter can be used to get the minimal ER measurement. To reset the minima use <a class="linkSCPI" title="Resets ER_min measurement" href="#SENS:ER:RES">SENS:ER:RES</a>.</div>
        <div class="example">&gt;&gt; MEAS?<br />&lt;&lt; 12.23, -34.3465</div>
        <div class="info">This command is synchronized with the internal measurement update of 10 Hz</div>
      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="MEAS">
        <span class="scpi" id="MEAS:POW?">MEAS:POW?</span><span class="briefScpi">Measures power of light</span>
      </div>
      <div class="content">
        <div class="scpiLong">MEASure[:SCALar]:POWer?</div>
        <div class="resultDocu">Response<ul>
          <li><span class="param">power</span><span class="paramType">float</span> <span class="paramDesc">Power of light in dBm or Watt.</span></li>
        </ul></div>

        <div class="details"> Call this command to start a new measurement of the light power in dBm or Watt. Ensure you selected the correct light wavelength by using command <a class="linkSCPI" title="Gets the wavelength" href="#SENS:CORR:WAV?">SENS:CORR:WAV?</a>. By default the result is in dBm but you might change the unit to Watt by using command <a class="linkSCPI" title="Selects the power unit" href="#SENS:POW:UNIT">SENS:POW:UNIT</a>. The value is updated at 10 Hz.</div>
        <div class="example">&gt;&gt; MEAS:POW?<br />&lt;&lt; 0.532</div>
        <div class="info">This command is synchronized with the internal measurement update of 10 Hz</div>
      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="MEAS">
        <span class="scpi" id="MEAS:VEL?">MEAS:VEL?</span><span class="briefScpi">Measures speed of encoder</span>
      </div>
      <div class="content">
        <div class="scpiLong">MEASure[:SCALar][:ENCOder]:VELocity?</div>
        <div class="resultDocu">Response<ul>
          <li><span class="param">speed</span><span class="paramType">uint</span> <span class="paramDesc">Turns per Second of encoder wheel</span></li>
        </ul></div>

        <div class="details"> Call this command to return the recently measured speed of the encoder wheel in turns per Second. By default the speed should be between 9 and 11.</div>
        <div class="example">&gt;&gt; MEAS:VEL?<br />&lt;&lt; 10</div>
        <div class="info">This command is not synchronized with the internal update rate. Calling faster will returns same value multiple times.</div>
      </div>
    </div>
    <hr>
    <p id="SENS"><a href="#SENS:AVER" data-subsystem="SENS" class="unfoldSection">Unfold all SENS Commands</a></p>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="SENS">
        <span class="scpi" id="SENS:AVER">SENS:AVER</span><span class="briefScpi">Sets the averaging for ER measurement</span>
      </div>
      <div class="content">
        <div class="scpiLong">SENSe:AVERage[:COUNt] <div class="tooltip" data-id="14">&lt;pres&gt;</div></div>
        <div class="paramDocu">Parameters<ul>
              <li id="14"><span class="param">pres</span><span class="paramType">uint, MIN, MAX</span> <span class="paramDesc">whole number for window width between 1 and 30. Use MIN or MAX to set special parameter.</span></li>
        </ul></div>

        <div class="details">This command sets the averaging for ER measurement low pass filtering. The sliding average filter is implemented as a logical window of the recent measurements. Internally a new measurement is add to the window while the oldest element drops out of the window. All values within the window are averaged. This type of averaging is no prescaler of the internal update rate as a new averaged result is output for every input.</div>
        <div class="example">&gt;&gt; SENS:AVER 10</div>
        <div class="info">Averaging is not applied for power measurements.</div>
      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="SENS">
        <span class="scpi" id="SENS:AVER?">SENS:AVER?</span><span class="briefScpi">Gets the averaging for ER measurement</span>
      </div>
      <div class="content">
        <div class="scpiLong">SENSe:AVERage[:COUNt]? <div class="tooltip" data-id="15">&lt;special&gt;</div></div>
        <div class="paramDocu">Parameters<ul>
           <li id="15"><span class="optParam">special</span><span class="paramType">MIN,MAX</span> <span class="paramDesc">Optional! nothing to query actual value. MIN or MAX to query special average values.</span></li>
        </ul></div>

        <div class="resultDocu">Response<ul>
          <li><span class="param">pres</span><span class="paramType">uint</span> <span class="paramDesc">whole number for window width</span></li>
        </ul></div>

        <div class="details">This command gets the averaging for ER measurement low pass filtering. For closer details read <a class="linkSCPI" title="Sets the averaging for ER measurement" href="#SENS:AVER">SENS:AVER</a>.</div>
        <div class="example">&gt;&gt; SENS:AVER?<br />&lt;&lt; 10</div>
        <div class="info">Averaging is not applied for power measurements.</div>
      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="SENS">
        <span class="scpi" id="SENS:CORR:WAV">SENS:CORR:WAV</span><span class="briefScpi">Sets the wavelength</span>
      </div>
      <div class="content">
        <div class="scpiLong">SENSe:CORRection:WAVelength <div class="tooltip" data-id="12">&lt;wavelength&gt;</div></div>
        <div class="paramDocu">Parameters<ul>
              <li id="12"><span class="param">wavelength</span><span class="paramType">uint,MIN,MAX</span> <span class="paramDesc">wavelength as whole number in nm. MIN or MAX to set special values.</span></li>
        </ul></div>

        <div class="details">Use this command to set wavelength of light in nm. The wavelength affects the results of the <a class="linkSCPI" title="Measures power of light" href="#MEAS:POW?">MEAS:POW?</a> command. Wavelength parameter is stored persistently and restored after reboot.</div>
        <div class="example">&gt;&gt; SENS:CORR:WAV 650</div>

      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="SENS">
        <span class="scpi" id="SENS:CORR:WAV?">SENS:CORR:WAV?</span><span class="briefScpi">Gets the wavelength</span>
      </div>
      <div class="content">
        <div class="scpiLong">SENSe:CORRection:WAVelength? <div class="tooltip" data-id="11">&lt;special&gt;</div></div>
        <div class="paramDocu">Parameters<ul>
           <li id="11"><span class="optParam">special</span><span class="paramType">MIN,MAX</span> <span class="paramDesc">Optional! nothing to query actual value. MIN or MAX to query special wavelength values.</span></li>
        </ul></div>

        <div class="resultDocu">Response<ul>
          <li><span class="param">wavelength</span><span class="paramType">float</span> <span class="paramDesc">wavelength in nm</span></li>
        </ul></div>

        <div class="details">Use this command to query the wavelength of light in nm. The wavelength affects the results of the <a class="linkSCPI" title="Measures power of light" href="#MEAS:POW?">MEAS:POW?</a> command.</div>
        <div class="example">&gt;&gt; SENS:CORR:WAV? MIN<br />&lt;&lt; 400</div>
        <div class="note">Only supported for sensor heads with EEPROM. Adapter types have to get responsivity directly.</div>

      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="SENS">
        <span class="scpi" id="SENS:ER:RES">SENS:ER:RES</span><span class="briefScpi">Resets ER_min measurement</span>
      </div>
      <div class="content">
        <div class="scpiLong">SENSe:ER:RESet</div>
        <div class="example">&gt;&gt; SENS:ER:RES</div>

      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="SENS">
        <span class="scpi" id="SENS:POW:UNIT">SENS:POW:UNIT</span><span class="briefScpi">Selects the power unit</span>
      </div>
      <div class="content">
        <div class="scpiLong">SENSe:POWer:UNIT <div class="tooltip" data-id="13">&lt;unit&gt;</div></div>
        <div class="paramDocu">Parameters<ul>
              <li id="13"><span class="param">unit</span><span class="paramType">W, DBM</span> <span class="paramDesc">choose power unit between Watt or dBm.</span></li>
        </ul></div>

        <div class="details">Use this command to change the power unit between Watt and dBm. The unit affects the results of the <a class="linkSCPI" title="Measures power of light" href="#MEAS:POW?">MEAS:POW?</a> command. This parameter is not stored persistently. The unit is always dBm after reboot.</div>
        <div class="example">&gt;&gt; SENS:POW:UNIT DBM</div>

      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="SENS">
        <span class="scpi" id="SENS:POW:UNIT?">SENS:POW:UNIT?</span><span class="briefScpi">Returns the power unit</span>
      </div>
      <div class="content">
        <div class="scpiLong">SENSe:POWer:UNIT?</div>
        <div class="resultDocu">Response<ul>
          <li><span class="param">unit</span><span class="paramType">W, DBM</span> <span class="paramDesc">power unit.</span></li>
        </ul></div>

        <div class="details">Use this command to query the actual used power unit. For closer details read command  <a class="linkSCPI" title="Selects the power unit" href="#SENS:POW:UNIT">SENS:POW:UNIT</a>.</div>
        <div class="example">&gt;&gt; SENS:POW:UNIT?<br />&lt;&lt; DBM</div>

      </div>
    </div>
    <hr>
    <p id="STAT"><a href="#STAT:OPER?" data-subsystem="STAT" class="unfoldSection">Unfold all STAT Commands</a></p>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="STAT">
        <span class="scpi" id="STAT:OPER?">STAT:OPER?</span><span class="briefScpi">Queries the SCPI Operation Event register</span>
      </div>
      <div class="content">
        <div class="scpiLong">STATus:OPERation[:EVENt]?</div>
        <div class="resultDocu">Response<ul>
          <li><span class="param">bitmask</span><span class="paramType">uint</span> <span class="paramDesc">16 bit wide event register</span></li>
        </ul></div>

        <div class="details">Use this command to query the SCPI 16 bit wide Operation Event register bit mask. The operation register set monitors the operating state of the measurement system.<br/> Reading this event register automatically resets the register back to 0. The register content is a bit mask with a 1 bit for every event occurred since the register has been read the last time. If the same event occurs multiple times the event bit stays 1. To get the meaning of the single bits read the <a class="linkSCPI" title="Queries the SCPI Operation Condition register" href="#STAT:OPER:COND?">STAT:OPER:COND?</a> description. The event register can store a change of the Operation Condition register. The two transition registers are used to mask changes of the condition register to the event register. If a certain bit in the condition register is set from 0 to 1 and the bit at the same position in the positive transition register <a class="linkSCPI" title="Sets the SCPI Operation Positive Transition register" href="#STAT:OPER:PTR">STAT:OPER:PTR</a> is set 1, the event register at the bit position is set to 1. It is also possible to detect condition register changes from 1 back to 0 using the negative transition register <a class="linkSCPI" title="Sets the SCPI Operation Negative Transition register" href="#STAT:OPER:NTR">STAT:OPER:NTR</a> following the same logic. <br/> Using the event register makes sense for short lasting conditions. Changes of the event register can be summarized to the SCPI status register bit 7 if enabled by <a class="linkSCPI" title="Sets the SCPI Operation Enable register" href="#STAT:OPER:ENAB">STAT:OPER:ENAB</a>. To read the SCPI status register call <a class="linkSCPI" title="Status byte query" href="#STB?">STB?</a>. After reboot or <a class="linkSCPI" title="Clears all SCPI event status registers" href="#CLS">CLS</a> the events are all reset to 0.<br/> Read <a href="#statusRegisterSetDesc" title="General SCPI Status register description">SCPI register set</a> section to get closer infos about the SCPI register system in general.</div>
        <div class="example">&gt;&gt; STAT:OPER?<br />&lt;&lt; 253</div>
        <div class="note">Some events might get lost if same event occurs multiple times without the event register being read in between.</div>
        <div class="info">Reading the event register automatically resets register to 0. To get the events bit positions read <a class="linkSCPI" title="Queries the SCPI Operation Condition register" href="#STAT:OPER:COND?">STAT:OPER:COND?</a>.</div>
      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="STAT">
        <span class="scpi" id="STAT:OPER:COND?">STAT:OPER:COND?</span><span class="briefScpi">Queries the SCPI Operation Condition register</span>
      </div>
      <div class="content">
        <div class="scpiLong">STATus:OPERation:CONDition?</div>
        <div class="resultDocu">Response<ul>
          <li><span class="param">bitmask</span><span class="paramType">uint</span> <span class="paramDesc">16 bit wide condition register.</span></li>
        </ul></div>

        <div class="details">Use this command to query the SCPI 16 bit wide Operation Condition register bit mask. The operation register set monitors the operating state of the measurement system.<br/> The register content is a bit mask with a 1 bit for every condition being active at the moment. Be aware some conditions might only last for a very short time period. In this case using the event register <a class="linkSCPI" title="Queries the SCPI Operation Event register" href="#STAT:OPER?">STAT:OPER?</a> makes more sense. Configure the positive <a class="linkSCPI" title="Sets the SCPI Operation Positive Transition register" href="#STAT:OPER:PTR">STAT:OPER:PTR</a> or negative <a class="linkSCPI" title="Sets the SCPI Operation Negative Transition register" href="#STAT:OPER:NTR">STAT:OPER:NTR</a> transition register to map changes of the condition register to the event register. These are the masked events:<br/>  <svg width="200px" viewBox="-1239 605 283 327" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">  <g>    <rect style="fill: #ffffff" x="-1096.75" y="607.309" width="140" height="324"/>    <rect style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x="-1096.75" y="607.309" width="140" height="324"/>    <text font-size="14.1111" style="fill: #000000;text-anchor:end;font-family:sans-serif;font-style:normal;font-weight:700" x="-965.749" y="764.77">      <tspan x="-965.749" y="764.77">STAT:</tspan>      <tspan x="-965.749" y="782.409">OPER</tspan>    </text>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="-1236.75" y="617.309">    <tspan x="-1236.75" y="617.309">reserved</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="-1236.75" y="637.309">    <tspan x="-1236.75" y="637.309">Powering Up</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="-1236.75" y="657.309">    <tspan x="-1236.75" y="657.309">reserved</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="-1216.75" y1="619.309" x2="-1101.22" y2="619.309"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="-1108.99,624.309 -1098.99,619.309 -1108.99,614.309 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="-1092.75" y="623.309">    <tspan x="-1092.75" y="623.309">bit 0(1)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="-1236.75" y="677.309">    <tspan x="-1236.75" y="677.309">reserved</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="-1216.75" y1="639.309" x2="-1101.22" y2="639.309"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="-1108.99,644.309 -1098.99,639.309 -1108.99,634.309 "/>  </g>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="-1216.75" y1="659.309" x2="-1101.22" y2="659.309"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="-1108.99,664.309 -1098.99,659.309 -1108.99,654.309 "/>  </g>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="-1216.75" y1="679.309" x2="-1101.22" y2="679.309"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="-1108.99,684.309 -1098.99,679.309 -1108.99,674.309 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="-1238.05" y="697.309">    <tspan x="-1238.05" y="697.309">Measurement active</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="-1216.75" y1="699.309" x2="-1101.22" y2="699.309"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="-1108.99,704.309 -1098.99,699.309 -1108.99,694.309 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="-1236.75" y="717.309">    <tspan x="-1236.75" y="717.309">reserved</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="-1216.75" y1="719.309" x2="-1101.22" y2="719.309"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="-1108.99,724.309 -1098.99,719.309 -1108.99,714.309 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="-1236.75" y="737.309">    <tspan x="-1236.75" y="737.309">resvered</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="-1216.75" y1="739.309" x2="-1101.22" y2="739.309"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="-1108.99,744.309 -1098.99,739.309 -1108.99,734.309 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="-1236.75" y="757.309">    <tspan x="-1236.75" y="757.309">reserved</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="-1216.75" y1="759.309" x2="-1101.22" y2="759.309"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="-1108.99,764.309 -1098.99,759.309 -1108.99,754.309 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="-1236.75" y="777.309">    <tspan x="-1236.75" y="777.309">Motor started</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="-1216.75" y1="779.309" x2="-1101.22" y2="779.309"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="-1108.99,784.309 -1098.99,779.309 -1108.99,774.309 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="-1236.75" y="797.309">    <tspan x="-1236.75" y="797.309">Encoder rotating</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="-1216.75" y1="799.309" x2="-1101.22" y2="799.309"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="-1108.99,804.309 -1098.99,799.309 -1108.99,794.309 "/>  </g>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="-1216.75" y1="819.309" x2="-1101.22" y2="819.309"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="-1108.99,824.309 -1098.99,819.309 -1108.99,814.309 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="-1236.75" y="837.309">    <tspan x="-1236.75" y="837.309">Power data to fetch</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="-1216.75" y1="839.309" x2="-1101.22" y2="839.309"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="-1108.99,844.309 -1098.99,839.309 -1108.99,834.309 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="-1236.75" y="857.309">    <tspan x="-1236.75" y="857.309">reserved</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="-1216.75" y1="859.309" x2="-1101.22" y2="859.309"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="-1108.99,864.309 -1098.99,859.309 -1108.99,854.309 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="-1236.75" y="917.309">    <tspan x="-1236.75" y="917.309">reserved</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="-1216.75" y1="919.309" x2="-1101.22" y2="919.309"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="-1108.99,924.309 -1098.99,919.309 -1108.99,914.309 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="-1092.75" y="643.309">    <tspan x="-1092.75" y="643.309">bit 1(2)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="-1092.75" y="663.309">    <tspan x="-1092.75" y="663.309">bit 2(4)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="-1092.75" y="683.309">    <tspan x="-1092.75" y="683.309">bit 3(8)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="-1092.75" y="703.309">    <tspan x="-1092.75" y="703.309">bit 4(16)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="-1092.75" y="723.309">    <tspan x="-1092.75" y="723.309">bit 5(32)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="-1092.75" y="743.309">    <tspan x="-1092.75" y="743.309">bit 6(64)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="-1092.75" y="763.309">    <tspan x="-1092.75" y="763.309">bit 7(128)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="-1092.75" y="783.309">    <tspan x="-1092.75" y="783.309">bit 8(256)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="-1092.75" y="803.309">    <tspan x="-1092.75" y="803.309">bit 9(512)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="-1092.75" y="823.309">    <tspan x="-1092.75" y="823.309">bit 10(1024)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="-1092.75" y="843.309">    <tspan x="-1092.75" y="843.309">bit 11(2048)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="-1092.75" y="863.309">    <tspan x="-1092.75" y="863.309">bit 12(4096)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="-1092.75" y="883.309">    <tspan x="-1092.75" y="883.309">bit 13(8192)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="-1092.75" y="903.309">    <tspan x="-1092.75" y="903.309">bit 14(16384)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="-1092.75" y="923.309">    <tspan x="-1092.75" y="923.309">bit 15(32768)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="-1236.75" y="815.309">    <tspan x="-1236.75" y="815.309">ER data to fetch</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="-1236.75" y="877.309">    <tspan x="-1236.75" y="877.309">reserved</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="-1216.75" y1="879.309" x2="-1101.22" y2="879.309"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="-1108.99,884.309 -1098.99,879.309 -1108.99,874.309 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="-1236.75" y="897.309">    <tspan x="-1236.75" y="897.309">reserved</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="-1216.75" y1="899.309" x2="-1101.22" y2="899.309"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="-1108.99,904.309 -1098.99,899.309 -1108.99,894.309 "/>  </g></svg> <ul class="detailListing"> <li><b>bit 1:</b> Power up condition.</li> <li><b>bit 4:</b> Measurement active condition.</li> <li><b>bit 8:</b> Motor started condition.</li> <li><b>bit 9:</b> Encoder rotating condition.</li> <li><b>bit 10:</b> ER data ready to fetch condition.</li> <li><b>bit 11:</b> Power data ready to fetch condition. </li> </ul> All other condition bits are reserved. Read <a href="#statusRegisterSetDesc" title="General SCPI Status register description">SCPI register set</a> section to get closer infos about the SCPI register system in general.</div>
        <div class="example">&gt;&gt; STAT:OPER:COND?<br />&lt;&lt; 2182</div>
        <div class="info">To test for short lasting conditions like a range change use the event register <a class="linkSCPI" title="Queries the SCPI Operation Event register" href="#STAT:OPER?">STAT:OPER?</a>.</div>
      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="STAT">
        <span class="scpi" id="STAT:OPER:ENAB">STAT:OPER:ENAB</span><span class="briefScpi">Sets the SCPI Operation Enable register</span>
      </div>
      <div class="content">
        <div class="scpiLong">STATus:OPERation:ENABle <div class="tooltip" data-id="4">&lt;bitmask&gt;</div></div>
        <div class="paramDocu">Parameters<ul>
              <li id="4"><span class="param">bitmask</span><span class="paramType">uint</span> <span class="paramDesc">16 bit wide bit mask for enable register.</span></li>
        </ul></div>

        <div class="details">Use this command to set the SCPI 16 bit wide Operation Enable register bit mask. The register content is a bit mask with a 1 bit for every enabled event that should be forwarded to the 8 bit wide SCPI status register bit 7. See <a class="linkSCPI" title="Status byte query" href="#STB?">STB?</a>. To get the meaning of the single bits read the <a class="linkSCPI" title="Queries the SCPI Operation Condition register" href="#STAT:OPER:COND?">STAT:OPER:COND?</a> description. This enable register acts like a filter mask for all operation events (see <a class="linkSCPI" title="Queries the SCPI Operation Event register" href="#STAT:OPER?">STAT:OPER?</a>) that should be forwarded to the SCPI status register. If the enable mask is set with all bits 1, the SCPI status register bit 7 is set to 1 whenever one or multiple events are set in <a class="linkSCPI" title="Queries the SCPI Operation Event register" href="#STAT:OPER?">STAT:OPER?</a> register.<br/> The idea behind this abstraction concept is to read the SCPI status register and check for bit 7 to change. Whenever the bit changed the user then may query this Operation Event register to get the details. Changing the enable register with active events might directly update the summarized SCPI status register bit.<br/> Read <a href="#statusRegisterSetDesc" title="General SCPI Status register description">SCPI register set</a> section to get closer infos about the SCPI register system in general.</div>
        <div class="example">&gt;&gt; STAT:OPER:ENAB 65280</div>
        <div class="note">Ensure that the positive and negative transition registers are set accordingly to change the event register. Forwarding only works if event register gets updated.</div>
        <div class="info">The default value after reboot or <a class="linkSCPI" title="Clears all SCPI event status registers" href="#CLS">CLS</a> is 0. So no events are forwarded.</div>
      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="STAT">
        <span class="scpi" id="STAT:OPER:ENAB?">STAT:OPER:ENAB?</span><span class="briefScpi">Queries the SCPI Operation Enable register</span>
      </div>
      <div class="content">
        <div class="scpiLong">STATus:OPERation:ENABle?</div>
        <div class="resultDocu">Response<ul>
          <li><span class="param">bitmask</span><span class="paramType">uint</span> <span class="paramDesc">16 bit wide enable register.</span></li>
        </ul></div>

        <div class="details">Use this command to query the SCPI 16 bit wide Operation Enable register bit mask. The register content is a bit mask with a 1 bit for every enabled event that should be forwarded to the 8 bit wide SCPI status register bit 7. To get the meaning of the single bits read the <a class="linkSCPI" title="Queries the SCPI Operation Condition register" href="#STAT:OPER:COND?">STAT:OPER:COND?</a> description. For closer details about the Operation Enable register read <a class="linkSCPI" title="Sets the SCPI Operation Enable register" href="#STAT:OPER:ENAB">STAT:OPER:ENAB</a>.<br/> Read <a href="#statusRegisterSetDesc" title="General SCPI Status register description">SCPI register set</a> section to get closer infos about the SCPI register system in general.</div>
        <div class="example">&gt;&gt; STAT:OPER:ENAB?<br />&lt;&lt; 65280</div>
        <div class="note">Ensure that the positive or negative transition registers are set accordingly to change the event register. Forwarding only works if event register gets updated.</div>
        <div class="info">The default value after reboot or <a class="linkSCPI" title="Clears all SCPI event status registers" href="#CLS">CLS</a> is 0. So no events are forwarded.</div>
      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="STAT">
        <span class="scpi" id="STAT:OPER:NTR">STAT:OPER:NTR</span><span class="briefScpi">Sets the SCPI Operation Negative Transition register</span>
      </div>
      <div class="content">
        <div class="scpiLong">STATus:OPERation:NTRansition <div class="tooltip" data-id="6">&lt;bitmask&gt;</div></div>
        <div class="paramDocu">Parameters<ul>
              <li id="6"><span class="param">bitmask</span><span class="paramType">uint</span> <span class="paramDesc">16 bit wide bit mask for positive transition register.</span></li>
        </ul></div>

        <div class="details">Use this command to set the SCPI 16 bit wide Operation Negative Transition register bit mask. The register content is a bit mask with a 1 bit for every condition change that should be mapped to the operation event register <a class="linkSCPI" title="Queries the SCPI Operation Event register" href="#STAT:OPER?">STAT:OPER?</a>. As it is the negative transition register, only condition register bit changes from 1 back to 0 update the bits in the event register. To get the meaning of the single bits read the <a class="linkSCPI" title="Queries the SCPI Operation Condition register" href="#STAT:OPER:COND?">STAT:OPER:COND?</a> description. For example if you set this register value to 8 (binary: 1000 bit at position 3) and the motor gets stopped (condition register at position 3 becomes 0) it will also set the event register value bit at position 3 to 1.<br/> Read <a href="#statusRegisterSetDesc" title="General SCPI Status register description">SCPI register set</a> section to get closer infos about the SCPI register system in general.</div>
        <div class="example">&gt;&gt; STAT:OPER:NTR 260</div>
        <div class="info">The default value after reboot or <a class="linkSCPI" title="Clears all SCPI event status registers" href="#CLS">CLS</a> is 0. So no condition changes from bit 1 to 0 update the event register.</div>
      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="STAT">
        <span class="scpi" id="STAT:OPER:NTR?">STAT:OPER:NTR?</span><span class="briefScpi">Queries the SCPI Operation Negative Transition register</span>
      </div>
      <div class="content">
        <div class="scpiLong">STATus:OPERation:NTRansition?</div>
        <div class="resultDocu">Response<ul>
          <li><span class="param">bitmask</span><span class="paramType">uint</span> <span class="paramDesc">16 bit wide bit mask for positive transition register.</span></li>
        </ul></div>

        <div class="details">Use this command to query the SCPI 16 bit wide Operation Negative Transition register bit mask. The register content is a bit mask with a 1 bit for every condition change that should be mapped to the operation event register <a class="linkSCPI" title="Queries the SCPI Operation Event register" href="#STAT:OPER?">STAT:OPER?</a>. For closer details about Negative Transition register read description <a class="linkSCPI" title="Sets the SCPI Operation Negative Transition register" href="#STAT:OPER:NTR">STAT:OPER:NTR</a>.<br/> Read <a href="#statusRegisterSetDesc" title="General SCPI Status register description">SCPI register set</a> section to get closer infos about the SCPI register system in general.</div>
        <div class="example">&gt;&gt; STAT:OPER:NTR?<br />&lt;&lt; 260</div>
        <div class="info">The default value after reboot or <a class="linkSCPI" title="Clears all SCPI event status registers" href="#CLS">CLS</a> is 0. So no condition changes from bit 1 to 0 update the event register.</div>
      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="STAT">
        <span class="scpi" id="STAT:OPER:PTR">STAT:OPER:PTR</span><span class="briefScpi">Sets the SCPI Operation Positive Transition register</span>
      </div>
      <div class="content">
        <div class="scpiLong">STATus:OPERation:PTRansition <div class="tooltip" data-id="5">&lt;bitmask&gt;</div></div>
        <div class="paramDocu">Parameters<ul>
              <li id="5"><span class="param">bitmask</span><span class="paramType">uint</span> <span class="paramDesc">16 bit wide bit mask for positive transition register.</span></li>
        </ul></div>

        <div class="details">Use this command to set the SCPI 16 bit wide Operation Positive Transition register bit mask. The register content is a bit mask with a 1 bit for every condition change that should be mapped to the Operation Event register <a class="linkSCPI" title="Queries the SCPI Operation Event register" href="#STAT:OPER?">STAT:OPER?</a>. As it is the positive transition register, only condition register bit changes from 0 to 1 update the bits in the event register. To get the meaning of the single bits read the <a class="linkSCPI" title="Queries the SCPI Operation Condition register" href="#STAT:OPER:COND?">STAT:OPER:COND?</a> description. For example if you set this register value to 8 (binary: 1000 bit at position 3) and the motor gets started (condition register at position 3) it will also set the event register value bit at position 3 to 1.<br/> Read <a href="#statusRegisterSetDesc" title="General SCPI Status register description">SCPI register set</a> section to get closer infos about the SCPI register system in general.</div>
        <div class="example">&gt;&gt; STAT:OPER:PTR 36</div>
        <div class="info">The default value after reboot or <a class="linkSCPI" title="Clears all SCPI event status registers" href="#CLS">CLS</a> is 65535 (all bits 1). So all condition changes from bit 0 to 1 update the event register.</div>
      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="STAT">
        <span class="scpi" id="STAT:OPER:PTR?">STAT:OPER:PTR?</span><span class="briefScpi">Queries the SCPI Operation Positive Transition register</span>
      </div>
      <div class="content">
        <div class="scpiLong">STATus:OPERation:PTRansition?</div>
        <div class="resultDocu">Response<ul>
          <li><span class="param">bitmask</span><span class="paramType">uint</span> <span class="paramDesc">16 bit wide bit mask for positive transition register.</span></li>
        </ul></div>

        <div class="details">Use this command to query the SCPI 16 bit wide Operation Positive Transition register bit mask. The register content is a bit mask with a 1 bit for every condition change that should be mapped to the operation event register <a class="linkSCPI" title="Queries the SCPI Operation Event register" href="#STAT:OPER?">STAT:OPER?</a>. For closer details about Positive Transition register read description <a class="linkSCPI" title="Sets the SCPI Operation Positive Transition register" href="#STAT:OPER:PTR">STAT:OPER:PTR</a>.<br/> Read <a href="#statusRegisterSetDesc" title="General SCPI Status register description">SCPI register set</a> section to get closer infos about the SCPI register system in general.</div>
        <div class="example">&gt;&gt; STAT:OPER:PTR?<br />&lt;&lt; 65280</div>
        <div class="info">The default value after reboot or <a class="linkSCPI" title="Clears all SCPI event status registers" href="#CLS">CLS</a> is 65535 (all bits 1). So all condition changes from bit 0 to 1 update the event register.</div>
      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="STAT">
        <span class="scpi" id="STAT:PRES">STAT:PRES</span><span class="briefScpi">Pre-sets all SCPI status register with default values</span>
      </div>
      <div class="content">
        <div class="scpiLong">STATus:PRESet</div>
        <div class="details">Use this command to pre set all SCPI status registers with default values. All ENABle and NTRansition Registers are set to 0. The PTRansition registers are set to 65535. The event registers are cleared to 0.</div>
        <div class="example">&gt;&gt; STAT:PRES</div>

      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="STAT">
        <span class="scpi" id="STAT:QUES?">STAT:QUES?</span><span class="briefScpi">Queries the SCPI Questionable Event register</span>
      </div>
      <div class="content">
        <div class="scpiLong">STATus:QUEStionable[:EVENt]?</div>
        <div class="resultDocu">Response<ul>
          <li><span class="param">bitmask</span><span class="paramType">uint</span> <span class="paramDesc">16 bit wide event register bit mask.</span></li>
        </ul></div>

        <div class="details">Use this command to query the SCPI 16 bit wide Questionable Event register bit mask. The Questionable register set monitors the validity of the measurement results.<br/> Reading this event register automatically resets the register back to 0. The register content is a bit mask with a 1 bit for every event occurred since the register has been read the last time. If the same event occurs multiple times the event bit stays 1. To get the meaning of the single bits read the <a class="linkSCPI" title="Queries the SCPI Questionable Condition register" href="#STAT:QUES:COND?">STAT:QUES:COND?</a> description. The event register can store a change of the Questionable Condition register. The two transition registers are used to mask changes of the condition register to the event register. If a certain bit in the condition register is set from 0 to 1 and the bit at the same position in the positive transition register <a class="linkSCPI" title="Sets the SCPI Questionable Positive Transition register" href="#STAT:QUES:PTR">STAT:QUES:PTR</a> is set 1, the event register at the bit position is set to 1. It is also possible to detect condition register changes from 1 back to 0 using the negative transition register <a class="linkSCPI" title="Sets the SCPI Questionable Negative Transition register" href="#STAT:QUES:NTR">STAT:QUES:NTR</a> following the same logic. <br/> Using the event register makes sense for short lasting conditions like a small peak causing underflow of power measurement for example. Changes of the event register can be summarized to the SCPI status register bit 3 if enabled by <a class="linkSCPI" title="Sets the SCPI Questionable Enable register" href="#STAT:QUES:ENAB">STAT:QUES:ENAB</a>. To read the SCPI status register call <a class="linkSCPI" title="Status byte query" href="#STB?">STB?</a>. After reboot or <a class="linkSCPI" title="Clears all SCPI event status registers" href="#CLS">CLS</a> the events are all reset to 0.<br/> Read <a href="#statusRegisterSetDesc" title="General SCPI Status register description">SCPI register set</a> section to get closer infos about the SCPI register system in general.</div>
        <div class="example">&gt;&gt; STAT:QUES?<br />&lt;&lt; 253</div>
        <div class="note">Some events might get lost if same event occurs multiple times without the event register being read in between.</div>
        <div class="info">Reading the event register automatically resets register to 0. To get the events bit positions read <a class="linkSCPI" title="Queries the SCPI Questionable Condition register" href="#STAT:QUES:COND?">STAT:QUES:COND?</a>.</div>
      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="STAT">
        <span class="scpi" id="STAT:QUES:COND?">STAT:QUES:COND?</span><span class="briefScpi">Queries the SCPI Questionable Condition register</span>
      </div>
      <div class="content">
        <div class="scpiLong">STATus:QUEStionable:CONDition?</div>
        <div class="resultDocu">Response<ul>
          <li><span class="param">bitmask</span><span class="paramType">uint</span> <span class="paramDesc">16 bit wide condition register bit mask.</span></li>
        </ul></div>

        <div class="details">Use this command to query the SCPI 16 bit wide Questionable Condition register bit mask. The Questionable register set monitors the validity of the measurement results.<br/> The register content is a bit mask with a 1 bit for every condition being active at the moment. Be aware some conditions might only last for a very short time period like a small peak causing overflow of measure range for example. In this case using the event register <a class="linkSCPI" title="Queries the SCPI Questionable Event register" href="#STAT:QUES?">STAT:QUES?</a> makes more sense. Configure the positive <a class="linkSCPI" title="Sets the SCPI Questionable Positive Transition register" href="#STAT:QUES:PTR">STAT:QUES:PTR</a> or negative <a class="linkSCPI" title="Sets the SCPI Questionable Negative Transition register" href="#STAT:QUES:NTR">STAT:QUES:NTR</a> transition register to map changes of the condition register to the event register. These are the masked events:<br/> <svg width="200px" viewBox="-549 579 282 327" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">  <g>    <rect style="fill: #ffffff" x="-408.139" y="581.067" width="140" height="324"/>    <rect style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x="-408.139" y="581.067" width="140" height="324"/>    <text font-size="14.1111" style="fill: #000000;text-anchor:end;font-family:sans-serif;font-style:normal;font-weight:700" x="-277.139" y="738.528">      <tspan x="-277.139" y="738.528">STAT:</tspan>      <tspan x="-277.139" y="756.167">QUEST</tspan>    </text>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="-548.139" y="591.067">    <tspan x="-548.139" y="591.067">reserved</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="-548.139" y="611.067">    <tspan x="-548.139" y="611.067">reserved</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="-548.139" y="631.067">    <tspan x="-548.139" y="631.067">reserved</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="-528.139" y1="593.067" x2="-412.611" y2="593.067"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="-420.375,598.067 -410.375,593.067 -420.375,588.067 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="-404.139" y="597.067">    <tspan x="-404.139" y="597.067">bit 0(1)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="-548.139" y="651.067">    <tspan x="-548.139" y="651.067">Questionalbe Power</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="-528.139" y1="613.067" x2="-412.611" y2="613.067"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="-420.375,618.067 -410.375,613.067 -420.375,608.067 "/>  </g>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="-528.139" y1="633.067" x2="-412.611" y2="633.067"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="-420.375,638.067 -410.375,633.067 -420.375,628.067 "/>  </g>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="-528.139" y1="653.067" x2="-412.611" y2="653.067"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="-420.375,658.067 -410.375,653.067 -420.375,648.067 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="-548.139" y="671.067">    <tspan x="-548.139" y="671.067">reserved</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="-528.139" y1="673.067" x2="-412.611" y2="673.067"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="-420.375,678.067 -410.375,673.067 -420.375,668.067 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="-548.139" y="691.067">    <tspan x="-548.139" y="691.067">reserved</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="-528.139" y1="693.067" x2="-412.611" y2="693.067"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="-420.375,698.067 -410.375,693.067 -420.375,688.067 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="-548.139" y="711.067">    <tspan x="-548.139" y="711.067">reserved</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="-528.139" y1="713.067" x2="-412.611" y2="713.067"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="-420.375,718.067 -410.375,713.067 -420.375,708.067 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="-548.139" y="731.067">    <tspan x="-548.139" y="731.067">reserved</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="-528.139" y1="733.067" x2="-412.611" y2="733.067"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="-420.375,738.067 -410.375,733.067 -420.375,728.067 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="-548.139" y="751.067">    <tspan x="-548.139" y="751.067">Questionable Calibrat</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="-528.139" y1="753.067" x2="-412.611" y2="753.067"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="-420.375,758.067 -410.375,753.067 -420.375,748.067 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="-548.139" y="771.067">    <tspan x="-548.139" y="771.067">reserved</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="-528.139" y1="773.067" x2="-412.611" y2="773.067"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="-420.375,778.067 -410.375,773.067 -420.375,768.067 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="-548.139" y="791.067">    <tspan x="-548.139" y="791.067">reserved</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="-528.139" y1="793.067" x2="-412.611" y2="793.067"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="-420.375,798.067 -410.375,793.067 -420.375,788.067 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="-548.139" y="811.067">    <tspan x="-548.139" y="811.067">reserved</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="-528.139" y1="813.067" x2="-412.611" y2="813.067"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="-420.375,818.067 -410.375,813.067 -420.375,808.067 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="-548.139" y="831.067">    <tspan x="-548.139" y="831.067">reserved</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="-528.139" y1="833.067" x2="-412.611" y2="833.067"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="-420.375,838.067 -410.375,833.067 -420.375,828.067 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="-548.139" y="891.067">    <tspan x="-548.139" y="891.067">reserved</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="-528.139" y1="893.067" x2="-412.611" y2="893.067"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="-420.375,898.067 -410.375,893.067 -420.375,888.067 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="-404.139" y="617.067">    <tspan x="-404.139" y="617.067">bit 1(2)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="-404.139" y="637.067">    <tspan x="-404.139" y="637.067">bit 2(4)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="-404.139" y="657.067">    <tspan x="-404.139" y="657.067">bit 3(8)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="-404.139" y="677.067">    <tspan x="-404.139" y="677.067">bit 4(16)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="-404.139" y="697.067">    <tspan x="-404.139" y="697.067">bit 5(32)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="-404.139" y="717.067">    <tspan x="-404.139" y="717.067">bit 6(64)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="-404.139" y="737.067">    <tspan x="-404.139" y="737.067">bit 7(128)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="-404.139" y="757.067">    <tspan x="-404.139" y="757.067">bit 8(256)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="-404.139" y="777.067">    <tspan x="-404.139" y="777.067">bit 9(512)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="-404.139" y="797.067">    <tspan x="-404.139" y="797.067">bit 10(1024)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="-404.139" y="817.067">    <tspan x="-404.139" y="817.067">bit 11(2048)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="-404.139" y="837.067">    <tspan x="-404.139" y="837.067">bit 12(4096)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="-404.139" y="857.067">    <tspan x="-404.139" y="857.067">bit 13(8192)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="-404.139" y="877.067">    <tspan x="-404.139" y="877.067">bit 14(16384)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="-404.139" y="897.067">    <tspan x="-404.139" y="897.067">bit 15(32768)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="-548.139" y="851.067">    <tspan x="-548.139" y="851.067">reserved</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="-528.139" y1="853.067" x2="-412.611" y2="853.067"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="-420.375,858.067 -410.375,853.067 -420.375,848.067 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="-548.139" y="871.067">    <tspan x="-548.139" y="871.067">reserved</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="-528.139" y1="873.067" x2="-412.611" y2="873.067"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="-420.375,878.067 -410.375,873.067 -420.375,868.067 "/>  </g></svg> <ul class="detailListing"> <li><b>bit 3:</b> Questionable Power measurement. See <a class="linkSCPI" title="Measures power of light" href="#MEAS:POW?">MEAS:POW?</a>. </li> <li><b>bit 8:</b> Questionable device calibration.</li> </ul> All other condition bits are reserved. Read <a href="#statusRegisterSetDesc" title="General SCPI Status register description">SCPI register set</a> section to get closer infos about the SCPI register system in general.</div>
        <div class="example">&gt;&gt; STAT:QUES:COND?<br />&lt;&lt; 2182</div>
        <div class="info">To test for short lasting conditions better use the event register <a class="linkSCPI" title="Queries the SCPI Questionable Event register" href="#STAT:QUES?">STAT:QUES?</a>.</div>
      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="STAT">
        <span class="scpi" id="STAT:QUES:ENAB">STAT:QUES:ENAB</span><span class="briefScpi">Sets the SCPI Questionable Enable register</span>
      </div>
      <div class="content">
        <div class="scpiLong">STATus:QUEStionable:ENABle <div class="tooltip" data-id="7">&lt;bitmask&gt;</div></div>
        <div class="paramDocu">Parameters<ul>
              <li id="7"><span class="param">bitmask</span><span class="paramType">uint</span> <span class="paramDesc">16 bit wide bit mask for enable register.</span></li>
        </ul></div>

        <div class="details">Use this command to set the SCPI 16 bit wide Questionable Enable register bit mask. The register content is a bit mask with a 1 bit for every enabled event that should be forwarded to the 8 bit wide SCPI status register bit 3. See <a class="linkSCPI" title="Status byte query" href="#STB?">STB?</a>. To get the meaning of the single bits read the <a class="linkSCPI" title="Queries the SCPI Questionable Condition register" href="#STAT:QUES:COND?">STAT:QUES:COND?</a> description. This enable register acts like a filter mask for all operation events (see <a class="linkSCPI" title="Queries the SCPI Questionable Event register" href="#STAT:QUES?">STAT:QUES?</a>) that should be forwarded to the SCPI status register. If the enable mask is set with all bits 1, the SCPI status register bit 3 is set to 1 whenever one or multiple events are set in <a class="linkSCPI" title="Queries the SCPI Questionable Event register" href="#STAT:QUES?">STAT:QUES?</a> register.<br/> The idea behind this abstraction concept is to read the SCPI status register and check for bit 3 to change. Whenever the bit changed the user then may query this Questionable Event register to get the details. Changing the enable register with active events might directly update the summarized SCPI status register bit.<br/>  Read <a href="#statusRegisterSetDesc" title="General SCPI Status register description">SCPI register set</a> section to get closer infos about the SCPI register system in general.</div>
        <div class="example">&gt;&gt; STAT:QUES:ENAB 65280</div>
        <div class="note">Ensure that the positive and negative transition registers are set accordingly to change the event register. Forwarding only works if event register gets updated.</div>
        <div class="info">The default value after reboot or <a class="linkSCPI" title="Clears all SCPI event status registers" href="#CLS">CLS</a> is 0. So no events are forwarded.</div>
      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="STAT">
        <span class="scpi" id="STAT:QUES:ENAB?">STAT:QUES:ENAB?</span><span class="briefScpi">Queries the SCPI Questionable Enable register</span>
      </div>
      <div class="content">
        <div class="scpiLong">STATus:QUEStionable:ENABle?</div>
        <div class="resultDocu">Response<ul>
          <li><span class="param">bitmask</span><span class="paramType">uint</span> <span class="paramDesc">16 bit wide enable register bit mask.</span></li>
        </ul></div>

        <div class="details">Use this command to query the SCPI 16 bit wide Questionable Enable register bit mask. The register content is a bit mask with a 1 bit for every enabled event that should be forwarded to the 8 bit wide SCPI status register bit 7. To get the meaning of the single bits read the <a class="linkSCPI" title="Queries the SCPI Questionable Condition register" href="#STAT:QUES:COND?">STAT:QUES:COND?</a> description. For closer details about the Questionable Enable register read <a class="linkSCPI" title="Sets the SCPI Questionable Enable register" href="#STAT:QUES:ENAB">STAT:QUES:ENAB</a>.<br/> Read <a href="#statusRegisterSetDesc" title="General SCPI Status register description">SCPI register set</a> section to get closer infos about the SCPI register system in general.</div>
        <div class="example">&gt;&gt; STAT:QUES:ENAB<br />&lt;&lt; 65280</div>
        <div class="note">Ensure that the positive or negative transition registers are set accordingly to change the event register. Forwarding only works if event register gets updated.</div>
        <div class="info">The default value after reboot or <a class="linkSCPI" title="Clears all SCPI event status registers" href="#CLS">CLS</a> is 0. So no events are forwarded.</div>
      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="STAT">
        <span class="scpi" id="STAT:QUES:NTR">STAT:QUES:NTR</span><span class="briefScpi">Sets the SCPI Questionable Negative Transition register</span>
      </div>
      <div class="content">
        <div class="scpiLong">STATus:QUEStionable:NTRansition <div class="tooltip" data-id="9">&lt;bitmask&gt;</div></div>
        <div class="paramDocu">Parameters<ul>
              <li id="9"><span class="param">bitmask</span><span class="paramType">uint</span> <span class="paramDesc">16 bit wide bit mask for positive transition register.</span></li>
        </ul></div>

        <div class="details">Use this command to set the SCPI 16 bit wide Questionable Negative Transition register bit mask. The register content is a bit mask with a 1 bit for every condition change that should be mapped to the operation event register <a class="linkSCPI" title="Queries the SCPI Questionable Event register" href="#STAT:QUES?">STAT:QUES?</a>. As it is the negative transition register, only condition register bit changes from 1 back to 0 update the bits in the event register. To get the meaning of the single bits read the <a class="linkSCPI" title="Queries the SCPI Questionable Condition register" href="#STAT:QUES:COND?">STAT:QUES:COND?</a> description.<br/> Read <a href="#statusRegisterSetDesc" title="General SCPI Status register description">SCPI register set</a> section to get closer infos about the SCPI register system in general.</div>
        <div class="example">&gt;&gt; STAT:QUES:NTR 260</div>
        <div class="info">The default value after reboot or <a class="linkSCPI" title="Clears all SCPI event status registers" href="#CLS">CLS</a> is 0. So no condition changes from bit 1 to 0 update the event register.</div>
      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="STAT">
        <span class="scpi" id="STAT:QUES:NTR?">STAT:QUES:NTR?</span><span class="briefScpi">Queries the SCPI Questionable Negative Transition register</span>
      </div>
      <div class="content">
        <div class="scpiLong">STATus:QUEStionable:NTRansition?</div>
        <div class="resultDocu">Response<ul>
          <li><span class="param">bitmask</span><span class="paramType">uint</span> <span class="paramDesc">16 bit wide bit mask for positive transition register.</span></li>
        </ul></div>

        <div class="details">Use this command to query the SCPI 16 bit wide Questionable Negative Transition register bit mask. The register content is a bit mask with a 1 bit for every condition change that should be mapped to the operation event register <a class="linkSCPI" title="Queries the SCPI Questionable Event register" href="#STAT:QUES?">STAT:QUES?</a>. For closer details about Negative Transition register read description <a class="linkSCPI" title="Sets the SCPI Questionable Negative Transition register" href="#STAT:QUES:NTR">STAT:QUES:NTR</a>.<br/> Read <a href="#statusRegisterSetDesc" title="General SCPI Status register description">SCPI register set</a> section to get closer infos about the SCPI register system in general.</div>
        <div class="example">&gt;&gt; STAT:QUES:NTR?<br />&lt;&lt; 260</div>
        <div class="info">The default value after reboot or <a class="linkSCPI" title="Clears all SCPI event status registers" href="#CLS">CLS</a> is 0. So no condition changes from bit 1 to 0 update the event register.</div>
      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="STAT">
        <span class="scpi" id="STAT:QUES:PTR">STAT:QUES:PTR</span><span class="briefScpi">Sets the SCPI Questionable Positive Transition register</span>
      </div>
      <div class="content">
        <div class="scpiLong">STATus:QUEStionable:PTRansition <div class="tooltip" data-id="8">&lt;bitmask&gt;</div></div>
        <div class="paramDocu">Parameters<ul>
              <li id="8"><span class="param">bitmask</span><span class="paramType">uint</span> <span class="paramDesc">16 bit wide bit mask for positive transition register.</span></li>
        </ul></div>

        <div class="details">Use this command to set the SCPI 16 bit wide Questionable Positive Transition register bit mask. The register content is a bit mask with a 1 bit for every condition change that should be mapped to the Questionable Event register <a class="linkSCPI" title="Queries the SCPI Questionable Event register" href="#STAT:QUES?">STAT:QUES?</a>. As it is the positive transition register, only condition register bit changes from 0 to 1 update the bits in the event register. To get the meaning of the single bits read the <a class="linkSCPI" title="Queries the SCPI Questionable Condition register" href="#STAT:QUES:COND?">STAT:QUES:COND?</a> description. For example if you set this register value to 4 (binary: 100 bit at position 2) and the device measures a short peak causing a overflow of power  measurement the Questionable Power Measurement flag will become true for a short moment it will also set the event register value bit at position 2 to 1.<br/> Read <a href="#statusRegisterSetDesc" title="General SCPI Status register description">SCPI register set</a> section to get closer infos about the SCPI register system in general.</div>
        <div class="example">&gt;&gt; STAT:QUES:PTR?<br />&lt;&lt; 65280</div>
        <div class="info">The default value after reboot or <a class="linkSCPI" title="Clears all SCPI event status registers" href="#CLS">CLS</a> is 65535 (all bits 1). So all condition changes from bit 0 to 1 update the event register.</div>
      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="STAT">
        <span class="scpi" id="STAT:QUES:PTR?">STAT:QUES:PTR?</span><span class="briefScpi">Queries the SCPI Questionable Positive Transition register</span>
      </div>
      <div class="content">
        <div class="scpiLong">STATus:QUEStionable:PTRansition?</div>
        <div class="resultDocu">Response<ul>
          <li><span class="param">bitmask</span><span class="paramType">uint</span> <span class="paramDesc">16 bit wide bit mask for positive transition register.</span></li>
        </ul></div>

        <div class="details">Use this command to query the SCPI 16 bit wide Questionable Positive Transition register bit mask. The register content is a bit mask with a 1 bit for every condition change that should be mapped to the operation event register <a class="linkSCPI" title="Queries the SCPI Questionable Event register" href="#STAT:QUES?">STAT:QUES?</a>. For closer details about Positive Transition register read description <a class="linkSCPI" title="Sets the SCPI Questionable Positive Transition register" href="#STAT:QUES:PTR">STAT:QUES:PTR</a>.<br/> Read <a href="#statusRegisterSetDesc" title="General SCPI Status register description">SCPI register set</a> section to get closer infos about the SCPI register system in general.</div>
        <div class="example">&gt;&gt; STAT:QUES:PTR?<br />&lt;&lt; 65280</div>
        <div class="info">The default value after reboot or <a class="linkSCPI" title="Clears all SCPI event status registers" href="#CLS">CLS</a> is 65535 (all bits 1). So all condition changes from bit 0 to 1 update the event register.</div>
      </div>
    </div>
    <hr>
    <p id="SYST"><a href="#SYST:ERR?" data-subsystem="SYST" class="unfoldSection">Unfold all SYST Commands</a></p>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="SYST">
        <span class="scpi" id="SYST:ERR?">SYST:ERR?</span><span class="briefScpi">Reads and removes oldest element of SCPI error queue</span>
      </div>
      <div class="content">
        <div class="scpiLong">SYSTem:ERRor[:NEXT]?</div>
        <div class="resultDocu">Response<ul>
          <li><span class="param">error</span><span class="paramType">int</span> <span class="paramDesc">SPCI error code. If no error occurred result will be 0.</span></li>
          <li><span class="param">errDesc</span><span class="paramType">string</span> <span class="paramDesc">error code English description text.</span></li>
        </ul></div>

        <div class="details">Call to test if there where errors during command execution or asynchronous device errors. The device internally uses a error queue to store SCPI errors. This command returns and removes the oldest element of the queue. Call multiple times until queue is empty and function returns 0, "No error". The internal queue length is limited. So if you do not keep the queue empty you might loose errors when queue is full.</div>
        <div class="example">&gt;&gt; SYST:ERR?<br />&lt;&lt; -113,"Undefined header"</div>

      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="SYST">
        <span class="scpi" id="SYST:ERR:COUN?">SYST:ERR:COUN?</span><span class="briefScpi">Tests how many errors are stored in SCPI error queue</span>
      </div>
      <div class="content">
        <div class="scpiLong">SYSTem:ERRor:COUNt?</div>
        <div class="resultDocu">Response<ul>
          <li><span class="param">errDesc</span><span class="paramType">uint</span> <span class="paramDesc">Amount of errors stored in SCPI error queue. 0 when empty.</span></li>
        </ul></div>

        <div class="details">For closer details about SCPI error queue read <a class="linkSCPI" title="Reads and removes oldest element of SCPI error queue" href="#SYST:ERR?">SYST:ERR?</a>.</div>
        <div class="example">&gt;&gt; SYST:ERR:COUN?<br />&lt;&lt; 1</div>

      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="SYST">
        <span class="scpi" id="SYST:VERS?">SYST:VERS?</span><span class="briefScpi">Returns SCPI version string</span>
      </div>
      <div class="content">
        <div class="scpiLong">SYSTem:VERSion?</div>
        <div class="resultDocu">Response<ul>
          <li><span class="param">vers</span><span class="paramType">string</span> <span class="paramDesc">SCPI version string without quotes. See details for format.</span></li>
        </ul></div>

        <div class="details">Response format YYYY.V. YYYY is year of SCPI standard. V is the revision of standard.</div>
        <div class="example">&gt;&gt; SYST:VER?<br />&lt;&lt; 1999.0</div>

      </div>
    </div>


  </div>
  <script>
    //Add event handler to all SCPI command descriptions to hide or show all commands
    var coll = document.getElementsByClassName("collapsible");
    for (let i = 0; i < coll.length; i++) {
      coll[i].addEventListener("click", (e) => {
        let event = e.target || e.currentTarget;
        //Navigate DOM to collapsible div
        let content;
        if(event.nodeName == "SPAN") {
          content = event.parentElement.nextElementSibling;
        } else {
          content = event.nextElementSibling;
        }
          
        //Toggle fold/unfold by maxHeight style attribute
        if (content.style.maxHeight){
          content.style.maxHeight = null;
        } else {
          content.style.maxHeight = content.scrollHeight + "px";
        } 
      });
    }

    //Add event handler to all SCPI cross reference links to ensure link destination is visible
    var scpiLinks = document.getElementsByClassName("linkSCPI");
    for(let i = 0; i < scpiLinks.length; i++) {
      scpiLinks[i].addEventListener("click", (e) => {
        let event = e.target || e.currentTarget;
        if(event.hash) {
          //Get element of id stored in anchor element
          let destSpan = document.getElementById(event.hash.substring(1));
          if(destSpan) {
            //Navigate DOM to collapsible div
            let destEl = destSpan.parentElement.nextElementSibling;
            //Ensure to show div if not unfolded already
            if(destEl && !destEl.style.maxHeight) {
              destEl.style.maxHeight = destEl.scrollHeight + "px";
            }
          }
        }
      });
    }
    
    //Add event handlers to show and hide tooltips of SCPI commands
    var tooltips = document.getElementsByClassName("tooltip");    
    for (let i = 0; i < tooltips.length; i++) 
    {
      tooltips[i].addEventListener("mouseenter", (e) => {
        let event = e.target || e.currentTarget;
        document.getElementById(event.getAttribute('data-id')).classList.add("highlightParam");
      });
      tooltips[i].addEventListener("mouseleave", (e) => {
        let event = e.target || e.currentTarget;
        document.getElementById(event.getAttribute('data-id')).classList.remove("highlightParam");
      });
    }

    //Add event handler for SCPI subsystem fold or unfold operation 
    var unfolder = document.getElementsByClassName("unfoldSection");
    for (let i = 0; i < unfolder.length; i++) {
      unfolder[i].addEventListener("click", (e) => {
        let event = e.target || e.currentTarget;
        
        //Should we hide or show all entries?
        let isFold = event.text.startsWith('Unfold')
        //Get Subsystem for this event handler
        let subsystem = event.getAttribute('data-subsystem')
    
        //List of all SCPI command containers for given SCPI subsystem
        var affected = document.querySelectorAll('div[data-subsystem="'+subsystem+'"]');
    
        for(let j = 0; j < affected.length; j++) {
          //Navigate DOM to collapsible div
          let content = affected[j].nextElementSibling;
          //Test if we should hide or show all entries.
          if(isFold) {
            content.style.maxHeight = null;
          }
          else {
            content.style.maxHeight = content.scrollHeight + "px";
          }
    
          affected[j].click()
        }
    
        //Replace SCPI subsystem unfold/fold link text label
        if(isFold)
          event.text = event.text.replace("Unfold", "Fold");
        else
          event.text = event.text.replace("Fold", "Unfold");
      });
    }

    //Add event handler for global SCPI fold or unfold operation 
    document.getElementById("foldAll").addEventListener("click", (e) => {
      let event = e.target || e.currentTarget;
      //Should we hide or show all entries?
      let isFold = event.text.startsWith('Unfold');
        
      //List of all SCPI command container
      var affected = document.querySelectorAll('div[data-subsystem]');
        
      for(let j = 0; j < affected.length; j++) {
        //Navigate DOM to collapsible div
        let content = affected[j].nextElementSibling;
        //Test if we should hide or show all entries.
        if(isFold) {
          content.style.maxHeight = null;
        }
        else {
          content.style.maxHeight = content.scrollHeight + "px";
        }

        affected[j].click()
      }
        
      //Replace global unfold/fold link text label
      if(isFold)
        event.text = event.text.replace("Unfold", "Fold");
      else
        event.text = event.text.replace("Fold", "Unfold");
      
      //Replace all SCPI subsystem fold/unfold text labes
      otherFolders = document.querySelectorAll('a[data-subsystem]');
      for(let j = 0; j < otherFolders.length; j++) {
        if(isFold)
          otherFolders[j].text = otherFolders[j].text.replace("Unfold", "Fold");
        else
          otherFolders[j].text = otherFolders[j].text.replace("Fold", "Unfold");
      }
    });
    </script>
  </body>
</html>
