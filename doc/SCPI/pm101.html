<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <style>
body,html { 
  margin: 0;
  font-family: Arial, Helvetica, sans-serif;
  font-size: 14px;
  box-sizing: border-box;
}

dev-name, scpi-list {
    display: none;
}

p {
  margin-left: 5px;
}

.codeSample {
  margin-left: 5px;
  font-family: Consolas,"courier new";
}

img {
  margin-left: 5px;
}

@media print {
   .pageBody {
    overflow-y: auto !important;
    height: auto !important;
  }

  .content {
    overflow: auto !important;
    max-height: initial !important;
    transition: none !important;
  }

  .noSplit {
    break-inside: avoid;
  }
  
  .header-right {
    display: none !important;
  }
 
  .content .scpiLong {
    font-size: 12px !important;
  }
  
  .collapsible {
    font-weight: bold !important;
    color-adjust: exact!important;  
    -webkit-print-color-adjust: exact!important; 
    print-color-adjust: exact!important;
  }
  
  rect {
    color-adjust: exact!important;  
    -webkit-print-color-adjust: exact!important; 
    print-color-adjust: exact!important;
  }
  
  svg {
    width: 95% !important;
  }
}

a[href^="http"]:after {
     content: " " url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAoAAAAKCAYAAACNMs+9AAAAVklEQVR4Xn3PgQkAMQhDUXfqTu7kTtkpd5RA8AInfArtQ2iRXFWT2QedAfttj2FsPIOE1eCOlEuoWWjgzYaB/IkeGOrxXhqB+uA9Bfcm0lAZuh+YIeAD+cAqSz4kCMUAAAAASUVORK5CYII=);
}

svg {
  margin-left: 5px;
}

.verInfo{
  float:right;
  text-align: right;
  width: 300px;
  margin-right: 5px;
}

.header {
  overflow: hidden;
  background-color: #f1f1f1;
  padding: 5px 5px;
  left: 0;
  top: 0;
}

.pageBody {
  overflow-y: scroll; 
  overflow-x: hidden; 
  height: calc(100vh - 50px);
}

.header a {
  float: left;
  color: black;
  text-align: center;
  padding: 8px;
  text-decoration: none;
  font-size: 18px; 
  line-height: 22px;
  border-radius: 4px;
}

.header span {
  padding: 0px;
  font-size: 25px;
  font-weight: bold;
}

.header a:hover {
  background-color: #ddd;
  color: black;
}

.header-right {
  float: right;
}

.tooltip {
  display: inline-block;
  border-bottom: 1px dotted black;
  cursor: help;
}

.collapsible {
  background-color: #777;
  background-image: linear-gradient(#777, #555);
  color: white;
  cursor: pointer;
  padding: 5px;
  margin: 0px;
  margin-top: 2px;
  width: 100%;
  border: none;
  text-align: left;
  outline: none;
  font-size: 12px;
}

.collapsible .scpi {
  float:left;
  font-size: 14px;
  width: 280px;
}

.collapsible .briefScpi {
  margin-left: 10px;
  font-size: 14px;
}

.active, .collapsible:hover {
  background-color: #555;
  background-image: linear-gradient(#555, #777);
}

.content {
  padding: 0;
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.2s ease-out;
  background-color: #f1f1f1;
  z-index: 0;
}

.content ul { 
  position: relative;
  list-style: none;
  padding: 0px;
  margin: 0px;
  margin-top: 5px;
  margin-bottom: 10px;
  font-weight: normal;
  font-size: 14px;
}

.content li {
  margin: 0;
  margin-top: 4px;
  margin-bottom: 4px;
}

.content .scpiLong {
  width: 99%;
  margin: 10px 5px 10px 5px;
  font-size: 18px;
}

.details{
  padding: 7px 5px;
  margin: 4px;
  font-size: 14px;
}

.detailListing {
  margin:  5px;
}

.detailListing ul li{
  list-style-type: circle;
  margin-left: 20px;
}

span.param , span.optParam {
  float: left;
  width: 100px;
}

span.optParam {
  font-style: italic;
}

span.paramType {
  float: left;
  font-style: italic;
  width: 140px;
}

li.highlightParam {
  font-weight: bold;
}

.info {
  background-color: #c7ffd6;
  border-left: 6px solid #389c53;
  padding: 7px 5px;
  margin: 4px;
  background-image: linear-gradient(to right, #c7ffd6, #f1f1f1);
  font-size: 14px;
}

.note {
  background-color: #ffdddd;
  border-left: 6px solid #DE1616;
  padding: 7px 5px;
  margin: 4px;
  background-image: linear-gradient(to right, #ffdddd, #f1f1f1);
  font-size: 14px;
}

.example {
  background-color: #E0E6F8;
  border-left: 6px solid #075C91;
  padding: 7px 5px;
  margin: 4px;
  font-family: monospace;
  background-image: linear-gradient(to right, #E0E6F8, #f1f1f1);
  font-size: 14px;
}

.sufixDocu, .paramDocu, .resultDocu {
  padding-left: 7px;
  font-size: 10px;
  font-weight: bold;
}

.resultDocu{
  padding-left: 7px;
  font-size: 10px;
  font-weight: bold;
}

span.scpiCmd {
  font-family: monospace;
}

div.scpiSpacer {
   display: inline-block;
   width: 180px;
  font-weight: bold;
}
    </style>
    <title>PM101 Powermeter SCPI Commands</title>
    <link rel="icon" type="image/png" sizes="16x16" href="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAIAAAACACAYAAADDPmHLAAABcklEQVR4nO3cMQ7DIBAAwSPK/79MnkBlEWtnWiiQtbrCxa2Z2UPW5/YDuEsAcQKIE0CcAOIEECeAuO/pgp8E77YO5yZAnADiBBAngDgBxAkgTgBxAogTQJwA4gQQJ4A4AcQJIE4AcQKIE0CcAOIEECeAOAHECSBOAHECiBNAnADiBBAngDgBxAkgTgBxAogTQJwA4gQQJ4A4AcQJIE4Accc9gbP3adXcs9Z696rCP/9+JkCcAOIEECeAOAHECSBOAHECiBNAnADiBBAngDgBxAkgTgBxAogTQJwA4gQQJ4A4AcQJIE4AcQKIE0CcAOIEECeAOAHECSBOAHECiBNAnADiBBAngDgBxAkgTgBxAogTQJwA4gQQJ4A4AcQJIE4AcQKIE0CcAOIEECeAOAHECSBOAHECiBNAnADiBBAngDgBxAkgTgBxAogTQJwA4gQQJ4A4AcQJIE4AcWtm9u1HcI8JECeAOAHECSBOAHECiBNA3A/BiAoBsMLaFQAAAABJRU5ErkJggg==" />
    <dev-name>PM101</dev-name>
    <scpi-list>
		<scpi-cmd cmd-str="*CLS">Clears all SCPI event status registers</scpi-cmd>
		<scpi-cmd cmd-str="*ESE <bitmask>">Programs the 8 bit wide Standard Event Enable Register</scpi-cmd>
		<scpi-cmd cmd-str="*ESE?">Reads the 8 bit wide Standard Event Enable Register</scpi-cmd>
		<scpi-cmd cmd-str="*ESR?">Reads and clears the Standard Event Register</scpi-cmd>
		<scpi-cmd cmd-str="*IDN?">Reads the device identification string</scpi-cmd>
		<scpi-cmd cmd-str="*OPC">Sets the Operation Complete bit in the Standard Event Register</scpi-cmd>
		<scpi-cmd cmd-str="*OPC?">Operation complete query</scpi-cmd>
		<scpi-cmd cmd-str="*RST">Resets all SCPI and sensor parameters to default</scpi-cmd>
		<scpi-cmd cmd-str="*SRE <bitmask>">Service request enable command</scpi-cmd>
		<scpi-cmd cmd-str="*SRE?">Service request enable query</scpi-cmd>
		<scpi-cmd cmd-str="*STB?">Status byte query</scpi-cmd>
		<scpi-cmd cmd-str="*WAI">Wait-to-continue command</scpi-cmd>
		<scpi-cmd cmd-str="CALibration:STRing?">Queries factory calibration string</scpi-cmd>
		<scpi-cmd cmd-str="INPut:ADAPter[:TYPE] <type>">Sets the default adapter sensor type</scpi-cmd>
		<scpi-cmd cmd-str="INPut:ADAPter[:TYPE]?">Queries the default adapter sensor type</scpi-cmd>
		<scpi-cmd cmd-str="INPut[:PDIode]:FILTer[:LPASs][:STATe] <enable>">Enables/Disables bandwidth limitation</scpi-cmd>
		<scpi-cmd cmd-str="INPut[:PDIode]:FILTer[:LPASs][:STATe]?">Queries the default adapter sensor type</scpi-cmd>
		<scpi-cmd cmd-str="INPut:THERmopile:ACCelerator[:STATe]?">Tests if accelerator algorithm is currently operating</scpi-cmd>
		<scpi-cmd cmd-str="INPut:THERmopile:ACCelerator:AUTO <enable>">Enables/Disables thermopile prediction</scpi-cmd>
		<scpi-cmd cmd-str="INPut:THERmopile:ACCelerator:AUTO?">Queries if thermopile prediction is enabled/disabled</scpi-cmd>
		<scpi-cmd cmd-str="INPut:THERmopile:ACCelerator:TAU <tau>">Sets thermopile sensor adapter specific time constant Tau</scpi-cmd>
		<scpi-cmd cmd-str="INPut:THERmopile:ACCelerator:TAU? <special>">Queries thermopile sensor specific time constant Tau</scpi-cmd>
		<scpi-cmd cmd-str="ABORt">Aborts any previously started measurement</scpi-cmd>
		<scpi-cmd cmd-str="CONFigure[:SCALar][:POWer]">Configures SCPI measure system for power measurement</scpi-cmd>
		<scpi-cmd cmd-str="CONFigure?">Returns the actual configured unit for measurement system</scpi-cmd>
		<scpi-cmd cmd-str="CONFigure:ARRay:[POWer] <samples>,<delta_t>">Configures power array measurement</scpi-cmd>
		<scpi-cmd cmd-str="CONFigure:ARRay:CURRent <samples>,<delta_t>">Configures current array measurement</scpi-cmd>
		<scpi-cmd cmd-str="CONFigure:ARRay:PDENsity <samples>,<delta_t>">Configures power density array measurement</scpi-cmd>
		<scpi-cmd cmd-str="CONFigure[:SCALar]:FREQuency">Configures SCPI measure system for energy frequency measurement</scpi-cmd>
		<scpi-cmd cmd-str="CONFigure[:SCALar]:PDENsity">Configures SCPI measure system for power density measurement</scpi-cmd>
		<scpi-cmd cmd-str="CONFigure[:SCALar]:RESistance">Configures SCPI measure system for head resistance measurement</scpi-cmd>
		<scpi-cmd cmd-str="CONFigure[:SCALar]:TEMPerature">Configures SCPI measurement system for head temperature measurement</scpi-cmd>
		<scpi-cmd cmd-str="CONFigure[:SCALar]:VOLTage[:DC]">Configures SCPI measure system for voltage measurement</scpi-cmd>
		<scpi-cmd cmd-str="CONFigure#[:SCALar]:CURRent[:DC]">Configures SCPI measure system for current measurement</scpi-cmd>
		<scpi-cmd cmd-str="FETCh? <offset>">Blocks until previously initiated measurement is complete and returns result finally</scpi-cmd>
		<scpi-cmd cmd-str="FETCh:ARRay? <offset>, <length>">Returns binary data block of array measure buffer.</scpi-cmd>
		<scpi-cmd cmd-str="FETCh#:STATe?">Tests if there is measurement data to fetch</scpi-cmd>
		<scpi-cmd cmd-str="INITiate[:IMMediate]">Starts a new single measurement for a previously configured unit</scpi-cmd>
		<scpi-cmd cmd-str="INITiate:CONTinuous">Starts a new continuos measurement for a previously configured unit</scpi-cmd>
		<scpi-cmd cmd-str="MEASure[:SCALar][:POWer]?">Measures the sensor averaged power in Watt or dBm</scpi-cmd>
		<scpi-cmd cmd-str="MEASure:ARRay[:POWer]? <samples>,<delta_t>">Measures a sequence of power values in array mode</scpi-cmd>
		<scpi-cmd cmd-str="MEASure:ARRay:CURRent? <samples>,<delta_t>">Measures a sequence of current values in array mode</scpi-cmd>
		<scpi-cmd cmd-str="MEASure:ARRay:PDENsity? <samples>,<delta_t>">Measures a sequence of power density values in array mode</scpi-cmd>
		<scpi-cmd cmd-str="MEASure[:SCALar]:CURRent[:DC]?">Measures the sensor averaged current</scpi-cmd>
		<scpi-cmd cmd-str="MEASure[:SCALar]:FREQuency?">Measures the modulated sensor signal frequency in Hz</scpi-cmd>
		<scpi-cmd cmd-str="MEASure[:SCALar]:RESistance?">Measures sensor head NTC resistance</scpi-cmd>
		<scpi-cmd cmd-str="MEASure[:SCALar]:TEMPerature?">Measures sensor head temperature in °C</scpi-cmd>
		<scpi-cmd cmd-str="MEASure[:SCALar]:VOLTage[:DC]?">Measures the sensor averaged voltage</scpi-cmd>
		<scpi-cmd cmd-str="MEASure#[:SCALar]:PDENsity?">Measures the sensor averaged power density</scpi-cmd>
		<scpi-cmd cmd-str="READ?">Starts and queries measurement result for a previously configured unit</scpi-cmd>
		<scpi-cmd cmd-str="SENSe#:AVERage[:COUNt] <pres>">Sets the averaging for slow measurements</scpi-cmd>
		<scpi-cmd cmd-str="SENSe#:AVERage[:COUNt]?">Gets the averaging for slow measurements</scpi-cmd>
		<scpi-cmd cmd-str="SENSe#:CORRection[:LOSS[:INPut[:MAGNitude]]] <attenuation>">Sets the light attenuation</scpi-cmd>
		<scpi-cmd cmd-str="SENSe#:CORRection[:LOSS[:INPut[:MAGNitude]]]? <special>">Gets the light attenuation</scpi-cmd>
		<scpi-cmd cmd-str="SENSe#:CORRection:BEAMdiameter <diam>">Sets the beam diameter</scpi-cmd>
		<scpi-cmd cmd-str="SENSe#:CORRection:BEAMdiameter? <special>">Gets the beam diameter</scpi-cmd>
		<scpi-cmd cmd-str="SENSe#:CORRection:COEFficient:BETA <beta>">Sets external thermistor equation Beta parameter</scpi-cmd>
		<scpi-cmd cmd-str="SENSe#:CORRection:COEFficient:BETA?  <special>">Sets external thermistor equation Beta parameter</scpi-cmd>
		<scpi-cmd cmd-str="SENSe#:CORRection:COEFficient:RESistance <resist>">Sets external thermistor equation R0 parameter</scpi-cmd>
		<scpi-cmd cmd-str="SENSe#:CORRection:COEFficient:RESistance? <special>">Gets external thermistor equation R0 parameter</scpi-cmd>
		<scpi-cmd cmd-str="SENSe#:CORRection:COLLect:ZERO[:INITiate]">Starts a zeroing of the sensor</scpi-cmd>
		<scpi-cmd cmd-str="SENSe#:CORRection:COLLect:ZERO:ABORt">Aborts previously started zeroing procedure</scpi-cmd>
		<scpi-cmd cmd-str="SENSe#:CORRection:COLLect:ZERO:MAGNitude <zero>">Sets zero correction to a given value</scpi-cmd>
		<scpi-cmd cmd-str="SENSe#:CORRection:COLLect:ZERO:MAGNitude? <special>">Queries the zero correction value</scpi-cmd>
		<scpi-cmd cmd-str="SENSe#:CORRection:COLLect:ZERO:STATe?">Tests if zeroing procedure is running</scpi-cmd>
		<scpi-cmd cmd-str="SENSe#:CORRection:CSET:POINts <wavelength1>,<factor1>,<wavelength2>,<factor2>,<wavelength3>,<factor3>">Sets customer calibration set point tuples</scpi-cmd>
		<scpi-cmd cmd-str="SENSe#:CORRection:CSET:PREamble <serial>,<calDate>,<author>,<filterPos>">Starts updating customer calibration by writing slot meta information</scpi-cmd>
		<scpi-cmd cmd-str="SENSe#:CORRection:CSET#:DEFine">Stores previously configured customer calibration in persistent memory</scpi-cmd>
		<scpi-cmd cmd-str="SENSe#:CORRection:CSET#:POINts?">Returns customer calibration set point tuples</scpi-cmd>
		<scpi-cmd cmd-str="SENSe#:CORRection:CSET#:PREamble?">Returns customer calibration meta information of given slot</scpi-cmd>
		<scpi-cmd cmd-str="SENSe#:CORRection:CSET#:STATe <state>">Enables/Disables customer calibration</scpi-cmd>
		<scpi-cmd cmd-str="SENSe#:CORRection:CSET#:STATe?">Tests if customer calibration is enabled/disabled</scpi-cmd>
		<scpi-cmd cmd-str="SENSe#:CORRection:POWer[:PDIode][:RESPonse] <resp>">Sets photodiode sensor adapter responsivity</scpi-cmd>
		<scpi-cmd cmd-str="SENSe#:CORRection:POWer[:PDIode][:RESPonse]? <special>">Gets photodiode sensor responsivity</scpi-cmd>
		<scpi-cmd cmd-str="SENSe#:CORRection:POWer:THERmopile[:RESPonse] <resp>">Sets thermopile sensor adapter responsivity</scpi-cmd>
		<scpi-cmd cmd-str="SENSe#:CORRection:POWer:THERmopile[:RESPonse]? <special>">Gets thermopile sensor responsivity</scpi-cmd>
		<scpi-cmd cmd-str="SENSe#:CORRection:WAVelength <wavelength>">Sets the wavelength</scpi-cmd>
		<scpi-cmd cmd-str="SENSe#:CORRection:WAVelength? <special>">Gets the wavelength</scpi-cmd>
		<scpi-cmd cmd-str="SENSe#:CURRent[:DC]:RANGe[:UPPer] <current>">Sets a measurement range for given current</scpi-cmd>
		<scpi-cmd cmd-str="SENSe#:CURRent[:DC]:RANGe[:UPPer]? <current>">Returns measurement range for current measurement</scpi-cmd>
		<scpi-cmd cmd-str="SENSe#:CURRent[:DC]:RANGe:AUTO <state>">Sets current measurement auto ranging</scpi-cmd>
		<scpi-cmd cmd-str="SENSe#:CURRent[:DC]:RANGe:AUTO?">Tests if current auto ranging is enabled</scpi-cmd>
		<scpi-cmd cmd-str="SENSe#:CURRent[:DC]:REFerence <refCurr>">Sets relative current measurement offset</scpi-cmd>
		<scpi-cmd cmd-str="SENSe#:CURRent[:DC]:REFerence? <special>">Gets relative current measurement offset</scpi-cmd>
		<scpi-cmd cmd-str="SENSe#:CURRent[:DC]:REFerence:STATe <enable>">Enables current delta mode</scpi-cmd>
		<scpi-cmd cmd-str="SENSe#:CURRent[:DC]:REFerence:STATe?">Tests if current delta mode is enabled</scpi-cmd>
		<scpi-cmd cmd-str="SENSe#:POWer[:DC]:RANGe[:UPPer] <power>">Sets a measurement range for given power</scpi-cmd>
		<scpi-cmd cmd-str="SENSe#:POWer[:DC]:RANGe[:UPPer]? <power>">Returns measurement range for power measurement</scpi-cmd>
		<scpi-cmd cmd-str="SENSe#:POWer[:DC]:RANGe:AUTO <state>">Sets power measurement auto ranging</scpi-cmd>
		<scpi-cmd cmd-str="SENSe#:POWer[:DC]:RANGe:AUTO?">Tests if power auto ranging is enabled</scpi-cmd>
		<scpi-cmd cmd-str="SENSe#:POWer[:DC]:REFerence <refPow>">Sets relative power measurement offset</scpi-cmd>
		<scpi-cmd cmd-str="SENSe#:POWer[:DC]:REFerence? <special>">Gets relative power measurement offset</scpi-cmd>
		<scpi-cmd cmd-str="SENSe#:POWer[:DC]:REFerence:STATe <enable>">Enables power delta mode</scpi-cmd>
		<scpi-cmd cmd-str="SENSe#:POWer[:DC]:REFerence:STATe?">Tests if power delta mode is enabled</scpi-cmd>
		<scpi-cmd cmd-str="SENSe#:POWer[:DC]:UNIT <unit>">Selects the power unit</scpi-cmd>
		<scpi-cmd cmd-str="SENSe#:POWer[:DC]:UNIT?">Returns the power unit</scpi-cmd>
		<scpi-cmd cmd-str="SENSe#:REInit">Reconnect connected sensor</scpi-cmd>
		<scpi-cmd cmd-str="SENSe#:RESistance:DATA?">Returns the resistance of external NTC</scpi-cmd>
		<scpi-cmd cmd-str="SENSe#:SERial:JABber <state>">Enables/Disables serial jabber mode</scpi-cmd>
		<scpi-cmd cmd-str="SENSe#:TEMPerature:DATA?">Returns the temperature of external NTC sensor</scpi-cmd>
		<scpi-cmd cmd-str="SENSe#:VOLTage[:DC]:RANGe[:UPPer] <voltage>">Sets a measurement range for given voltage</scpi-cmd>
		<scpi-cmd cmd-str="SENSe#:VOLTage[:DC]:RANGe[:UPPer]? <voltage>">Returns measurement range for voltage measurement</scpi-cmd>
		<scpi-cmd cmd-str="SENSe#:VOLTage[:DC]:RANGe:AUTO <state>">Sets voltage measurement auto ranging</scpi-cmd>
		<scpi-cmd cmd-str="SENSe#:VOLTage[:DC]:RANGe:AUTO?">Tests if voltage auto ranging is enabled</scpi-cmd>
		<scpi-cmd cmd-str="SENSe#:VOLTage[:DC]:REFerence <refVolt>">Sets relative voltage measurement offset</scpi-cmd>
		<scpi-cmd cmd-str="SENSe#:VOLTage[:DC]:REFerence? <special>">Gets relative voltage measurement offset</scpi-cmd>
		<scpi-cmd cmd-str="SENSe#:VOLTage:REFerence:STATe <enable>">Enables voltage delta mode</scpi-cmd>
		<scpi-cmd cmd-str="SENSe#:VOLTage:REFerence:STATe?">Tests if voltage delta mode is enabled</scpi-cmd>
		<scpi-cmd cmd-str="SOURce:DIGital:DATA <bitmask>">Sets GPIO output pin level to high or low</scpi-cmd>
		<scpi-cmd cmd-str="SOURce:DIGital:DATA?">Reads back GPIO input/output levels</scpi-cmd>
		<scpi-cmd cmd-str="SOURce:DIGital:ENABle <bitmask>">Configures all GPIO pin as input our output simultaneously</scpi-cmd>
		<scpi-cmd cmd-str="SOURce:DIGital:ENABle?">Gets GPIO input/output pin direction</scpi-cmd>
		<scpi-cmd cmd-str="SOURce#:VOLTage[:LEVel][:IMMediate][:AMPLitude]? <special>">Queries analogue output voltage</scpi-cmd>
		<scpi-cmd cmd-str="SOURce#:VOLTage:CORRection:SLOPe[:OUTPut][:MAGNitude] <slope>">Sets the analogue output gain</scpi-cmd>
		<scpi-cmd cmd-str="SOURce#:VOLTage:CORRection:SLOPe[:OUTPut][:MAGNitude]? <special>">Queries the analogue output gain level</scpi-cmd>
		<scpi-cmd cmd-str="STATus:AUXiliary[:EVENt]?">Queries the SCPI Auxiliary Event register</scpi-cmd>
		<scpi-cmd cmd-str="STATus:AUXiliary:CONDition?">Queries the SCPI Auxiliary Condition register</scpi-cmd>
		<scpi-cmd cmd-str="STATus:AUXiliary:ENABle <bitmask>">Sets the SCPI Auxiliary Enable register</scpi-cmd>
		<scpi-cmd cmd-str="STATus:AUXiliary:ENABle?">Queries the SCPI Auxiliary Enable register</scpi-cmd>
		<scpi-cmd cmd-str="STATus:AUXiliary:NTRansition <bitmask>">Sets the SCPI Auxiliary Negative Transition register</scpi-cmd>
		<scpi-cmd cmd-str="STATus:AUXiliary:NTRansition?">Queries the SCPI Auxiliary Negative Transition register</scpi-cmd>
		<scpi-cmd cmd-str="STATus:AUXiliary:PTRansition <bitmask>">Sets the SCPI Auxiliary Positive Transition register</scpi-cmd>
		<scpi-cmd cmd-str="STATus:AUXiliary:PTRansition?">Queries the SCPI Auxiliary Positive Transition register</scpi-cmd>
		<scpi-cmd cmd-str="STATus:OPERation[:EVENt]?">Queries the SCPI Operation Event register</scpi-cmd>
		<scpi-cmd cmd-str="STATus:OPERation:CONDition?">Queries the SCPI Operation Condition register</scpi-cmd>
		<scpi-cmd cmd-str="STATus:OPERation:ENABle <bitmask>">Sets the SCPI Operation Enable register</scpi-cmd>
		<scpi-cmd cmd-str="STATus:OPERation:ENABle?">Queries the SCPI Operation Enable register</scpi-cmd>
		<scpi-cmd cmd-str="STATus:OPERation:NTRansition <bitmask>">Sets the SCPI Operation Negative Transition register</scpi-cmd>
		<scpi-cmd cmd-str="STATus:OPERation:NTRansition?">Queries the SCPI Operation Negative Transition register</scpi-cmd>
		<scpi-cmd cmd-str="STATus:OPERation:PTRansition <bitmask>">Sets the SCPI Operation Positive Transition register</scpi-cmd>
		<scpi-cmd cmd-str="STATus:OPERation:PTRansition?">Queries the SCPI Operation Positive Transition register</scpi-cmd>
		<scpi-cmd cmd-str="STATus:PRESet">Pre-sets all SCPI status register with default values</scpi-cmd>
		<scpi-cmd cmd-str="STATus:QUEStionable[:EVENt]?">Queries the SCPI Questionable Event register</scpi-cmd>
		<scpi-cmd cmd-str="STATus:QUEStionable:CONDition?">Queries the SCPI Questionable Condition register</scpi-cmd>
		<scpi-cmd cmd-str="STATus:QUEStionable:ENABle <bitmask>">Sets the SCPI Questionable Enable register</scpi-cmd>
		<scpi-cmd cmd-str="STATus:QUEStionable:ENABle?">Queries the SCPI Questionable Enable register</scpi-cmd>
		<scpi-cmd cmd-str="STATus:QUEStionable:NTRansition <bitmask>">Sets the SCPI Questionable Negative Transition register</scpi-cmd>
		<scpi-cmd cmd-str="STATus:QUEStionable:NTRansition?">Queries the SCPI Questionable Negative Transition register</scpi-cmd>
		<scpi-cmd cmd-str="STATus:QUEStionable:PTRansition <bitmask>">Sets the SCPI Questionable Positive Transition register</scpi-cmd>
		<scpi-cmd cmd-str="STATus:QUEStionable:PTRansition?">Queries the SCPI Questionable Positive Transition register</scpi-cmd>
		<scpi-cmd cmd-str="SYSTem:ERRor[:NEXT]?">Reads and removes oldest element of SCPI error queue</scpi-cmd>
		<scpi-cmd cmd-str="SYSTem:ERRor:COUNt?">Tests how many errors are stored in SCPI error queue</scpi-cmd>
		<scpi-cmd cmd-str="SYSTem:SENSor:IDN?">Sensor head identification query</scpi-cmd>
		<scpi-cmd cmd-str="SYSTem:SERial:TRANsmit:BAUD <baudrate>">Changes baudrate of SCPI serial interface</scpi-cmd>
		<scpi-cmd cmd-str="SYSTem:SERial:TRANsmit:BAUD? <special>">Queries baudrate of SCPI serial interface</scpi-cmd>
		<scpi-cmd cmd-str="SYSTem:VERSion?">Returns SCPI version string</scpi-cmd>

    </scpi-list>
</head>
<body>
  <div class="header">
    <span>PM101 Powermeter SCPI Commands</span>
    <div class="header-right">
      <a href="#None">None</a>
      <a href="#CAL">CAL</a>
      <a href="#INP">INP</a>
      <a href="#MEAS">MEAS</a>
      <a href="#SENS">SENS</a>
      <a href="#SOUR">SOUR</a>
      <a href="#STAT">STAT</a>
      <a href="#SYST">SYST</a>

    </div>
  </div>
  <div class="pageBody">
    <div id="SCPI_Desc">
      <div class="verInfo">V 1.4.0 generated 14 Feb 2024, 10:20:59</div>
      <p>This is the description of the <a target="_blank" href="https://www.thorlabs.com/">Thorlabs</a> PM101 remote interface. This single channel industrial Power meter can be remotely controlled using the SCPI (Standard Commands for Programmable Instruments) interface.</p>
      <p>In 1975, the IEEE standardized a bus developed by Hewlett-Packard originally called HPIB (Hewlett-Packard Interface Bus), later changed to GPIB (General Purpose Interface Bus). 
        The standard was called IEEE 488 (IEEE 488.1) and it defined mechanical aspects of the bus. The later IEEE 488.2 defined its protocol properties. What was missing were set of 
        rules between the manufacturers on commands to control the instruments. Sometimes these varied even between different models from the same manufacturer.</p>
      <p>In 1990 SCPI Consortium released the first SCPI standard as an additional layer for the IEEE-488.2 standard.</p>
      <p>SCPI commands are ASCII strings, which are sent to instrument over the physical communication layer. They can perform:</p>
      <ul>
        <li> Set operations, for example the <span class="scpiCmd">*RST</span> command (resetting the instrument).</li>
        <li> Query operations, for example the <span class="scpiCmd">*IDN?</span> query (querying the instrument’s identification string).</li>
      </ul>
      <p>Some SCPI commands exist as both set commands and query commands. An example is an Powermeter's averaging command <span class="scpiCmd">SENS:AVER</span>. You can set it with the 
        SCPI command <span class="scpiCmd">SENS:AVER 2</span>, and also query its current value with the <span class="scpiCmd">SENS:AVER?</span>.</p>

      <p>The format mentioned in this manual e.g.: <span class="scpiCmd">SENSe#:AVERage[:COUNt] &lt;pres&gt;</span> is called canonical form. Here are the most important rules to remember:</p>
      <ul>
        <li>The parts within square brackets are not mandatory and can be left out.</li>
        <li>All # within the command represent a suffix positive integer number starting at 1. If there is only one suffix you may skip it because default is applied internally. 
           If there are two or more suffix it is mandatory to specify all.</li>
        <li>The capital letter parts are mandatory; the small letters can be omitted. This is called short form. An example of the above command in the short form is <span class="scpiCmd">SENS1:AVER 3</span>. 
           You can use either the short form, or the long form <span class="scpiCmd">SENSE1:AVERAGE 3</span>, but nothing in between, e.g. <span class="scpiCmd">SENS1:AVERA 3</span>.</li>
        <li>The SCPI commands are case-insensitive. You can also use the short form <span class="scpiCmd">sens1:aver 3</span> or or the long form <span class="scpiCmd">sense1:average 3</span></li>
        <li>Combine multiple commands into one string using selmicolon ';'. For example, a combined string of <span class="scpiCmd">SENS1:AVER 3</span> and <span class="scpiCmd">SENS1:CORR:WAV 850</span> is 
            <span class="scpiCmd">SENS1:AVER 3;CORR:WAV 850</span>. Notice that the second command does not have the <span class="scpiCmd">SENS1:</span> part. The reason is, that the command tree path does 
            not change within one string. If you want to reset the command tree path to the root, use the colon character at the beginning of the second command: <span class="scpiCmd">SENS1:AVER 3;:SENS2:CORR:WAV 850</span>.</li>
        <li>Create query forms by adding a question mark, mostly to the end: <span class="scpiCmd">SENS1:AVER?</span> Sometimes there is an additional parameter placed after the 
            question mark. There must be a space character between the question mark and the additional parameter. For example: <span class="scpiCmd">SENS1:CORR:WAV? MIN</span></li>
      </ul>
      
      <p>The complete SCPI standard is available here: <a target="_blank" href="https://www.ivifoundation.org/docs/scpi-99.pdf" title="SCPI Standard PDF">SCPI-99</a></p>
    </div>
    <hr/>
    <div id="measureSystemDesc">
      <h1>Measurement Systems</h1>
      <p>The PM101 supports two different general measurement modes. The following section provides a detail description of these modes. Not all modes are supported by all 
         sensors or operating modes. The Powermeter samples the discrete input signal at 10 kHz. Every millisecond the device processes the last 10 sample results. 
         The device always generates a constant measurement result stream regardless of input signal. 
      </p>
      <h2 id="slowMeasSystem">Slow Measurement System</h2>
      <p>The "Slow Measurement System" is available for all sensor types and measurement modes. It returns values up to a frequency of 1kHz. The command SENS:AVER
         can be used to scale down the update rate by formula MeasFreq = 1000/AVG. Averaging is considered for the following units only:
        <ul>
          <li><b>POW</b>: Photodiode or Thermopile: Sensor power in W or dBm (See command SENSe#:POWer[:DC]:UNIT)</li>
          <li><b>CURR</b>: Photodiode: Sensor current in Ampere</li>
          <li><b>VOLT</b>: Thermopile: Sensor voltage in Volt</li>
          <li><b>PDEN</b>: Photodiode or Thermopile: Sensor power density in W/cm²</li>
        </ul>
      </p>
      <p>All other units have their own independent update rate.</p>
      <p>The SCPI commands for this measurement system are part of the MEAS group. 
         This group contains some convenience commands. These commands internally call a predefined SCPI command sequence. 
         Starting with the well-known MEAS? convenience command: 
        <ol>
          <li><div class="scpiSpacer">ABOR</div>Aborts any ongoing measurement</li>
          <li><div class="scpiSpacer">CONF</div>Configures measurement channel for power measurement</li>
          <li><div class="scpiSpacer">INIT</div>Starts a new measurement</li>
          <li><div class="scpiSpacer">FETC?</div>Blocks until measurement is complete and returns the result</li>
        </ol>
      </p>
      <p>If the same unit should be read multiple times in a row, reconfiguring the channel again and again is not necessary. 
         Therefore, use the READ? convenience command instead:
        <ol>
          <li><div class="scpiSpacer">ABOR</div>Aborts any ongoing measurement</li>
          <li><div class="scpiSpacer">INIT</div>Starts a new measurement</li>
          <li><div class="scpiSpacer">FETC?</div>Blocks until measurement is complete and returns the result</li>
        </ol>
      </p>
      <p>The following diagram shows two examples of SCPI command sequences for the Slow Measure System. 
        There is no timing constraint between the single SCPI commands. Timeline is from left to right.
      </p>
      <svg width="1100px" viewBox="19 217 1464 526" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">  <g>    <rect style="fill: #ffffff" x="1100" y="420" width="361" height="41"/>    <rect style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x="1100" y="420" width="361" height="41"/>    <text font-size="12.8" style="fill: #000000;text-anchor:middle;font-family:sans-serif;font-style:normal;font-weight:normal" x="1280.5" y="445.3">      <tspan x="1280.5" y="445.3">Measurement n+3</tspan>    </text>  </g>  <g>    <rect style="fill: #e6e6fa" x="480" y="352" width="260" height="22"/>    <rect style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #4bafef" x="480" y="352" width="260" height="22"/>  </g>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="100" y1="360" x2="100" y2="390.264"/>    <polygon style="fill: #000000" points="100,397.764 95,387.764 100,390.264 105,387.764 "/>    <polygon style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="100,397.764 95,387.764 100,390.264 105,387.764 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="40" y="320">    <tspan x="40" y="320">ABOR</tspan>    <tspan x="40" y="336">Aborts any ongoing </tspan>    <tspan x="40" y="352">measurement</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="200" y="320">    <tspan x="200" y="320">CONF:POW (Required once)</tspan>    <tspan x="200" y="336">Configure channel for </tspan>    <tspan x="200" y="352">Power measurement</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="460" y="320">    <tspan x="460" y="320">INIT</tspan>    <tspan x="460" y="336">Init Measurement</tspan>  </text>  <g>    <rect style="fill: #ffffff" x="20" y="420" width="360" height="41"/>    <rect style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x="20" y="420" width="360" height="41"/>    <text font-size="12.8" style="fill: #000000;text-anchor:middle;font-family:sans-serif;font-style:normal;font-weight:normal" x="200" y="445.3">      <tspan x="200" y="445.3">Measurement n</tspan>    </text>  </g>  <g>    <rect style="fill: #ffffff" x="380" y="420" width="361" height="41"/>    <rect style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x="380" y="420" width="361" height="41"/>    <text font-size="12.8" style="fill: #000000;text-anchor:middle;font-family:sans-serif;font-style:normal;font-weight:normal" x="560.5" y="445.3">      <tspan x="560.5" y="445.3">Measurement n+1</tspan>    </text>  </g>  <g>    <rect style="fill: #ffffff" x="740" y="420" width="361" height="41"/>    <rect style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x="740" y="420" width="361" height="41"/>    <text font-size="12.8" style="fill: #000000;text-anchor:middle;font-family:sans-serif;font-style:normal;font-weight:normal" x="920.5" y="445.3">      <tspan x="920.5" y="445.3">Measurement n+2</tspan>    </text>  </g>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="300" y1="360" x2="300" y2="390.264"/>    <polygon style="fill: #000000" points="300,397.764 295,387.764 300,390.264 305,387.764 "/>    <polygon style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="300,397.764 295,387.764 300,390.264 305,387.764 "/>  </g>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="480" y1="360" x2="480" y2="390.264"/>    <polygon style="fill: #000000" points="480,397.764 475,387.764 480,390.264 485,387.764 "/>    <polygon style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="480,397.764 475,387.764 480,390.264 485,387.764 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="890" y="320">    <tspan x="890" y="320">FETC?</tspan>    <tspan x="890" y="336">return result immedetaly as measurement already complete.</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="920" y1="369.736" x2="920" y2="390.264"/>    <polygon style="fill: #000000" points="920,362.236 925,372.236 920,369.736 915,372.236 "/>    <polygon style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="920,362.236 925,372.236 920,369.736 915,372.236 "/>    <polygon style="fill: #000000" points="920,397.764 915,387.764 920,390.264 925,387.764 "/>    <polygon style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="920,397.764 915,387.764 920,390.264 925,387.764 "/>  </g>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="740" y1="416" x2="740" y2="385.736"/>    <polygon style="fill: #000000" points="740,378.236 745,388.236 740,385.736 735,388.236 "/>    <polygon style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="740,378.236 745,388.236 740,385.736 735,388.236 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:700" x="20" y="480">    <tspan x="20" y="480">f = 1kHz / AVG(1)</tspan>  </text>  <g>    <rect style="fill: #ffffff" x="20" y="680" width="360" height="41"/>    <rect style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x="20" y="680" width="360" height="41"/>    <text font-size="12.8" style="fill: #000000;text-anchor:middle;font-family:sans-serif;font-style:normal;font-weight:normal" x="200" y="705.3">      <tspan x="200" y="705.3">Measurement n</tspan>    </text>  </g>  <g>    <rect style="fill: #ffffff" x="380" y="680" width="361" height="41"/>    <rect style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x="380" y="680" width="361" height="41"/>    <text font-size="12.8" style="fill: #000000;text-anchor:middle;font-family:sans-serif;font-style:normal;font-weight:normal" x="560.5" y="705.3">      <tspan x="560.5" y="705.3">Measurement n+1</tspan>    </text>  </g>  <g>    <rect style="fill: #ffffff" x="740" y="680" width="361" height="41"/>    <rect style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x="740" y="680" width="361" height="41"/>    <text font-size="12.8" style="fill: #000000;text-anchor:middle;font-family:sans-serif;font-style:normal;font-weight:normal" x="920.5" y="705.3">      <tspan x="920.5" y="705.3">Measurement n+2</tspan>    </text>  </g>  <g>    <rect style="fill: #ffffff" x="1100" y="680" width="361" height="41"/>    <rect style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x="1100" y="680" width="361" height="41"/>    <text font-size="12.8" style="fill: #000000;text-anchor:middle;font-family:sans-serif;font-style:normal;font-weight:normal" x="1280.5" y="705.3">      <tspan x="1280.5" y="705.3">Measurement n+3</tspan>    </text>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:700" x="20" y="740">    <tspan x="20" y="740">f = 1kHz / AVG(2)</tspan>  </text>  <g>    <rect style="fill: #e6e6fa" x="860" y="636" width="240" height="10"/>    <rect style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x="860" y="636" width="240" height="10"/>  </g>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="100" y1="620" x2="100" y2="650.264"/>    <polygon style="fill: #000000" points="100,657.764 95,647.764 100,650.264 105,647.764 "/>    <polygon style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="100,657.764 95,647.764 100,650.264 105,647.764 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="40" y="580">    <tspan x="40" y="580">ABOR</tspan>    <tspan x="40" y="596">Aborts any ongoing </tspan>    <tspan x="40" y="612">measurement</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="440" y="580">    <tspan x="440" y="580">CONF:POW (Required once)</tspan>    <tspan x="440" y="596">Configure channel for </tspan>    <tspan x="440" y="612">Power measurement</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="660" y="580">    <tspan x="660" y="580">INIT</tspan>    <tspan x="660" y="596">Init Measurement</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="540" y1="620" x2="540" y2="650.264"/>    <polygon style="fill: #000000" points="540,657.764 535,647.764 540,650.264 545,647.764 "/>    <polygon style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="540,657.764 535,647.764 540,650.264 545,647.764 "/>  </g>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="700" y1="620" x2="700" y2="650.264"/>    <polygon style="fill: #000000" points="700,657.764 695,647.764 700,650.264 705,647.764 "/>    <polygon style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="700,657.764 695,647.764 700,650.264 705,647.764 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="840" y="580">    <tspan x="840" y="580">FETC?</tspan>    <tspan x="840" y="596">               ...blocks...                       receive result</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="20" y1="240" x2="1470.26" y2="240"/>    <polygon style="fill: #000000" points="1477.76,240 1467.76,245 1470.26,240 1467.76,235 "/>    <polygon style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1477.76,240 1467.76,245 1470.26,240 1467.76,235 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="710" y="230">    <tspan x="710" y="230">timeline</tspan>  </text>  <g>    <rect style="fill: #e6e6fa" x="700" y="616" width="400.1" height="18"/>    <rect style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #4bafef" x="700" y="616" width="400.1" height="18"/>  </g>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="1100" y1="680" x2="1100" y2="649.736"/>    <polygon style="fill: #000000" points="1100,642.236 1105,652.236 1100,649.736 1095,652.236 "/>    <polygon style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1100,642.236 1105,652.236 1100,649.736 1095,652.236 "/>  </g>  <text font-size="12.8" style="fill: #4bafef;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="624" y="368">    <tspan x="624" y="368">Measurment</tspan>  </text>  <text font-size="12.8" style="fill: #4bafef;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="872" y="628">    <tspan x="872" y="628">Measurment</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="860" y1="620" x2="860" y2="650.264"/>    <polygon style="fill: #000000" points="860,657.764 855,647.764 860,650.264 865,647.764 "/>    <polygon style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="860,657.764 855,647.764 860,650.264 865,647.764 "/>  </g></svg>
      <p>The first example shows a 1 kHz measurement scenario with an averaging of 1. INIT starts a new measurement. 
         Once the measurement is complete, the result is stored internally until FETC? is called. If FETC? is called 
         multiple times in a row the same result will be returned as no new measurement has been started. 
      </p>
      <p>The second example show a 500 Hz measurement scenario with an averaging of 2. INIT starts the new measurement. 
         Before the measurement result is ready FETC? is already called. As the measurement is still ongoing in 
         the background the FETC? blocks the entire remote interface until measurement has been taken.
      </p>
      <p>Once you send a FETC?, MEAS? or READ? command, the interface is blocked until the measurement has been taken. 
         For photodiode sensors these commands work reliable except for very high averaging. High averaging values result in a long 
         block time when waiting for the measurement to terminate. Within this block time do not send an ABOR or any other SCPI 
         command. Please do not enlarge the communication interface timeout to solve this problem but continue reading.
      </p>
      <p>Once a new measurement has been started by INIT, checking for completion can be done by the command FETC:STAT? If this command returns 1, 
        FETC? can be called with providing an instant result. If the FETC:STAT? returns 0, continue to ask for completion or abort the measurement 
        if not longer relevant by using the ABOR command. Do not use the convenience function in these cases.
      </p>
      <p>Another configuration of the Slow Measurement system is the continuous measurement. 
         For this mode there are no convenience functions. Use the following sequence for continuous mode:
        <ol>
          <li><div class="scpiSpacer">ABOR</div>Aborts any previous ongoing measurement</li>
          <li><div class="scpiSpacer">CONF:POW</div>Configure a power measurement for example</li>
          <li><div class="scpiSpacer">INIT:CONT?</div>Start continuous measurement</li>
          <li><div class="scpiSpacer">FETC?</div>Returns the last recent measurement result </li>
          <li><div class="scpiSpacer">Delay()</div>Delay between reading results</li>
          <li><div class="scpiSpacer">FETC?</div>Returns the last recent measurement result </li>
          <li><div class="scpiSpacer">Delay()</div>Delay between reading results</li>
          <li><b>...</b></li>
        </ol>
      </p>
      <p>Keep in mind in continuous configuration there is no synchronization between the command and the measurements. 
        If FETC? is called faster than the measurement update rate, the same result is read multiple times.
      </p>

      <h2 id="scopeMeasSystem">Array Mode</h2>
      <p>In "Array Mode" the PM101 stores a measurement sequence in the device memory. The mode is 
      supported for photodiode sensors only. The buffered measurements may be accessed at any speed or order 
      once the sequence has been taken. The sequence is always triggered by software. A hardware trigger is not available.
      The buffer always stores 10k samples with a fixed sample rate of 10 kHz. The buffer contains the raw samples
      without any further averaging. Data is processed and averaged during data access later. Because of this limitation
      the product of the parameters delta_t and sample needs to be less or equal 10000 in any case. To get the highest
      resolution use 10000 samples and delta_t 100. As the sample rate is 10 kHz delta_t needs to be a multiple of 100.
      Otherwiese delta_t gets coerced.</p>
      <p>Before starting a Array Mode for optical power reading ensure:
        <ul>
          <li>Set manual range (Disables auto ranging of PM101 automatically), command: SENSe#:POWer[:DC]:RANGe[:UPPer]</li>
          <li>Set full bandwidth for photodiode sensors (no analog filter), command: INPut#[:PDIode]:FILTer[:LPASs][:STATe] 0</li>
        </ul>
      </p>
      <p>The Array Mode is part of the MEAS SCPI command group. You can use the convenience commands like MEAS:ARR? or the 
         following SCPI command sequence to have more control about the measurement:
        <ol>
          <li><div class="scpiSpacer">ABOR</div>Abort any ongoing measurement</li>
          <li><div class="scpiSpacer">CONF:ARR 10000, 100</div>Configure array mode for 10000 samples with delta_t of 100 us</li>
          <li><div class="scpiSpacer">INIT</div>Starts a array measurement </li>
          <li><div class="scpiSpacer">FETC:STAT?</div>Scope buffer filled? If not keep on asking. For timeout send ABOR finally</li>
          <li><div class="scpiSpacer">FETC:STAT?</div>Scope buffer filled? If not keep on asking. For timeout send ABOR finally</li>
          <li><b>...</b></li>
          <li><div class="scpiSpacer">FETC:STAT?</div>Scope buffer filled returned true.</li>
          <li><div class="scpiSpacer">FETC? 0</div>Read scope buffer content at index 0</li>
          <li><div class="scpiSpacer">FETC? 1</div>Read scope buffer content at index 1</li>
          <li><b>...</b></li>
          <li><div class="scpiSpacer">FETC? 9999</div>Read scope buffer content at index 9999</li>
        </ol>
      </p>
      
      <p/>The buffered measurements might be accessed in a textual manor using FETC? command
      or in a binary fession using the FETC:ARR? command. If you are using the faster binary 
      FETC:ARR? command you will always receive up to 40 measurements at once. You can access 
      the array buffer in any order. The binary list response of FETC:ARR? has the following 
      litle endian byte order: 
      </p>
      <svg width="600px" viewBox="988 398 607 141" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">  <g>    <rect style="fill: #d88ff2" x="1146" y="500" width="56.05" height="38"/>    <rect style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x="1146" y="500" width="56.05" height="38"/>    <text font-size="12.8" style="fill: #000000;text-anchor:middle;font-family:sans-serif;font-style:normal;font-weight:normal" x="1174.03" y="523.8">      <tspan x="1174.03" y="523.8">0xa51</tspan>    </text>  </g>  <g>    <rect style="fill: #d88ff2" x="1200" y="500" width="52" height="38"/>    <rect style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x="1200" y="500" width="52" height="38"/>    <text font-size="12.8" style="fill: #000000;text-anchor:middle;font-family:sans-serif;font-style:normal;font-weight:normal" x="1226" y="523.8">      <tspan x="1226" y="523.8">0x4c</tspan>    </text>  </g>  <g>    <rect style="fill: #d88ff2" x="1250" y="500" width="52" height="38"/>    <rect style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x="1250" y="500" width="52" height="38"/>    <text font-size="12.8" style="fill: #000000;text-anchor:middle;font-family:sans-serif;font-style:normal;font-weight:normal" x="1276" y="523.8">      <tspan x="1276" y="523.8">0x5e</tspan>    </text>  </g>  <g>    <rect style="fill: #d88ff2" x="1302" y="500" width="52" height="38"/>    <rect style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x="1302" y="500" width="52" height="38"/>    <text font-size="12.8" style="fill: #000000;text-anchor:middle;font-family:sans-serif;font-style:normal;font-weight:normal" x="1328" y="523.8">      <tspan x="1328" y="523.8">0x39</tspan>    </text>  </g>  <g>    <rect style="fill: #d88ff2" x="1146" y="438" width="208" height="62"/>    <rect style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x="1146" y="438" width="208" height="62"/>    <text font-size="12.8" style="fill: #000000;text-anchor:middle;font-family:sans-serif;font-style:normal;font-weight:normal" x="1250" y="473.8">      <tspan x="1250" y="473.8">0x395e4c51 (0.000212)</tspan>    </text>  </g>  <g>    <rect style="fill: #d88ff2" x="1354" y="500" width="120.75" height="38"/>    <rect style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x="1354" y="500" width="120.75" height="38"/>    <text font-size="12.8" style="fill: #000000;text-anchor:middle;font-family:sans-serif;font-style:normal;font-weight:normal" x="1414.38" y="523.8">      <tspan x="1414.38" y="523.8">4 Bytes float</tspan>    </text>  </g>  <g>    <rect style="fill: #d88ff2" x="1474" y="500" width="120.75" height="38"/>    <rect style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x="1474" y="500" width="120.75" height="38"/>    <text font-size="12.8" style="fill: #000000;text-anchor:middle;font-family:sans-serif;font-style:normal;font-weight:normal" x="1534.38" y="523.8">      <tspan x="1534.38" y="523.8">4 Bytes float ch</tspan>    </text>  </g>  <g>    <rect style="fill: #75daff" x="990" y="500" width="52" height="38"/>    <rect style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x="990" y="500" width="52" height="38"/>    <text font-size="12.8" style="fill: #000000;text-anchor:middle;font-family:sans-serif;font-style:normal;font-weight:normal" x="1016" y="523.8">      <tspan x="1016" y="523.8">'4'</tspan>    </text>  </g>  <g>    <rect style="fill: #75daff" x="990" y="430" width="156.55" height="70"/>    <rect style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x="990" y="430" width="156.55" height="70"/>    <text font-size="12.8" style="fill: #000000;text-anchor:middle;font-family:sans-serif;font-style:normal;font-weight:normal" x="1068.28" y="453.8">      <tspan x="1068.28" y="453.8">Length as String "40" </tspan>      <tspan x="1068.28" y="469.8">followed by comma</tspan>      <tspan x="1068.28" y="485.8"> if length &gt; 0</tspan>    </text>  </g>  <g>    <rect style="fill: #75daff" x="1042" y="500" width="52" height="38"/>    <rect style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x="1042" y="500" width="52" height="38"/>    <text font-size="12.8" style="fill: #000000;text-anchor:middle;font-family:sans-serif;font-style:normal;font-weight:normal" x="1068" y="523.8">      <tspan x="1068" y="523.8">'0'</tspan>    </text>  </g>  <g>    <rect style="fill: #75daff" x="1094" y="500" width="52" height="38"/>    <rect style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x="1094" y="500" width="52" height="38"/>    <text font-size="12.8" style="fill: #000000;text-anchor:middle;font-family:sans-serif;font-style:normal;font-weight:normal" x="1120" y="523.8">      <tspan x="1120" y="523.8">','</tspan>    </text>  </g>  <g>    <rect style="fill: #75daff" x="990" y="400" width="156.55" height="38"/>    <rect style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x="990" y="400" width="156.55" height="38"/>    <text font-size="12.8" style="fill: #000000;text-anchor:middle;font-family:sans-serif;font-style:normal;font-weight:normal" x="1068.28" y="423.8">      <tspan x="1068.28" y="423.8">Response count</tspan>    </text>  </g></svg>
      <p>The following code gives an example to parse the binary data in Python:</p>
      <div class="codeSample">
        inst.write('FETC:ARR? 120, 40')<br />
        length = 0<br />
        lenStr = ""<br />
        
        while length == 0:<br />
        &nbsp;&nbsp;byte = inst.read_bytes(1)<br />
        &nbsp;&nbsp;if byte == b'0':<br />
        &nbsp;&nbsp;&nbsp;&nbsp;return res<br />
        &nbsp;&nbsp;if byte == b',':<br />
        &nbsp;&nbsp;&nbsp;&nbsp;length = int(lenStr)<br />
        &nbsp;&nbsp;else:<br />
        &nbsp;&nbsp;&nbsp;&nbsp;lenStr = lenStr + chr(byte[0])<br />
        
        vals = inst.read_bytes(4 * length)<br />
        
        res = []<br />
        for i in range (0, 4 * length, 4):<br />
        &nbsp;&nbsp;value = struct.unpack('&ltf',  bytearray(vals[i + 0 : i + 4]))[0]<br />
        &nbsp;&nbsp;res.append(value)<br />
        
      </div>

    <div id="statusRegisterSetDesc">
    <h1>SCPI Registers</h1>
      <p>The SCPI system defines a status reporting system based on status register sets. Every state or event is mapped to a single bit in the values of the register sets. 
      The PM101 defines the following register sets: </p>
      <ul>
        <li><b>AUX</b>ilary: Auxiliary system monitoring like shutter or fan.</li>
        <li><b>OPER</b>ation: Measurement state monitoring.</li>
        <li><b>QUEST</b>ionalbe: Measure result reliability status monitoring.</li>
        <li><b>STD</b>andard Byte: SCPI global system status byte.</li>
      </ul>

      <p>Every register set contains 5 registers.</p>
      <ul>
        <li><b>COND</b>ition: The Condition register monitors the actual state. For example if a sensor is currently connected or not.</li>
        <li><b>PTR</b>ansition: The Positive Transition register masks bit changes from 0 to 1 of the Condition register to the Event register.</li>
        <li><b>NTR</b>ansition: The Negative Transition register masks bit changes from 1 to 0 of the Condition register to the Event register.</li>
        <li><b>EVENT</b>: The Event register shows changes of the Condition register. Some conditions only last for a short moment and are otherwise hard to detect.</li>
        <li><b>ENAB</b>le: The Enable register masks if changes of the Event registers (TODO JS registers plural?) are forwarded to a higher level hierarchy Condition register.</li>
      </ul>
      <p>The following image visualizes the logical relation between the registers.</p>
      <svg width="600px" viewBox="239 259 690 160" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">  <g>    <rect style="fill: #ffffff" x="240" y="320" width="75.2" height="38"/>    <rect style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x="240" y="320" width="75.2" height="38"/>    <text font-size="12.8" style="fill: #000000;text-anchor:middle;font-family:sans-serif;font-style:normal;font-weight:normal" x="277.6" y="343.8">      <tspan x="277.6" y="343.8">CON</tspan>    </text>  </g>  <g>    <rect style="fill: #ffffff" x="360" y="260" width="136.65" height="38"/>    <rect style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x="360" y="260" width="136.65" height="38"/>    <text font-size="12.8" style="fill: #000000;text-anchor:middle;font-family:sans-serif;font-style:normal;font-weight:normal" x="428.325" y="283.8">      <tspan x="428.325" y="283.8">PTR: CON bit 0 -&gt; 1</tspan>    </text>  </g>  <g>    <rect style="fill: #ffffff" x="359.25" y="380" width="138.15" height="38"/>    <rect style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x="359.25" y="380" width="138.15" height="38"/>    <text font-size="12.8" style="fill: #000000;text-anchor:middle;font-family:sans-serif;font-style:normal;font-weight:normal" x="428.325" y="403.8">      <tspan x="428.325" y="403.8">NTR: CON bit 1 -&gt; 0</tspan>    </text>  </g>  <g>    <rect style="fill: #ffffff" x="540" y="320" width="75.2" height="38"/>    <rect style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x="540" y="320" width="75.2" height="38"/>    <text font-size="12.8" style="fill: #000000;text-anchor:middle;font-family:sans-serif;font-style:normal;font-weight:normal" x="577.6" y="343.8">      <tspan x="577.6" y="343.8">EVENT</tspan>    </text>  </g>  <g>    <rect style="fill: #ffffff" x="680" y="320" width="75.2" height="38"/>    <rect style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x="680" y="320" width="75.2" height="38"/>    <text font-size="12.8" style="fill: #000000;text-anchor:middle;font-family:sans-serif;font-style:normal;font-weight:normal" x="717.6" y="343.8">      <tspan x="717.6" y="343.8">ENAB</tspan>    </text>  </g>  <g>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="277.6,320 277.6,279 355.528,279 "/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="347.764,284 357.764,279 347.764,274 "/>  </g>  <g>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="277.6,358 277.6,399 354.778,399 "/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="347.014,404 357.014,399 347.014,394 "/>  </g>  <g>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="496.65,279 577.6,279 577.6,315.528 "/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="572.6,307.764 577.6,317.764 582.6,307.764 "/>  </g>  <g>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="497.4,399 577.6,399 577.6,362.472 "/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="582.6,370.236 577.6,360.236 572.6,370.236 "/>  </g>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="615.2" y1="339" x2="675.528" y2="339"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="667.764,344 677.764,339 667.764,334 "/>  </g>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke-dasharray: 4; stroke: #000000" x1="755.2" y1="339" x2="847.528" y2="339"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="839.764,344 849.764,339 839.764,334 "/>  </g>  <g>    <rect style="fill: #ffffff" x="852" y="320" width="75.2" height="38"/>    <rect style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x="852" y="320" width="75.2" height="38"/>    <text font-size="12.8" style="fill: #000000;text-anchor:middle;font-family:sans-serif;font-style:normal;font-weight:normal" x="889.6" y="343.8">      <tspan x="889.6" y="343.8">CON</tspan>    </text>  </g></svg>
      <p>The Condition register always reflects the current state. For example if a sensor is connected or not. The Positive Transition register masks if a
      certain condition becomes true (bit changes from 0 to 1) to set the bit at the same position in the Event register. For example to set the Event register 
      bit to 1 if the sensor got connected. <br/>The Negative Transition register tests if condition becomes false to update the Event register. For example to 
      set event register bit to 1 if the sensor got disconnected. The Enable register masks if changes of the Event register should be reported to the 
      higher level hierarchy Condition register. The register sets are logically chained. The following section will provide further details about the chaining.</p>
      <p>All registers except the STB register group are 16 bit wide. The following paragraph gives a short description of bit mask in general. Bit mask use the binary
      representation of numbers to store boolean states (true, false) or (active, inactive). This is a memory saving way to store multiple conditions at once.
      If you look on a decimal number 9 for example the binary representation is 1001 = 1 * 2 ^ 4 + 1 * 2 ^ 0. This bit mask means condition at bit position 1 and 4 are true. 
      All other conditions are false as all other bits are set to 0.<br/>
      To test if a certain bit is set to 1 you can either use bit logic operations or use generic mathematic operations. Bit logic operations are available in almost every 
      programming language. In Python you would write something like condition = bitmask &amp; (1 &lt;&lt; position).<br/>
      If you want to use standard math you can use the following formula: condition = round_down(bitmask / (2 ^ bitPos)) modulus 2.</p>
      <p>The register sets are linked together in an hirarchical way. This enables you to check for a summarized condition only. If the 
      highlevel condition becomes true you can than go down the hierarchy to query the details causing the event.</p>
        
      <svg width="600px" viewBox="820 1857 642 344" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">  <g>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1100,2022 1160,2022 1160,2180 1315.53,2180 "/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1307.76,2185 1317.76,2180 1307.76,2175 "/>  </g>  <g>    <rect style="fill: #ffffff" x="1320" y="2020" width="140" height="180"/>    <rect style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x="1320" y="2020" width="140" height="180"/>    <text font-size="14.1111" style="fill: #000000;text-anchor:end;font-family:sans-serif;font-style:normal;font-weight:700" x="1451" y="2115.28">      <tspan x="1451" y="2115.28">*STB</tspan>    </text>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1180" y="2058">    <tspan x="1180" y="2058">Protection event </tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1180" y="2078">    <tspan x="1180" y="2078">Error queue state chng</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1180" y="2098">    <tspan x="1180" y="2098">Questionable status</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="1200" y1="2060" x2="1315.53" y2="2060"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1307.76,2065 1317.76,2060 1307.76,2055 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1180" y="2118">    <tspan x="1180" y="2118">Message Available</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="1200" y1="2080" x2="1315.53" y2="2080"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1307.76,2085 1317.76,2080 1307.76,2075 "/>  </g>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="1200" y1="2120" x2="1315.53" y2="2120"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1307.76,2125 1317.76,2120 1307.76,2115 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1180" y="2138">    <tspan x="1180" y="2138">Standard Event Status</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="1200" y1="2140" x2="1315.53" y2="2140"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1307.76,2145 1317.76,2140 1307.76,2135 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1180" y="2158">    <tspan x="1180" y="2158">Service Request</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="1200" y1="2160" x2="1315.53" y2="2160"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1307.76,2165 1317.76,2160 1307.76,2155 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1180" y="2178">    <tspan x="1180" y="2178">Operation Status</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="1200" y1="2180" x2="1315.53" y2="2180"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1307.76,2185 1317.76,2180 1307.76,2175 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1324" y="2044">    <tspan x="1324" y="2044">bit 0(1)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1324" y="2064">    <tspan x="1324" y="2064">bit 1(2)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1324" y="2084">    <tspan x="1324" y="2084">bit 2(4)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1324" y="2104">    <tspan x="1324" y="2104">bit 3(8)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1324" y="2124">    <tspan x="1324" y="2124">bit 4(16)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1324" y="2144">    <tspan x="1324" y="2144">bit 5(32)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1324" y="2164">    <tspan x="1324" y="2164">bit 6(64)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1324" y="2184">    <tspan x="1324" y="2184">bit 7(128)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1180" y="2038">    <tspan x="1180" y="2038">Aux Status </tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="1200" y1="2040" x2="1315.53" y2="2040"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1307.76,2045 1317.76,2040 1307.76,2035 "/>  </g>  <g>    <rect style="fill: #ffffff" x="960" y="1860" width="140" height="324"/>    <rect style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x="960" y="1860" width="140" height="324"/>    <text font-size="14.1111" style="fill: #000000;text-anchor:end;font-family:sans-serif;font-style:normal;font-weight:700" x="1091" y="2018.46">      <tspan x="1091" y="2018.46">STAT:</tspan>      <tspan x="1091" y="2036.1">OPER</tspan>    </text>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="820" y="1870">    <tspan x="820" y="1870">reserved</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="820" y="1890">    <tspan x="820" y="1890">reserved</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="820" y="1910">    <tspan x="820" y="1910">Ranging</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="840" y1="1872" x2="955.528" y2="1872"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="947.764,1877 957.764,1872 947.764,1867 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="964" y="1876">    <tspan x="964" y="1876">bit 0(1)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="820" y="1930">    <tspan x="820" y="1930">reserved</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="840" y1="1892" x2="955.528" y2="1892"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="947.764,1897 957.764,1892 947.764,1887 "/>  </g>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="840" y1="1912" x2="955.528" y2="1912"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="947.764,1917 957.764,1912 947.764,1907 "/>  </g>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="840" y1="1932" x2="955.528" y2="1932"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="947.764,1937 957.764,1932 947.764,1927 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="820" y="1950">    <tspan x="820" y="1950">Measuring</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="840" y1="1952" x2="955.528" y2="1952"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="947.764,1957 957.764,1952 947.764,1947 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="820" y="1970">    <tspan x="820" y="1970">reserved</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="840" y1="1972" x2="955.528" y2="1972"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="947.764,1977 957.764,1972 947.764,1967 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="820" y="1990">    <tspan x="820" y="1990">reserved</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="840" y1="1992" x2="955.528" y2="1992"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="947.764,1997 957.764,1992 947.764,1987 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="820" y="2010">    <tspan x="820" y="2010">Zeroing</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="840" y1="2012" x2="955.528" y2="2012"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="947.764,2017 957.764,2012 947.764,2007 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="820" y="2030">    <tspan x="820" y="2030">Sensor connected</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="840" y1="2032" x2="955.528" y2="2032"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="947.764,2037 957.764,2032 947.764,2027 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="820" y="2050">    <tspan x="820" y="2050">Data ready to fetch</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="840" y1="2052" x2="955.528" y2="2052"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="947.764,2057 957.764,2052 947.764,2047 "/>  </g>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="840" y1="2072" x2="955.528" y2="2072"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="947.764,2077 957.764,2072 947.764,2067 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="820" y="2090">    <tspan x="820" y="2090">reserved</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="840" y1="2092" x2="955.528" y2="2092"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="947.764,2097 957.764,2092 947.764,2087 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="820" y="2110">    <tspan x="820" y="2110">reserved</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="840" y1="2112" x2="955.528" y2="2112"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="947.764,2117 957.764,2112 947.764,2107 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="820" y="2170">    <tspan x="820" y="2170">reserved</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="840" y1="2172" x2="955.528" y2="2172"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="947.764,2177 957.764,2172 947.764,2167 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="964" y="1896">    <tspan x="964" y="1896">bit 1(2)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="964" y="1916">    <tspan x="964" y="1916">bit 2(4)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="964" y="1936">    <tspan x="964" y="1936">bit 3(8)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="964" y="1956">    <tspan x="964" y="1956">bit 4(16)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="964" y="1976">    <tspan x="964" y="1976">bit 5(32)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="964" y="1996">    <tspan x="964" y="1996">bit 6(64)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="964" y="2016">    <tspan x="964" y="2016">bit 7(128)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="964" y="2036">    <tspan x="964" y="2036">bit 8(256)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="964" y="2056">    <tspan x="964" y="2056">bit 9(512)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="964" y="2076">    <tspan x="964" y="2076">bit 10(1024)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="964" y="2096">    <tspan x="964" y="2096">bit 11(2048)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="964" y="2116">    <tspan x="964" y="2116">bit 12(4096)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="964" y="2136">    <tspan x="964" y="2136">bit 13(8192)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="964" y="2156">    <tspan x="964" y="2156">bit 14(16384)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="964" y="2176">    <tspan x="964" y="2176">bit 15(32768)</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="1200" y1="2100" x2="1315.53" y2="2100"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1307.76,2105 1317.76,2100 1307.76,2095 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="820" y="2068">    <tspan x="820" y="2068">Thermo accelerator</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="820" y="2130">    <tspan x="820" y="2130">reserved</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="840" y1="2132" x2="955.528" y2="2132"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="947.764,2137 957.764,2132 947.764,2127 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="820" y="2150">    <tspan x="820" y="2150">reserved</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="840" y1="2152" x2="955.528" y2="2152"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="947.764,2157 957.764,2152 947.764,2147 "/>  </g></svg>
      <p>The STAT:OPER status register set contains state information about the instrument’s normal
      operation. The register set might report to the SCPI Standard Byte register bit 7 if 
      STAT:OPER:ENAB bit mask is set active.</p>  

     <svg width="600px" viewBox="820 1057 642 328" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">  <g>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1100,1202 1160,1202 1160,1280 1315.53,1280 "/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1307.76,1285 1317.76,1280 1307.76,1275 "/>  </g>  <g>    <rect style="fill: #ffffff" x="960" y="1060" width="140" height="324"/>    <rect style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x="960" y="1060" width="140" height="324"/>    <text font-size="14.1111" style="fill: #000000;text-anchor:end;font-family:sans-serif;font-style:normal;font-weight:700" x="1091" y="1218.46">      <tspan x="1091" y="1218.46">STAT:</tspan>      <tspan x="1091" y="1236.1">QUEST</tspan>    </text>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="820" y="1070">    <tspan x="820" y="1070">Questionable Voltage</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="820" y="1090">    <tspan x="820" y="1090">Questionable Current</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="820" y="1110">    <tspan x="820" y="1110">reserved</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="840" y1="1072" x2="955.528" y2="1072"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="947.764,1077 957.764,1072 947.764,1067 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="964" y="1076">    <tspan x="964" y="1076">bit 0(1)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="820" y="1130">    <tspan x="820" y="1130">Questionable Power</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="840" y1="1092" x2="955.528" y2="1092"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="947.764,1097 957.764,1092 947.764,1087 "/>  </g>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="840" y1="1112" x2="955.528" y2="1112"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="947.764,1117 957.764,1112 947.764,1107 "/>  </g>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="840" y1="1132" x2="955.528" y2="1132"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="947.764,1137 957.764,1132 947.764,1127 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="820" y="1150">    <tspan x="820" y="1150">reserved</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="840" y1="1152" x2="955.528" y2="1152"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="947.764,1157 957.764,1152 947.764,1147 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="820" y="1170">    <tspan x="820" y="1170">reserved</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="840" y1="1172" x2="955.528" y2="1172"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="947.764,1177 957.764,1172 947.764,1167 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="820" y="1190">    <tspan x="820" y="1190">reserved</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="840" y1="1192" x2="955.528" y2="1192"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="947.764,1197 957.764,1192 947.764,1187 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="820" y="1210">    <tspan x="820" y="1210">reserved</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="840" y1="1212" x2="955.528" y2="1212"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="947.764,1217 957.764,1212 947.764,1207 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="820" y="1230">    <tspan x="820" y="1230">Questionable Calibrat</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="840" y1="1232" x2="955.528" y2="1232"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="947.764,1237 957.764,1232 947.764,1227 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="820" y="1250">    <tspan x="820" y="1250">reserved</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="840" y1="1252" x2="955.528" y2="1252"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="947.764,1257 957.764,1252 947.764,1247 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="820" y="1270">    <tspan x="820" y="1270">reserved</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="840" y1="1272" x2="955.528" y2="1272"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="947.764,1277 957.764,1272 947.764,1267 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="820" y="1290">    <tspan x="820" y="1290">reserved</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="840" y1="1292" x2="955.528" y2="1292"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="947.764,1297 957.764,1292 947.764,1287 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="820" y="1310">    <tspan x="820" y="1310">reserved</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="840" y1="1312" x2="955.528" y2="1312"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="947.764,1317 957.764,1312 947.764,1307 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="820" y="1370">    <tspan x="820" y="1370">reserved</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="840" y1="1372" x2="955.528" y2="1372"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="947.764,1377 957.764,1372 947.764,1367 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="964" y="1096">    <tspan x="964" y="1096">bit 1(2)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="964" y="1116">    <tspan x="964" y="1116">bit 2(4)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="964" y="1136">    <tspan x="964" y="1136">bit 3(8)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="964" y="1156">    <tspan x="964" y="1156">bit 4(16)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="964" y="1176">    <tspan x="964" y="1176">bit 5(32)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="964" y="1196">    <tspan x="964" y="1196">bit 6(64)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="964" y="1216">    <tspan x="964" y="1216">bit 7(128)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="964" y="1236">    <tspan x="964" y="1236">bit 8(256)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="964" y="1256">    <tspan x="964" y="1256">bit 9(512)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="964" y="1276">    <tspan x="964" y="1276">bit 10(1024)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="964" y="1296">    <tspan x="964" y="1296">bit 11(2048)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="964" y="1316">    <tspan x="964" y="1316">bit 12(4096)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="964" y="1336">    <tspan x="964" y="1336">bit 13(8192)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="964" y="1356">    <tspan x="964" y="1356">bit 14(16384)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="964" y="1376">    <tspan x="964" y="1376">bit 15(32768)</tspan>  </text>  <g>    <rect style="fill: #ffffff" x="1320" y="1200" width="140" height="180"/>    <rect style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x="1320" y="1200" width="140" height="180"/>    <text font-size="14.1111" style="fill: #000000;text-anchor:end;font-family:sans-serif;font-style:normal;font-weight:700" x="1451" y="1295.28">      <tspan x="1451" y="1295.28">*STB</tspan>    </text>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1180" y="1238">    <tspan x="1180" y="1238">Protection event </tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1180" y="1258">    <tspan x="1180" y="1258">Error queue state chng</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1180" y="1278">    <tspan x="1180" y="1278">Questionable status</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="1200" y1="1240" x2="1315.53" y2="1240"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1307.76,1245 1317.76,1240 1307.76,1235 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1180" y="1298">    <tspan x="1180" y="1298">Message Available</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="1200" y1="1260" x2="1315.53" y2="1260"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1307.76,1265 1317.76,1260 1307.76,1255 "/>  </g>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="1200" y1="1300" x2="1315.53" y2="1300"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1307.76,1305 1317.76,1300 1307.76,1295 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1180" y="1318">    <tspan x="1180" y="1318">Standard Event Status</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="1200" y1="1320" x2="1315.53" y2="1320"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1307.76,1325 1317.76,1320 1307.76,1315 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1180" y="1338">    <tspan x="1180" y="1338">Service Request</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="1200" y1="1340" x2="1315.53" y2="1340"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1307.76,1345 1317.76,1340 1307.76,1335 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1180" y="1358">    <tspan x="1180" y="1358">Operation Status</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="1200" y1="1360" x2="1315.53" y2="1360"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1307.76,1365 1317.76,1360 1307.76,1355 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1324" y="1224">    <tspan x="1324" y="1224">bit 0(1)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1324" y="1244">    <tspan x="1324" y="1244">bit 1(2)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1324" y="1264">    <tspan x="1324" y="1264">bit 2(4)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1324" y="1284">    <tspan x="1324" y="1284">bit 3(8)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1324" y="1304">    <tspan x="1324" y="1304">bit 4(16)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1324" y="1324">    <tspan x="1324" y="1324">bit 5(32)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1324" y="1344">    <tspan x="1324" y="1344">bit 6(64)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1324" y="1364">    <tspan x="1324" y="1364">bit 7(128)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1180" y="1218">    <tspan x="1180" y="1218">Aux Status </tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="1200" y1="1220" x2="1315.53" y2="1220"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1307.76,1225 1317.76,1220 1307.76,1215 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="820" y="1330">    <tspan x="820" y="1330">reserved</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="840" y1="1332" x2="955.528" y2="1332"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="947.764,1337 957.764,1332 947.764,1327 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="820" y="1350">    <tspan x="820" y="1350">reserved</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="840" y1="1352" x2="955.528" y2="1352"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="947.764,1357 957.764,1352 947.764,1347 "/>  </g></svg>
     <p>The QUEStionable status register set contains bits which give an indication of the quality of
      various aspects of the measurement signal. The register set might report to the SCPI Standard 
      Byte register bit 3 if STAT:OPER:ENAB bit mask is set active.</p>
     
     <svg width="600px" viewBox="660 117 642 344" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">  <g>    <rect style="fill: #ffffff" x="800" y="120" width="140" height="324"/>    <rect style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x="800" y="120" width="140" height="324"/>    <text font-size="14.1111" style="fill: #000000;text-anchor:end;font-family:sans-serif;font-style:normal;font-weight:700" x="931" y="278.461">      <tspan x="931" y="278.461">STAT:</tspan>      <tspan x="931" y="296.1">AUX</tspan>    </text>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="660" y="130">    <tspan x="660" y="130">Ext NTC connected</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="660" y="170">    <tspan x="660" y="170">Custom cal supported</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="680" y1="132" x2="795.528" y2="132"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="787.764,137 797.764,132 787.764,127 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="804" y="136">    <tspan x="804" y="136">bit 0(1)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="660" y="190">    <tspan x="660" y="190">Custom cal active</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="680" y1="152" x2="795.528" y2="152"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="787.764,157 797.764,152 787.764,147 "/>  </g>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="680" y1="172" x2="795.528" y2="172"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="787.764,177 797.764,172 787.764,167 "/>  </g>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="680" y1="192" x2="795.528" y2="192"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="787.764,197 797.764,192 787.764,187 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="660" y="210">    <tspan x="660" y="210">reserved</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="680" y1="212" x2="795.528" y2="212"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="787.764,217 797.764,212 787.764,207 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="660" y="230">    <tspan x="660" y="230">reserved</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="680" y1="232" x2="795.528" y2="232"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="787.764,237 797.764,232 787.764,227 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="660" y="250">    <tspan x="660" y="250">reserved</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="680" y1="252" x2="795.528" y2="252"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="787.764,257 797.764,252 787.764,247 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="660" y="270">    <tspan x="660" y="270">reserved</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="680" y1="272" x2="795.528" y2="272"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="787.764,277 797.764,272 787.764,267 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="660" y="290">    <tspan x="660" y="290">reserved</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="680" y1="292" x2="795.528" y2="292"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="787.764,297 797.764,292 787.764,287 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="660" y="310">    <tspan x="660" y="310">reserved</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="680" y1="312" x2="795.528" y2="312"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="787.764,317 797.764,312 787.764,307 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="660" y="330">    <tspan x="660" y="330">reserved</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="680" y1="332" x2="795.528" y2="332"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="787.764,337 797.764,332 787.764,327 "/>  </g>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="680" y1="352" x2="795.528" y2="352"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="787.764,357 797.764,352 787.764,347 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="660" y="370">    <tspan x="660" y="370">reserved</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="680" y1="372" x2="795.528" y2="372"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="787.764,377 797.764,372 787.764,367 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="660" y="410">    <tspan x="660" y="410">reserved</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="680" y1="412" x2="795.528" y2="412"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="787.764,417 797.764,412 787.764,407 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="660" y="430">    <tspan x="660" y="430">reserved</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="680" y1="432" x2="795.528" y2="432"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="787.764,437 797.764,432 787.764,427 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="804" y="156">    <tspan x="804" y="156">bit 1(2)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="804" y="176">    <tspan x="804" y="176">bit 2(4)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="804" y="196">    <tspan x="804" y="196">bit 3(8)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="804" y="216">    <tspan x="804" y="216">bit 4(16)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="804" y="236">    <tspan x="804" y="236">bit 5(32)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="804" y="256">    <tspan x="804" y="256">bit 6(64)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="804" y="276">    <tspan x="804" y="276">bit 7(128)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="804" y="296">    <tspan x="804" y="296">bit 8(256)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="804" y="316">    <tspan x="804" y="316">bit 9(512)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="804" y="336">    <tspan x="804" y="336">bit 10(1024)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="804" y="356">    <tspan x="804" y="356">bit 11(2048)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="804" y="376">    <tspan x="804" y="376">bit 12(4096)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="804" y="396">    <tspan x="804" y="396">bit 13(8192)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="804" y="416">    <tspan x="804" y="416">bit 14(16384)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="804" y="436">    <tspan x="804" y="436">bit 15(32768)</tspan>  </text>  <g>    <rect style="fill: #ffffff" x="1160" y="280" width="140" height="180"/>    <rect style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x="1160" y="280" width="140" height="180"/>    <text font-size="14.1111" style="fill: #000000;text-anchor:end;font-family:sans-serif;font-style:normal;font-weight:700" x="1291" y="375.281">      <tspan x="1291" y="375.281">*STB</tspan>    </text>  </g>  <g>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="940,282 1000,282 1000,300 1155.53,300 "/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1147.76,305 1157.76,300 1147.76,295 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1020" y="318">    <tspan x="1020" y="318">Protection event </tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1020" y="338">    <tspan x="1020" y="338">Error queue state chng</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1020" y="358">    <tspan x="1020" y="358">Questionable status</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="1040" y1="320" x2="1155.53" y2="320"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1147.76,325 1157.76,320 1147.76,315 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1020" y="378">    <tspan x="1020" y="378">Message Available</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="1040" y1="340" x2="1155.53" y2="340"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1147.76,345 1157.76,340 1147.76,335 "/>  </g>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="1040" y1="360" x2="1155.53" y2="360"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1147.76,365 1157.76,360 1147.76,355 "/>  </g>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="1040" y1="380" x2="1155.53" y2="380"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1147.76,385 1157.76,380 1147.76,375 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1020" y="398">    <tspan x="1020" y="398">Standard Event Status</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="1040" y1="400" x2="1155.53" y2="400"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1147.76,405 1157.76,400 1147.76,395 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1020" y="418">    <tspan x="1020" y="418">Service Request</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="1040" y1="420" x2="1155.53" y2="420"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1147.76,425 1157.76,420 1147.76,415 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1020" y="438">    <tspan x="1020" y="438">Operation Status</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="1040" y1="440" x2="1155.53" y2="440"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1147.76,445 1157.76,440 1147.76,435 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1164" y="304">    <tspan x="1164" y="304">bit 0(1)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1164" y="324">    <tspan x="1164" y="324">bit 1(2)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1164" y="344">    <tspan x="1164" y="344">bit 2(4)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1164" y="364">    <tspan x="1164" y="364">bit 3(8)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1164" y="384">    <tspan x="1164" y="384">bit 4(16)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1164" y="404">    <tspan x="1164" y="404">bit 5(32)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1164" y="424">    <tspan x="1164" y="424">bit 6(64)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1164" y="444">    <tspan x="1164" y="444">bit 7(128)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="660" y="390">    <tspan x="660" y="390">reserved</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="680" y1="392" x2="795.528" y2="392"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="787.764,397 797.764,392 787.764,387 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="660" y="348">    <tspan x="660" y="348">reserved</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="660" y="148">    <tspan x="660" y="148">reserved</tspan>  </text></svg>
     <p>The AUXilary register set contains state informatino about the instrument auxiliary like the 
      NTC or power supply. The register set might report to the SCPI Standard 
      Byte register bit 0 if STAT:OPER:ENAB bit mask is set active.</p>
      
    </div>

    <p><a href="#" id="foldAll">Unfold all SCPI Commands</a></p>
    <hr>
    <p id="None"><a href="#CLS" data-subsystem="None" class="unfoldSection">Unfold all None Commands</a></p>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="None">
        <span class="scpi" id="CLS">CLS</span><span class="briefScpi">Clears all SCPI event status registers</span>
      </div>
      <div class="content">
        <div class="scpiLong">*CLS</div>
        <div class="details">Use this command to clear all event bits in the SCPI event status registers. After calling this command all event registers are read as 0 as long no new event changes bits back to 1. For closer details about event registers read for example description of command <a class="linkSCPI" title="Queries the SCPI Operation Event register" href="#STAT:OPER?">STAT:OPER?</a>.</div>
        <div class="example">&gt;&gt; *CLS</div>

      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="None">
        <span class="scpi" id="ESE">ESE</span><span class="briefScpi">Programs the 8 bit wide Standard Event Enable Register</span>
      </div>
      <div class="content">
        <div class="scpiLong">*ESE <div class="tooltip" data-id="0">&lt;bitmask&gt;</div></div>
        <div class="paramDocu">Parameters<ul>
              <li id="0"><span class="param">bitmask</span><span class="paramType">uint</span> <span class="paramDesc">standard even enable register bit mask.</span></li>
        </ul></div>

        <div class="example">&gt;&gt; ESE 2</div>

      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="None">
        <span class="scpi" id="ESE?">ESE?</span><span class="briefScpi">Reads the 8 bit wide Standard Event Enable Register</span>
      </div>
      <div class="content">
        <div class="scpiLong">*ESE?</div>
        <div class="resultDocu">Response<ul>
          <li><span class="param">bitmask</span><span class="paramType">uint</span> <span class="paramDesc">standard even enable register bit mask.</span></li>
        </ul></div>

        <div class="example">&gt;&gt; ESE?<br />&lt;&lt; 0</div>

      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="None">
        <span class="scpi" id="ESR?">ESR?</span><span class="briefScpi">Reads and clears the Standard Event Register</span>
      </div>
      <div class="content">
        <div class="scpiLong">*ESR?</div>
        <div class="resultDocu">Response<ul>
          <li><span class="param">bitmask</span><span class="paramType">uint</span> <span class="paramDesc">standard byte event register bit mask.</span></li>
        </ul></div>

        <div class="example">&gt;&gt; ESR?<br />&lt;&lt; 2</div>

      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="None">
        <span class="scpi" id="IDN?">IDN?</span><span class="briefScpi">Reads the device identification string</span>
      </div>
      <div class="content">
        <div class="scpiLong">*IDN?</div>
        <div class="resultDocu">Response<ul>
          <li><span class="param">manuf</span><span class="paramType">string</span> <span class="paramDesc">Device manufacturer name.</span></li>
          <li><span class="param">devName</span><span class="paramType">string</span> <span class="paramDesc">Device name.</span></li>
          <li><span class="param">serial</span><span class="paramType">string</span> <span class="paramDesc">Device serial number.</span></li>
          <li><span class="param">version</span><span class="paramType">string</span> <span class="paramDesc">Firmware software number.</span></li>
        </ul></div>

        <div class="details">Call to query device identification string.</div>
        <div class="example">&gt;&gt; *IDN?<br />&lt;&lt; Thorlabs,PM103,T00000002,0.1.0.1</div>

      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="None">
        <span class="scpi" id="OPC">OPC</span><span class="briefScpi">Sets the Operation Complete bit in the Standard Event Register</span>
      </div>
      <div class="content">
        <div class="scpiLong">*OPC</div>
        <div class="example">&gt;&gt; OPC 2</div>

      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="None">
        <span class="scpi" id="OPC?">OPC?</span><span class="briefScpi">Operation complete query</span>
      </div>
      <div class="content">
        <div class="scpiLong">*OPC?</div>
        <div class="resultDocu">Response<ul>
          <li><span class="param">constant</span><span class="paramType">uint</span> <span class="paramDesc">Always 1</span></li>
        </ul></div>

        <div class="details">Places a "1" into the output queue when all device operations have been completed No real functionality on this implementation. Result is always 1.</div>
        <div class="example">&gt;&gt; OPC?<br />&lt;&lt; 1</div>

      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="None">
        <span class="scpi" id="RST">RST</span><span class="briefScpi">Resets all SCPI and sensor parameters to default</span>
      </div>
      <div class="content">
        <div class="scpiLong">*RST</div>
        <div class="details"> The command resets all SCPI parameters like serial baudrate and sensor parameters like wavelength to defaults. Both sensor channels are reset to defaults. To reset the SCPI registers use <a class="linkSCPI" title="Clears all SCPI event status registers" href="#CLS">CLS</a>.</div>
        <div class="example">&gt;&gt; RST</div>

      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="None">
        <span class="scpi" id="SRE">SRE</span><span class="briefScpi">Service request enable command</span>
      </div>
      <div class="content">
        <div class="scpiLong">*SRE <div class="tooltip" data-id="1">&lt;bitmask&gt;</div></div>
        <div class="paramDocu">Parameters<ul>
              <li id="1"><span class="param">bitmask</span><span class="paramType">uint</span> <span class="paramDesc">new service request enable register bit mask.</span></li>
        </ul></div>

        <div class="details">Programs the Service Request Enable Register</div>
        <div class="example">&gt;&gt; SRE 2</div>

      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="None">
        <span class="scpi" id="SRE?">SRE?</span><span class="briefScpi">Service request enable query</span>
      </div>
      <div class="content">
        <div class="scpiLong">*SRE?</div>
        <div class="resultDocu">Response<ul>
          <li><span class="param">bitmask</span><span class="paramType">uint</span> <span class="paramDesc">service request enable register bit mask.</span></li>
        </ul></div>

        <div class="details">Reads the Service Request Enable Register</div>
        <div class="example">&gt;&gt; SRE?<br />&lt;&lt; 0</div>

      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="None">
        <span class="scpi" id="STB?">STB?</span><span class="briefScpi">Status byte query</span>
      </div>
      <div class="content">
        <div class="scpiLong">*STB?</div>
        <div class="resultDocu">Response<ul>
          <li><span class="param">bitmask</span><span class="paramType">uint</span> <span class="paramDesc">status byte condition register bit mask.</span></li>
        </ul></div>

        <div class="details">Reads the Status Byte Register</div>
        <div class="example">&gt;&gt; STB?<br />&lt;&lt; 20</div>

      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="None">
        <span class="scpi" id="WAI">WAI</span><span class="briefScpi">Wait-to-continue command</span>
      </div>
      <div class="content">
        <div class="scpiLong">*WAI</div>
        <div class="details">Wait until all previous commands are executed. Empty implementation.</div>
        <div class="example">&gt;&gt; WAI</div>

      </div>
    </div>
    <hr>
    <p id="CAL"><a href="#CAL:STR?" data-subsystem="CAL" class="unfoldSection">Unfold all CAL Commands</a></p>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="CAL">
        <span class="scpi" id="CAL:STR?">CAL:STR?</span><span class="briefScpi">Queries factory calibration string</span>
      </div>
      <div class="content">
        <div class="scpiLong">CALibration:STRing?</div>
        <div class="resultDocu">Response<ul>
          <li><span class="param">date</span><span class="paramType">string</span> <span class="paramDesc">Cal string with format dd-MonthStr-YYYY or 'Uncalibrated' when not calibrated.</span></li>
        </ul></div>

        <div class="details">Use this command to query the factory calibration date string of the sensor measure frontend.</div>
        <div class="example">&gt;&gt; CAL:STR?<br />&lt;&lt; "18-Feb-2022"</div>

      </div>
    </div>
    <hr>
    <p id="INP"><a href="#INP:ADAP" data-subsystem="INP" class="unfoldSection">Unfold all INP Commands</a></p>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="INP">
        <span class="scpi" id="INP:ADAP">INP:ADAP</span><span class="briefScpi">Sets the default adapter sensor type</span>
      </div>
      <div class="content">
        <div class="scpiLong">INPut:ADAPter[:TYPE] <div class="tooltip" data-id="189">&lt;type&gt;</div></div>
        <div class="paramDocu">Parameters<ul>
              <li id="189"><span class="param">type</span><span class="paramType">special</span> <span class="paramDesc">Configure adapter type. See list in description.</span></li>
        </ul></div>

        <div class="details">Use this command to change the default adapter sensor type. Adapters are sensors without a head EEPROM. if currently no sensor is connected this function will change the default adapter sensor type. If an adapter is currently connected the function will re-enumerate the new adapter type on the Power Meter. Adapter type is stored persistently and will be reused after reboot automatically. The following adapter types are defined: <ul class="detailListing"> <li><b>PHOTodiode</b> Photodiode sensor adapter</li> <li><b>THERmal</b> Thermopile sensor adapter</li> <li><b>PDA</b> Preamplified photodiode adapter</li> <li><b>Q4</b> Four Quadrant Thermopile sensor adapter</li> <li><b>LUMinance</b> Photometric sensor adapter</li> </ul></div>
        <div class="example">&gt;&gt; INP:ADAP PYR</div>

      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="INP">
        <span class="scpi" id="INP:ADAP?">INP:ADAP?</span><span class="briefScpi">Queries the default adapter sensor type</span>
      </div>
      <div class="content">
        <div class="scpiLong">INPut:ADAPter[:TYPE]?</div>
        <div class="resultDocu">Response<ul>
          <li><span class="param">type</span><span class="paramType">special</span> <span class="paramDesc">Currently configured adapter type. For list read <a class="linkSCPI" title="Sets the default adapter sensor type" href="#INP:ADAP">INP:ADAP</a>.</span></li>
        </ul></div>

        <div class="details">Use this command read the adapter sensor type. Adapters are sensors without a head EEPROM. You can use this command also if no adapter is currently connected. For closer details about adapter type read <a class="linkSCPI" title="Sets the default adapter sensor type" href="#INP:ADAP">INP:ADAP</a>.</div>
        <div class="example">&gt;&gt; INP:ADAP?<br />&lt;&lt; PYR</div>

      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="INP">
        <span class="scpi" id="INP:FILT">INP:FILT</span><span class="briefScpi">Enables/Disables bandwidth limitation</span>
      </div>
      <div class="content">
        <div class="scpiLong">INPut[:PDIode]:FILTer[:LPASs][:STATe] <div class="tooltip" data-id="187">&lt;enable&gt;</div></div>
        <div class="paramDocu">Parameters<ul>
              <li id="187"><span class="param">enable</span><span class="paramType">bool</span> <span class="paramDesc">True to limit bandwidth. False for full bandwidth.</span></li>
        </ul></div>

        <div class="details">Use this command to enable or disable the bandwidth limitation of the sensor signal amplifier. This command is useful for CW signals to suppress noise.  For modulated signals ensure bandwidth is set to high.</div>
        <div class="example">&gt;&gt; INP:FILT 0</div>
        <div class="info">When active bandwidth is limited to approximately 3 Hz.</div>
      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="INP">
        <span class="scpi" id="INP:FILT?">INP:FILT?</span><span class="briefScpi">Queries the default adapter sensor type</span>
      </div>
      <div class="content">
        <div class="scpiLong">INPut[:PDIode]:FILTer[:LPASs][:STATe]?</div>
        <div class="resultDocu">Response<ul>
          <li><span class="param">state</span><span class="paramType">bool</span> <span class="paramDesc">True if bandwidth limited. False when full bandwidth.</span></li>
        </ul></div>

        <div class="details">Use this command read the adapter sensor type. Adapters are sensors without a head EEPROM. You can use this command also if no adapter is currently connected. For closer details about adapter type read <a class="linkSCPI" title="Sets the default adapter sensor type" href="#INP:ADAP">INP:ADAP</a>.</div>
        <div class="example">&gt;&gt; INP:ADAP?<br />&lt;&lt; PYR</div>

      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="INP">
        <span class="scpi" id="INP:THER:ACC?">INP:THER:ACC?</span><span class="briefScpi">Tests if accelerator algorithm is currently operating</span>
      </div>
      <div class="content">
        <div class="scpiLong">INPut:THERmopile:ACCelerator[:STATe]?</div>
        <div class="resultDocu">Response<ul>
          <li><span class="param">state</span><span class="paramType">bool</span> <span class="paramDesc">True if active at the moment. False if not</span></li>
        </ul></div>

        <div class="details">Use this command for thermopile sensors to test if the accelerator algorithm is currently operating. Normally the algorithm terminates after 5 times <a class="linkSCPI" title="Sets thermopile sensor adapter specific time constant Tau" href="#INP:THER:ACC:TAU">INP:THER:ACC:TAU</a> automatically. For closer details read <a class="linkSCPI" title="Enables/Disables thermopile prediction" href="#INP:THER:ACC:AUTO">INP:THER:ACC:AUTO</a>.</div>
        <div class="example">&gt;&gt; INP:THER:ACC?<br />&lt;&lt; 0</div>
        <div class="note">Thermopile only!</div>

      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="INP">
        <span class="scpi" id="INP:THER:ACC:AUTO">INP:THER:ACC:AUTO</span><span class="briefScpi">Enables/Disables thermopile prediction</span>
      </div>
      <div class="content">
        <div class="scpiLong">INPut:THERmopile:ACCelerator:AUTO <div class="tooltip" data-id="188">&lt;enable&gt;</div></div>
        <div class="paramDocu">Parameters<ul>
              <li id="188"><span class="param">enable</span><span class="paramType">bool</span> <span class="paramDesc">true to enable thermopile prediction. False to disable.</span></li>
        </ul></div>

        <div class="details">Use this command to enable or disable the thermopile sensor value prediction algorithm. Thermopile sensors are relatively slow when light intensity changes fast. The prediction algorithm automatically calculates the resulting power during the sensor signal follows a logarithmic function during positive jump and a 1/e function during negative jump. The sensor behaves like a capacitor. In both cases after the time period of 5 Tau the signal reached 99% of the final level and prediction is stopped automatically. Tau is a sensor specific constant and is stored in the head EEPROM. For adapter sensors Tau can be changed using the function <a class="linkSCPI" title="Sets thermopile sensor adapter specific time constant Tau" href="#INP:THER:ACC:TAU">INP:THER:ACC:TAU</a>. To test if prediction is currently active check for the for accelerator flag bit 10 in the Operation Status register <a class="linkSCPI" title="Queries the SCPI Operation Condition register" href="#STAT:OPER:COND?">STAT:OPER:COND?</a>. <img src=" data:image/jpeg;base64,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"></img></div>
        <div class="example">&gt;&gt; INP:THER:ACC:AUTO 1</div>
        <div class="note">Thermopile only!</div>

      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="INP">
        <span class="scpi" id="INP:THER:ACC:AUTO?">INP:THER:ACC:AUTO?</span><span class="briefScpi">Queries if thermopile prediction is enabled/disabled</span>
      </div>
      <div class="content">
        <div class="scpiLong">INPut:THERmopile:ACCelerator:AUTO?</div>
        <div class="resultDocu">Response<ul>
          <li><span class="param">state</span><span class="paramType">bool</span> <span class="paramDesc">true if thermopile prediction is enabled. False when disabled.</span></li>
        </ul></div>

        <div class="details">Use this command to test if thermopile sensor value prediction algorithm is enabled or disable. It does not test if the algorithm is active at the moment. To test if prediction is currently active check for the for accelerator flag bit 10 in the Operation Status register <a class="linkSCPI" title="Queries the SCPI Operation Condition register" href="#STAT:OPER:COND?">STAT:OPER:COND?</a> or call <a class="linkSCPI" title="Tests if accelerator algorithm is currently operating" href="#INP:THER:ACC?">INP:THER:ACC?</a>. For closer details about thermopile prediction in general read <a class="linkSCPI" title="Enables/Disables thermopile prediction" href="#INP:THER:ACC:AUTO">INP:THER:ACC:AUTO</a> description. \~cmdResult: 1</div>
        <div class="example">&gt;&gt; INP:THER:ACC:AUTO?</div>
        <div class="note">Thermopile only!</div>

      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="INP">
        <span class="scpi" id="INP:THER:ACC:TAU">INP:THER:ACC:TAU</span><span class="briefScpi">Sets thermopile sensor adapter specific time constant Tau</span>
      </div>
      <div class="content">
        <div class="scpiLong">INPut:THERmopile:ACCelerator:TAU <div class="tooltip" data-id="190">&lt;tau&gt;</div></div>
        <div class="paramDocu">Parameters<ul>
              <li id="190"><span class="param">tau</span><span class="paramType">float,MIN,MAX,DEF</span> <span class="paramDesc">new time constant Tau in Seconds. Or predefined special value.</span></li>
        </ul></div>

        <div class="details">Use this command to set the time constant Tau for thermopile adapter sensors. For sensors with EEPROM this function is forbidden. The time constant is stored in the EEPROM already. use <a class="linkSCPI" title="Queries thermopile sensor specific time constant Tau" href="#INP:THER:ACC:TAU?">INP:THER:ACC:TAU?</a> to read the value. When working with adapters ensure the correct adapter type is selected by using <a class="linkSCPI" title="Sets the default adapter sensor type" href="#INP:ADAP">INP:ADAP</a>.</div>
        <div class="example">&gt;&gt; INP:THER:ACC:TAU 1.2</div>
        <div class="note">Thermopile adapters only!</div>

      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="INP">
        <span class="scpi" id="INP:THER:ACC:TAU?">INP:THER:ACC:TAU?</span><span class="briefScpi">Queries thermopile sensor specific time constant Tau</span>
      </div>
      <div class="content">
        <div class="scpiLong">INPut:THERmopile:ACCelerator:TAU? <div class="tooltip" data-id="191">&lt;special&gt;</div></div>
        <div class="paramDocu">Parameters<ul>
           <li id="191"><span class="optParam">special</span><span class="paramType">MIN,MAX,DEF</span> <span class="paramDesc">Optional! nothing to query actual value. Special to query predefined value.</span></li>
        </ul></div>

        <div class="resultDocu">Response<ul>
          <li><span class="param">tau</span><span class="paramType">float</span> <span class="paramDesc">sensor time constant tau in Seconds.</span></li>
        </ul></div>

        <div class="details">Use this command to query the thermopile specific time constant Tau. For sensor heads without EEPROM alias adapters you can change the constant by using <a class="linkSCPI" title="Sets thermopile sensor adapter specific time constant Tau" href="#INP:THER:ACC:TAU">INP:THER:ACC:TAU</a>. For all other sensors this function returns the time constant out of the head EEPROM.</div>
        <div class="example">&gt;&gt; INP:THER:ACC:TAU?<br />&lt;&lt; 1.2</div>
        <div class="note">Thermopile only!</div>

      </div>
    </div>
    <hr>
    <p id="MEAS"><a href="#ABOR" data-subsystem="MEAS" class="unfoldSection">Unfold all MEAS Commands</a></p>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="MEAS">
        <span class="scpi" id="ABOR">ABOR</span><span class="briefScpi">Aborts any previously started measurement</span>
      </div>
      <div class="content">
        <div class="scpiLong">ABORt</div>
        <div class="details">Use this command to abort any previously started measurement.</div>
        <div class="example">&gt;&gt; ABOR</div>
        <div class="note">Do not send ABOR in case of <a class="linkSCPI" title="Starts and queries measurement result for a previously configured unit" href="#READ?">READ?</a> or <a class="linkSCPI" title="Measures the sensor averaged power in Watt or dBm" href="#MEAS?">MEAS?</a> timeout within 10 seconds. Otherwise Communication will crash totally.</div>
        <div class="info">Abort is part of the SCPI measurement system ABOR, <a class="linkSCPI" title="Configures SCPI measure system for power measurement" href="#CONF">CONF</a>, <a class="linkSCPI" title="Starts a new single measurement for a previously configured unit" href="#INIT">INIT</a>, <a class="linkSCPI" title="Blocks until previously initiated measurement is complete and returns result finally" href="#FETC?">FETC?</a>.</div>
      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="MEAS">
        <span class="scpi" id="CONF">CONF</span><span class="briefScpi">Configures SCPI measure system for power measurement</span>
      </div>
      <div class="content">
        <div class="scpiLong">CONFigure[:SCALar][:POWer]</div>
        <div class="details">Use this command to configure the Powermeter for photodiode, thermopile or four quadrant thermopile power measurement in Watt or dBm for. The unit depends on the power unit configuration set by <a class="linkSCPI" title="Selects the power unit" href="#SENS#:POW:UNIT">SENS#:POW:UNIT</a> command. This command only prepares the measurement. Configure is part of the SCPI measurement system <a class="linkSCPI" title="Aborts any previously started measurement" href="#ABOR">ABOR</a>, <a class="linkSCPI" title="Configures SCPI measure system for power measurement" href="#CONF">CONF</a>, <a class="linkSCPI" title="Starts a new single measurement for a previously configured unit" href="#INIT">INIT</a>, <a class="linkSCPI" title="Blocks until previously initiated measurement is complete and returns result finally" href="#FETC?">FETC?</a>. If the measurement has been configured you can either call <a class="linkSCPI" title="Starts a new single measurement for a previously configured unit" href="#INIT">INIT</a> and <a class="linkSCPI" title="Blocks until previously initiated measurement is complete and returns result finally" href="#FETC?">FETC?</a> or use the shorter <a class="linkSCPI" title="Starts and queries measurement result for a previously configured unit" href="#READ?">READ?</a> to start the measurements.<br /> For closer details about this measurement mode read <a href="#slowMeasSystem" title="Slow Measurement Mode"> Slow measurement</a> section.</div>
        <div class="example">&gt;&gt; CONF</div>
        <div class="note">Photodiode, Thermopile and four quadrant thermopile only!</div>
        <div class="info">After configuring the measurement call <a class="linkSCPI" title="Starts and queries measurement result for a previously configured unit" href="#READ?">READ?</a> or <a class="linkSCPI" title="Starts a new single measurement for a previously configured unit" href="#INIT">INIT</a> and <a class="linkSCPI" title="Blocks until previously initiated measurement is complete and returns result finally" href="#FETC?">FETC?</a> to start and query measurement results finally.</div>
      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="MEAS">
        <span class="scpi" id="CONF?">CONF?</span><span class="briefScpi">Returns the actual configured unit for measurement system</span>
      </div>
      <div class="content">
        <div class="scpiLong">CONFigure?</div>
        <div class="resultDocu">Response<ul>
          <li><span class="param">conf</span><span class="paramType">string</span> <span class="paramDesc">abbreviation of configuration in upper case letters</span></li>
        </ul></div>

        <div class="details">Use this command to query the actual configuration of the SCPI measure system. The following configurations are returned: <ul class="detailListing"> <li><b>POW</b> for power measurements. See <a class="linkSCPI" title="Configures SCPI measure system for power measurement" href="#CONF">CONF</a></li> <li><b>CURR</b> for current measurements for PM101. See <a class="linkSCPI" title="Configures SCPI measure system for current measurement" href="#CONF#:CURR">CONF#:CURR</a></li> <li><b>VOLT</b> for power measurements. See <a class="linkSCPI" title="Configures SCPI measure system for voltage measurement" href="#CONF:VOLT">CONF:VOLT</a></li> <li><b>POWDENS</b> for power density measurements. See <a class="linkSCPI" title="Configures SCPI measure system for power density measurement" href="#CONF:PDEN">CONF:PDEN</a></li> <li><b>FREQ</b> for frequency measurements for PM101. See <a class="linkSCPI" title="Configures SCPI measure system for energy frequency measurement" href="#CONF:FREQ">CONF:FREQ</a></li> <li><b>TEMP</b> for head NTC temperature measurements. See <a class="linkSCPI" title="Configures SCPI measurement system for head temperature measurement" href="#CONF:TEMP">CONF:TEMP</a></li> <li><b>RES</b> for head NTC resistance measurements. See <a class="linkSCPI" title="Configures SCPI measure system for head resistance measurement" href="#CONF:RES">CONF:RES</a></li> <li><b>4Q_VOLT</b> for four quadrant voltage measurements for PM102. See <a class="linkSCPI" title="Configures SCPI measurement system for 4 quadrant voltages measurement" href="#CONF:Q4V">CONF:Q4V</a></li> </ul></div>
        <div class="example">&gt;&gt; CONF?<br />&lt;&lt; VOLT</div>

      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="MEAS">
        <span class="scpi" id="CONF:ARR:">CONF:ARR:</span><span class="briefScpi">Configures power array measurement</span>
      </div>
      <div class="content">
        <div class="scpiLong">CONFigure:ARRay:[POWer] <div class="tooltip" data-id="14">&lt;samples&gt;</div>,<div class="tooltip" data-id="15">&lt;delta_t&gt;</div></div>
        <div class="paramDocu">Parameters<ul>
              <li id="14"><span class="param">samples</span><span class="paramType">uint</span> <span class="paramDesc">amount of samples to measure</span></li>
              <li id="15"><span class="param">delta_t</span><span class="paramType">uint</span> <span class="paramDesc">time in us between the samples. Needs to be >= 100</span></li>
        </ul></div>

        <div class="details">Use this command to configure the measure system for array power measurement in Watt or dBm. The command does not start the measurement. The configuration is only required once. Afterwards you control the measure system by using <a class="linkSCPI" title="Aborts any previously started measurement" href="#ABOR">ABOR</a>, <a class="linkSCPI" title="Starts a new single measurement for a previously configured unit" href="#INIT">INIT</a> and <a class="linkSCPI" title="Returns binary data block of array measure buffer." href="#FETC:ARR?">FETC:ARR?</a> finally. <br /> The array mode always stores 10000 power values in W or dBm with 10 kHz in an internal buffer. So max time resolution between the samples is 100 us. Ensure the product of delta_t / 100 * samples is always smaller or equal 10000. Also keep delta_t a multiple of 100. Normally it makes sense to disable bandwidth limitation for this measurement mode by using <a class="linkSCPI" title="Sets/Resets the the bandwidth limitation" href="#DIAG#:INP:PDI:BWID">DIAG#:INP:PDI:BWID</a>. <a class="linkSCPI" title="Sets the averaging for slow measurements" href="#SENS#:AVER">SENS#:AVER</a> is not applied for array mode. Also relative power measurements (See <a class="linkSCPI" title="Sets relative power measurement offset" href="#SENS#:POW:REF">SENS#:POW:REF</a>) are not supported in array mode.</div>
        <div class="example">&gt;&gt; CONF:ARR 10000, 100</div>
        <div class="note">Photodiode only!</div>

      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="MEAS">
        <span class="scpi" id="CONF:ARR:CURR">CONF:ARR:CURR</span><span class="briefScpi">Configures current array measurement</span>
      </div>
      <div class="content">
        <div class="scpiLong">CONFigure:ARRay:CURRent <div class="tooltip" data-id="16">&lt;samples&gt;</div>,<div class="tooltip" data-id="17">&lt;delta_t&gt;</div></div>
        <div class="paramDocu">Parameters<ul>
              <li id="16"><span class="param">samples</span><span class="paramType">uint</span> <span class="paramDesc">amount of samples to measure</span></li>
              <li id="17"><span class="param">delta_t</span><span class="paramType">uint</span> <span class="paramDesc">time in us between the samples. Needs to be >= 100</span></li>
        </ul></div>

        <div class="details">Use this command to configure the measure system for current power measurement in Ampere. The command does not start the measurement. The configuration is only required once. Afterwards you control the measure system by using <a class="linkSCPI" title="Aborts any previously started measurement" href="#ABOR">ABOR</a>, <a class="linkSCPI" title="Starts a new single measurement for a previously configured unit" href="#INIT">INIT</a> and <a class="linkSCPI" title="Returns binary data block of array measure buffer." href="#FETC:ARR?">FETC:ARR?</a> finally. <br /> The array mode always stores 10000 power values in W or dBm with 10 kHz in an internal buffer. So max time resolution between the samples is 100 us. Ensure the product of delta_t / 100 * samples is always smaller or equal 10000. Also keep delta_t a multiple of 100. Normally it makes sense to disable bandwidth limitation for this measurement mode by using <a class="linkSCPI" title="Sets/Resets the the bandwidth limitation" href="#DIAG#:INP:PDI:BWID">DIAG#:INP:PDI:BWID</a>. <a class="linkSCPI" title="Sets the averaging for slow measurements" href="#SENS#:AVER">SENS#:AVER</a> is not applied for array mode. Also relative power measurements (See <a class="linkSCPI" title="Sets relative power measurement offset" href="#SENS#:POW:REF">SENS#:POW:REF</a>) are not supported in array mode.</div>
        <div class="example">&gt;&gt; CONF:ARR 10000, 100</div>
        <div class="note">Photodiode only!</div>

      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="MEAS">
        <span class="scpi" id="CONF:ARR:PDEN">CONF:ARR:PDEN</span><span class="briefScpi">Configures power density array measurement</span>
      </div>
      <div class="content">
        <div class="scpiLong">CONFigure:ARRay:PDENsity <div class="tooltip" data-id="18">&lt;samples&gt;</div>,<div class="tooltip" data-id="19">&lt;delta_t&gt;</div></div>
        <div class="paramDocu">Parameters<ul>
              <li id="18"><span class="param">samples</span><span class="paramType">uint</span> <span class="paramDesc">amount of samples to measure</span></li>
              <li id="19"><span class="param">delta_t</span><span class="paramType">uint</span> <span class="paramDesc">time in us between the samples. Needs to be >= 100</span></li>
        </ul></div>

        <div class="details">Use this command to configure the measure system for array power density measurement in W/cm^2. The command does not start the measurement. The configuration is only required once. Afterwards you control the measure system by using <a class="linkSCPI" title="Aborts any previously started measurement" href="#ABOR">ABOR</a>, <a class="linkSCPI" title="Starts a new single measurement for a previously configured unit" href="#INIT">INIT</a> and <a class="linkSCPI" title="Returns binary data block of array measure buffer." href="#FETC:ARR?">FETC:ARR?</a> finally. <br /> The array mode always stores 10000 power values in W or dBm with 10 kHz in an internal buffer. So max time resolution between the samples is 100 us. Ensure the product of delta_t / 100 * samples is always smaller or equal 10000. Also keep delta_t a multiple of 100. Normally it makes sense to disable bandwidth limitation for this measurement mode by using <a class="linkSCPI" title="Sets/Resets the the bandwidth limitation" href="#DIAG#:INP:PDI:BWID">DIAG#:INP:PDI:BWID</a>. <a class="linkSCPI" title="Sets the averaging for slow measurements" href="#SENS#:AVER">SENS#:AVER</a> is not applied for array mode. Also relative power measurements (See <a class="linkSCPI" title="Sets relative power measurement offset" href="#SENS#:POW:REF">SENS#:POW:REF</a>) are not supported in array mode.</div>
        <div class="example">&gt;&gt; CONF:ARR 10000, 100</div>
        <div class="note">Photodiode only!</div>

      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="MEAS">
        <span class="scpi" id="CONF:FREQ">CONF:FREQ</span><span class="briefScpi">Configures SCPI measure system for energy frequency measurement</span>
      </div>
      <div class="content">
        <div class="scpiLong">CONFigure[:SCALar]:FREQuency</div>
        <div class="details">Use this command to configure the Powermeter for power frequency measurement in Hz. This command prepares the measurement only. Configure is part of the SCPI measurement system <a class="linkSCPI" title="Aborts any previously started measurement" href="#ABOR">ABOR</a>, <a class="linkSCPI" title="Configures SCPI measure system for energy frequency measurement" href="#CONF:FREQ">CONF:FREQ</a>, <a class="linkSCPI" title="Starts a new single measurement for a previously configured unit" href="#INIT">INIT</a>, <a class="linkSCPI" title="Blocks until previously initiated measurement is complete and returns result finally" href="#FETC?">FETC?</a>. If the measurement has been configured you can either call <a class="linkSCPI" title="Starts a new single measurement for a previously configured unit" href="#INIT">INIT</a> and <a class="linkSCPI" title="Blocks until previously initiated measurement is complete and returns result finally" href="#FETC?">FETC?</a> or use the shorter <a class="linkSCPI" title="Starts and queries measurement result for a previously configured unit" href="#READ?">READ?</a> to start the measurements. The Powermeter updates the frequency with 2 Hz or less if the input frequency is less. The frequency measurement accuracy depends on the input signal frequency. The Powermeter uses edge counting for input signals larger than approximately 500Hz and period measure for input signals for slower signal frequencies. Edge counting always results in a whole number frequency. If you input a CW signal frequency is read back as 0. Averaging <a class="linkSCPI" title="Sets the averaging for slow measurements" href="#SENS#:AVER">SENS#:AVER</a> is not applied for frequency measurements!</div>
        <div class="example">&gt;&gt; CONF:FREQ</div>
        <div class="note">Check different bandwidth of amplification stages for high frequencies.</div>
        <div class="info">After configuring the measurement call <a class="linkSCPI" title="Starts and queries measurement result for a previously configured unit" href="#READ?">READ?</a> or <a class="linkSCPI" title="Starts a new single measurement for a previously configured unit" href="#INIT">INIT</a> and <a class="linkSCPI" title="Blocks until previously initiated measurement is complete and returns result finally" href="#FETC?">FETC?</a> to start and query measurement results finally.</div>
      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="MEAS">
        <span class="scpi" id="CONF:PDEN">CONF:PDEN</span><span class="briefScpi">Configures SCPI measure system for power density measurement</span>
      </div>
      <div class="content">
        <div class="scpiLong">CONFigure[:SCALar]:PDENsity</div>
        <div class="details">Use this command to configure the Powermeter for photodiode, thermopile or four quadrant thermopile power density measurement in W / cm^2. This command prepares the measurement only. Configure is part of the SCPI measurement system <a class="linkSCPI" title="Aborts any previously started measurement" href="#ABOR">ABOR</a>, <a class="linkSCPI" title="Configures SCPI measure system for power density measurement" href="#CONF:PDEN">CONF:PDEN</a>, <a class="linkSCPI" title="Starts a new single measurement for a previously configured unit" href="#INIT">INIT</a>, <a class="linkSCPI" title="Blocks until previously initiated measurement is complete and returns result finally" href="#FETC?">FETC?</a>. If the measurement has been configured you can either call <a class="linkSCPI" title="Starts a new single measurement for a previously configured unit" href="#INIT">INIT</a> and <a class="linkSCPI" title="Blocks until previously initiated measurement is complete and returns result finally" href="#FETC?">FETC?</a> or use the shorter <a class="linkSCPI" title="Starts and queries measurement result for a previously configured unit" href="#READ?">READ?</a> to start the measurements. To change the density reference beam size use <a class="linkSCPI" title="Gets the beam diameter" href="#SENS#:CORR:BEAM?">SENS#:CORR:BEAM?</a> command. For four quadrant thermopile sensors this command configures measurement system for total power density.<br /> For closer details about this measurement mode read <a href="#slowMeasSystem" title="Slow Measurement Mode"> Slow measurement</a> section.</div>
        <div class="example">&gt;&gt; CONF:PDEN</div>
        <div class="note">Photodiode, Thermopile and four quadrant thermopile only!</div>
        <div class="info">After configuring the measurement call <a class="linkSCPI" title="Starts and queries measurement result for a previously configured unit" href="#READ?">READ?</a> or <a class="linkSCPI" title="Starts a new single measurement for a previously configured unit" href="#INIT">INIT</a> and <a class="linkSCPI" title="Blocks until previously initiated measurement is complete and returns result finally" href="#FETC?">FETC?</a> to start and query measurement results finally.</div>
      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="MEAS">
        <span class="scpi" id="CONF:RES">CONF:RES</span><span class="briefScpi">Configures SCPI measure system for head resistance measurement</span>
      </div>
      <div class="content">
        <div class="scpiLong">CONFigure[:SCALar]:RESistance</div>
        <div class="details">Use this command to configure the Powermeter for sensor head NTC resistance measurement in Ohm. This command prepares the measurement only. Configure is part of the SCPI measurement system <a class="linkSCPI" title="Aborts any previously started measurement" href="#ABOR">ABOR</a>, <a class="linkSCPI" title="Configures SCPI measure system for head resistance measurement" href="#CONF:RES">CONF:RES</a>, <a class="linkSCPI" title="Starts a new single measurement for a previously configured unit" href="#INIT">INIT</a>, <a class="linkSCPI" title="Blocks until previously initiated measurement is complete and returns result finally" href="#FETC?">FETC?</a>. If the measurement has been configured you can either call <a class="linkSCPI" title="Starts a new single measurement for a previously configured unit" href="#INIT">INIT</a> and <a class="linkSCPI" title="Blocks until previously initiated measurement is complete and returns result finally" href="#FETC?">FETC?</a> or use the shorter <a class="linkSCPI" title="Starts and queries measurement result for a previously configured unit" href="#READ?">READ?</a> to start the measurements. The sensor head temperature is updated at 1 Hz. Averaging <a class="linkSCPI" title="Sets the averaging for slow measurements" href="#SENS#:AVER">SENS#:AVER</a> is not applied for frequency measurements! To configure the NTC temperature measurement use <a class="linkSCPI" title="Configures SCPI measurement system for head temperature measurement" href="#CONF:TEMP">CONF:TEMP</a>.</div>
        <div class="example">&gt;&gt; CONF:RES</div>
        <div class="note">Not all sensor heads feature temperature measurement via NTC.</div>
        <div class="info">After configuring the measurement call <a class="linkSCPI" title="Starts and queries measurement result for a previously configured unit" href="#READ?">READ?</a> or <a class="linkSCPI" title="Starts a new single measurement for a previously configured unit" href="#INIT">INIT</a> and <a class="linkSCPI" title="Blocks until previously initiated measurement is complete and returns result finally" href="#FETC?">FETC?</a> to start and query measurement results finally.</div>
      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="MEAS">
        <span class="scpi" id="CONF:TEMP">CONF:TEMP</span><span class="briefScpi">Configures SCPI measurement system for head temperature measurement</span>
      </div>
      <div class="content">
        <div class="scpiLong">CONFigure[:SCALar]:TEMPerature</div>
        <div class="details">Use this command to configure the Powermeter for sensor head NTC temperature measurement in °C. This command prepares the measurement only. Configure is part of the SCPI measurement system <a class="linkSCPI" title="Aborts any previously started measurement" href="#ABOR">ABOR</a>, <a class="linkSCPI" title="Configures SCPI measurement system for head temperature measurement" href="#CONF:TEMP">CONF:TEMP</a>, <a class="linkSCPI" title="Starts a new single measurement for a previously configured unit" href="#INIT">INIT</a>, <a class="linkSCPI" title="Blocks until previously initiated measurement is complete and returns result finally" href="#FETC?">FETC?</a>. If the measurement has been configured you can either call <a class="linkSCPI" title="Starts a new single measurement for a previously configured unit" href="#INIT">INIT</a> and <a class="linkSCPI" title="Blocks until previously initiated measurement is complete and returns result finally" href="#FETC?">FETC?</a> or use the shorter <a class="linkSCPI" title="Starts and queries measurement result for a previously configured unit" href="#READ?">READ?</a> to start the measurements. The sensor head temperature is updated at 1 Hz. Averaging <a class="linkSCPI" title="Sets the averaging for slow measurements" href="#SENS#:AVER">SENS#:AVER</a> is not applied for frequency measurements! To configure the sensor head NTC resistance measurement use <a class="linkSCPI" title="Configures SCPI measure system for head resistance measurement" href="#CONF:RES">CONF:RES</a>.</div>
        <div class="example">&gt;&gt; CONF:TEMP</div>
        <div class="note">Not all sensor heads feature temperature measurement via NTC.</div>
        <div class="info">After configuring the measurement call <a class="linkSCPI" title="Starts and queries measurement result for a previously configured unit" href="#READ?">READ?</a> or <a class="linkSCPI" title="Starts a new single measurement for a previously configured unit" href="#INIT">INIT</a> and <a class="linkSCPI" title="Blocks until previously initiated measurement is complete and returns result finally" href="#FETC?">FETC?</a> to start and query measurement results finally.</div>
      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="MEAS">
        <span class="scpi" id="CONF:VOLT">CONF:VOLT</span><span class="briefScpi">Configures SCPI measure system for voltage measurement</span>
      </div>
      <div class="content">
        <div class="scpiLong">CONFigure[:SCALar]:VOLTage[:DC]</div>
        <div class="details">Use this command to configure the Powermeter for thermopile or four quadrant voltage measurement in Volt. This command prepares the measurement only. Configure is part of the SCPI measurement system <a class="linkSCPI" title="Aborts any previously started measurement" href="#ABOR">ABOR</a>, <a class="linkSCPI" title="Configures SCPI measure system for voltage measurement" href="#CONF:VOLT">CONF:VOLT</a>, <a class="linkSCPI" title="Starts a new single measurement for a previously configured unit" href="#INIT">INIT</a>, <a class="linkSCPI" title="Blocks until previously initiated measurement is complete and returns result finally" href="#FETC?">FETC?</a>. If the measurement has been configured you can either call <a class="linkSCPI" title="Starts a new single measurement for a previously configured unit" href="#INIT">INIT</a> and <a class="linkSCPI" title="Blocks until previously initiated measurement is complete and returns result finally" href="#FETC?">FETC?</a> or use the shorter <a class="linkSCPI" title="Starts and queries measurement result for a previously configured unit" href="#READ?">READ?</a> to start the measurements.<br /> For thermopile sensors and four quadrant thermopile sensors auto ranging is supported by command <a class="linkSCPI" title="Tests if voltage auto ranging is enabled" href="#SENS#:VOLT:RANG:AUTO?">SENS#:VOLT:RANG:AUTO?</a>. For thermopile sensors only you might also enable the thermopile prediction previously by using <a class="linkSCPI" title="Enables/Disables thermopile prediction" href="#INP:THER:ACC:AUTO">INP:THER:ACC:AUTO</a>.<br /> For four quadrant thermopile sensors this command configures measurement system for total voltage. For the single quadrant voltages use <a class="linkSCPI" title="Configures SCPI measurement system for 4 quadrant voltages measurement" href="#CONF:Q4V">CONF:Q4V</a> instead.<br /> For closer details about this measurement mode read <a href="#slowMeasSystem" title="Slow Measurement Mode"> Slow measurement</a> section.</div>
        <div class="example">&gt;&gt; CONF:VOLT</div>
        <div class="note">Thermopile or four quadrant thermopile only!</div>
        <div class="info">After configuring the measurement call <a class="linkSCPI" title="Starts and queries measurement result for a previously configured unit" href="#READ?">READ?</a> or <a class="linkSCPI" title="Starts a new single measurement for a previously configured unit" href="#INIT">INIT</a> and <a class="linkSCPI" title="Blocks until previously initiated measurement is complete and returns result finally" href="#FETC?">FETC?</a> to start and query measurement results finally.</div>
      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="MEAS">
        <span class="scpi" id="CONF#:CURR">CONF#:CURR</span><span class="briefScpi">Configures SCPI measure system for current measurement</span>
      </div>
      <div class="content">
        <div class="scpiLong">CONFigure<div class="tooltip" data-id="6">#</div>[:SCALar]:CURRent[:DC]</div>
        <div class="sufixDocu">Suffixe<ul>
          <li id="6"><span class="param">Sufix 1</span><span class="paramType">uint</span> <span class="paramDesc">light sensor frontend channel 1 or 2. Default is 1.</span></li>
        </ul></div>

        <div class="details">Use this command to configure the Powermeter for photodiode current measurement in Ampere. This command prepares the measurement only. Configure is part of the SCPI measurement system <a class="linkSCPI" title="Aborts any previously started measurement" href="#ABOR">ABOR</a>, <a class="linkSCPI" title="Configures SCPI measure system for current measurement" href="#CONF#:CURR">CONF#:CURR</a>, <a class="linkSCPI" title="Starts a new single measurement for a previously configured unit" href="#INIT">INIT</a>, <a class="linkSCPI" title="Blocks until previously initiated measurement is complete and returns result finally" href="#FETC?">FETC?</a>. If the measurement has been configured you can either call <a class="linkSCPI" title="Starts a new single measurement for a previously configured unit" href="#INIT">INIT</a> and <a class="linkSCPI" title="Blocks until previously initiated measurement is complete and returns result finally" href="#FETC?">FETC?</a> or use the shorter <a class="linkSCPI" title="Starts and queries measurement result for a previously configured unit" href="#READ?">READ?</a> to start the measurements.<br /> For closer details about this measurement mode read <a href="#slowMeasSystem" title="Slow Measurement Mode"> Slow measurement</a> section.</div>
        <div class="example">&gt;&gt; CONF:CURR</div>
        <div class="note">Photodiode only!</div>
        <div class="info">After configuring the measurement call <a class="linkSCPI" title="Starts and queries measurement result for a previously configured unit" href="#READ?">READ?</a> or <a class="linkSCPI" title="Starts a new single measurement for a previously configured unit" href="#INIT">INIT</a> and <a class="linkSCPI" title="Blocks until previously initiated measurement is complete and returns result finally" href="#FETC?">FETC?</a> to start and query measurement results finally.</div>
      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="MEAS">
        <span class="scpi" id="FETC?">FETC?</span><span class="briefScpi">Blocks until previously initiated measurement is complete and returns result finally</span>
      </div>
      <div class="content">
        <div class="scpiLong">FETCh? <div class="tooltip" data-id="2">&lt;offset&gt;</div></div>
        <div class="paramDocu">Parameters<ul>
           <li id="2"><span class="optParam">offset</span><span class="paramType">uint</span> <span class="paramDesc">Optional! Optional data fetch offset. Used for scope mode only.</span></li>
        </ul></div>

        <div class="resultDocu">Response<ul>
          <li><span class="param">measurement</span><span class="paramType">float</span> <span class="paramDesc">measurement result. May be INFINITY or -INFINITY if signal out of measurement range.</span></li>
        </ul></div>

        <div class="details">Fetch is part of the SCPI measurement system <a class="linkSCPI" title="Aborts any previously started measurement" href="#ABOR">ABOR</a>, <a class="linkSCPI" title="Configures SCPI measure system for power measurement" href="#CONF">CONF</a>, <a class="linkSCPI" title="Starts a new single measurement for a previously configured unit" href="#INIT">INIT</a>, <a class="linkSCPI" title="Blocks until previously initiated measurement is complete and returns result finally" href="#FETC?">FETC?</a>. Use this command block remote interface until previously initiated measurement is complete and return the result finally. The maximal query frequency is 1 kHz (averaging 1). All measurement results within the 1 millisecond period are averaged. During the measurement the remote interface will block and can not process any further SCPI requests. Call <a class="linkSCPI" title="Sets the averaging for slow measurements" href="#SENS#:AVER">SENS#:AVER</a> to change averaging. All temperature, resistance, frequency units are measured independently of the averaging. The result of this command may be absolute or relative depending on delta mode offset and enable state. For closer details read for example <a class="linkSCPI" title="Sets relative power measurement offset" href="#SENS#:POW:REF">SENS#:POW:REF</a> and <a class="linkSCPI" title="Enables power delta mode" href="#SENS#:POW:REF:STAT">SENS#:POW:REF:STAT</a>. By default delta is disabled.<br /> You may also call <a class="linkSCPI" title="Tests if there is measurement data to fetch" href="#FETC#:STAT?">FETC#:STAT?</a> or check for the DataReadyToFetch flag in the Operation Status register <a class="linkSCPI" title="Queries the SCPI Operation Condition register" href="#STAT:OPER:COND?">STAT:OPER:COND?</a> before calling <a class="linkSCPI" title="Blocks until previously initiated measurement is complete and returns result finally" href="#FETC?">FETC?</a> finally. You may query the Questionable Status register <a class="linkSCPI" title="Queries the SCPI Questionable Condition register" href="#STAT:QUES:COND?">STAT:QUES:COND?</a> so see if measurement results are valid and not infinity.<br /> Be aware result of fetch depends on measured unit. When configured for thermopile 4 quadrant voltage the result will be a quadruple of the four voltages separated by comma. You can also use this command to fetch the result of array mode in a normal text representation instead of binary format of command <a class="linkSCPI" title="Returns binary data block of array measure buffer." href="#FETC:ARR?">FETC:ARR?</a>. Querying the results in text representation is a lot slower than the binary transfers. This command may return inf or nan instead of floating point number.<br /> For closer details about this measurement mode read <a href="#slowMeasSystem" title="Slow Measurement Mode"> Slow measurement</a> section.</div>
        <div class="example">&gt;&gt; FETC?<br />&lt;&lt; 1.34324e-5 (or)<br />&lt;&lt;123, -234(Beam position)<br />&lt;&lt;5.34534e-3,3.2433e-4,1.8432e-3,5.2312e-8 (For four quadrant voltages)</div>
        <div class="note">Do not send ABOR in case of read timeout within 10 seconds.</div>
        <div class="info">Fetch is part of the SCPI measurement system command sequence: <a class="linkSCPI" title="Aborts any previously started measurement" href="#ABOR">ABOR</a>, <a class="linkSCPI" title="Configures SCPI measure system for power measurement" href="#CONF">CONF</a>, <a class="linkSCPI" title="Starts a new single measurement for a previously configured unit" href="#INIT">INIT</a>, FETC?.</div>
      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="MEAS">
        <span class="scpi" id="FETC:ARR?">FETC:ARR?</span><span class="briefScpi">Returns binary data block of array measure buffer.</span>
      </div>
      <div class="content">
        <div class="scpiLong">FETCh:ARRay? <div class="tooltip" data-id="4">&lt;offset&gt;</div>, <div class="tooltip" data-id="5">&lt;length&gt;</div></div>
        <div class="paramDocu">Parameters<ul>
              <li id="4"><span class="param">offset</span><span class="paramType">uint</span> <span class="paramDesc">start reading array measurements at this offset</span></li>
              <li id="5"><span class="param">length</span><span class="paramType">uint</span> <span class="paramDesc">amount of measurement to read at index. Max is 40</span></li>
        </ul></div>

        <div class="resultDocu">Response<ul>
          <li><span class="param">count</span><span class="paramType">uint</span> <span class="paramDesc">amount of binary tuples in result. Max is 40</span></li>
          <li><span class="param">data</span><span class="paramType">binary</span> <span class="paramDesc">list of up to 40 measurement results as floats (4 Bytes each)</span></li>
        </ul></div>

        <div class="details">Use this command to fetch the array measure results in multiple binary encoded data blocks. To retrieve the entire 10 kSamples of array measure mode the function has to be called with different offsets multiple times. The array content stays valid until the by <a class="linkSCPI" title="Starts a new single measurement for a previously configured unit" href="#INIT">INIT</a> again. You can fetch the data at any speed and order. The result starts with the amount of floats in response as printed decimal. If the result length is larger than 0, binary float array data follows after comma. The floats in the array are IEEE compliant. All floats are transferred with the following little endian byte order:<p> <svg width="1100px" viewBox="728 422 1553 117" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">  <g>    <rect style="fill: #90ee90" x="938" y="500" width="52" height="38"/>    <rect style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x="938" y="500" width="52" height="38"/>    <text font-size="12.8" style="fill: #000000;text-anchor:middle;font-family:sans-serif;font-style:normal;font-weight:normal" x="964" y="523.8">      <tspan x="964" y="523.8">0x23</tspan>    </text>  </g>  <g>    <rect style="fill: #90ee90" x="990" y="500" width="52" height="38"/>    <rect style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x="990" y="500" width="52" height="38"/>    <text font-size="12.8" style="fill: #000000;text-anchor:middle;font-family:sans-serif;font-style:normal;font-weight:normal" x="1016" y="523.8">      <tspan x="1016" y="523.8">0x54</tspan>    </text>  </g>  <g>    <rect style="fill: #90ee90" x="1042" y="500" width="52" height="38"/>    <rect style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x="1042" y="500" width="52" height="38"/>    <text font-size="12.8" style="fill: #000000;text-anchor:middle;font-family:sans-serif;font-style:normal;font-weight:normal" x="1068" y="523.8">      <tspan x="1068" y="523.8">0x1c</tspan>    </text>  </g>  <g>    <rect style="fill: #90ee90" x="1094" y="500" width="52" height="38"/>    <rect style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x="1094" y="500" width="52" height="38"/>    <text font-size="12.8" style="fill: #000000;text-anchor:middle;font-family:sans-serif;font-style:normal;font-weight:normal" x="1120" y="523.8">      <tspan x="1120" y="523.8">0x01</tspan>    </text>  </g>  <g>    <rect style="fill: #d88ff2" x="1146" y="500" width="56.05" height="38"/>    <rect style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x="1146" y="500" width="56.05" height="38"/>    <text font-size="12.8" style="fill: #000000;text-anchor:middle;font-family:sans-serif;font-style:normal;font-weight:normal" x="1174.03" y="523.8">      <tspan x="1174.03" y="523.8">0xa51</tspan>    </text>  </g>  <g>    <rect style="fill: #d88ff2" x="1200" y="500" width="52" height="38"/>    <rect style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x="1200" y="500" width="52" height="38"/>    <text font-size="12.8" style="fill: #000000;text-anchor:middle;font-family:sans-serif;font-style:normal;font-weight:normal" x="1226" y="523.8">      <tspan x="1226" y="523.8">0x4c</tspan>    </text>  </g>  <g>    <rect style="fill: #d88ff2" x="1250" y="500" width="52" height="38"/>    <rect style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x="1250" y="500" width="52" height="38"/>    <text font-size="12.8" style="fill: #000000;text-anchor:middle;font-family:sans-serif;font-style:normal;font-weight:normal" x="1276" y="523.8">      <tspan x="1276" y="523.8">0x5e</tspan>    </text>  </g>  <g>    <rect style="fill: #d88ff2" x="1302" y="500" width="52" height="38"/>    <rect style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x="1302" y="500" width="52" height="38"/>    <text font-size="12.8" style="fill: #000000;text-anchor:middle;font-family:sans-serif;font-style:normal;font-weight:normal" x="1328" y="523.8">      <tspan x="1328" y="523.8">0x39</tspan>    </text>  </g>  <g>    <rect style="fill: #ffa500" x="1354" y="500" width="52" height="38"/>    <rect style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x="1354" y="500" width="52" height="38"/>    <text font-size="12.8" style="fill: #000000;text-anchor:middle;font-family:sans-serif;font-style:normal;font-weight:normal" x="1380" y="523.8">      <tspan x="1380" y="523.8">0x73</tspan>    </text>  </g>  <g>    <rect style="fill: #ffa500" x="1406" y="500" width="52" height="38"/>    <rect style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x="1406" y="500" width="52" height="38"/>    <text font-size="12.8" style="fill: #000000;text-anchor:middle;font-family:sans-serif;font-style:normal;font-weight:normal" x="1432" y="523.8">      <tspan x="1432" y="523.8">0xd6</tspan>    </text>  </g>  <g>    <rect style="fill: #ffa500" x="1458" y="500" width="52" height="38"/>    <rect style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x="1458" y="500" width="52" height="38"/>    <text font-size="12.8" style="fill: #000000;text-anchor:middle;font-family:sans-serif;font-style:normal;font-weight:normal" x="1484" y="523.8">      <tspan x="1484" y="523.8">0xa7</tspan>    </text>  </g>  <g>    <rect style="fill: #ffa500" x="1510" y="500" width="52" height="38"/>    <rect style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x="1510" y="500" width="52" height="38"/>    <text font-size="12.8" style="fill: #000000;text-anchor:middle;font-family:sans-serif;font-style:normal;font-weight:normal" x="1536" y="523.8">      <tspan x="1536" y="523.8">0x3b</tspan>    </text>  </g>  <g>    <rect style="fill: #90ee90" x="938" y="462" width="208" height="38"/>    <rect style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x="938" y="462" width="208" height="38"/>    <text font-size="12.8" style="fill: #000000;text-anchor:middle;font-family:sans-serif;font-style:normal;font-weight:normal" x="1042" y="485.8">      <tspan x="1042" y="485.8">0x011c5423 (18633763)</tspan>    </text>  </g>  <g>    <rect style="fill: #d88ff2" x="1146" y="462" width="208" height="38"/>    <rect style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x="1146" y="462" width="208" height="38"/>    <text font-size="12.8" style="fill: #000000;text-anchor:middle;font-family:sans-serif;font-style:normal;font-weight:normal" x="1250" y="485.8">      <tspan x="1250" y="485.8">0x395e4c51 (0.000212)</tspan>    </text>  </g>  <g>    <rect style="fill: #ffa500" x="1354" y="462" width="208" height="38"/>    <rect style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x="1354" y="462" width="208" height="38"/>    <text font-size="12.8" style="fill: #000000;text-anchor:middle;font-family:sans-serif;font-style:normal;font-weight:normal" x="1458" y="485.8">      <tspan x="1458" y="485.8">0x3ba7d673 (0.005122)</tspan>    </text>  </g>  <g>    <rect style="fill: #90ee90" x="1562" y="500" width="120" height="38"/>    <rect style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x="1562" y="500" width="120" height="38"/>    <text font-size="12.8" style="fill: #000000;text-anchor:middle;font-family:sans-serif;font-style:normal;font-weight:normal" x="1622" y="523.8">      <tspan x="1622" y="523.8">4 Bytes uint32</tspan>    </text>  </g>  <g>    <rect style="fill: #d88ff2" x="1679.62" y="500" width="120.75" height="38"/>    <rect style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x="1679.62" y="500" width="120.75" height="38"/>    <text font-size="12.8" style="fill: #000000;text-anchor:middle;font-family:sans-serif;font-style:normal;font-weight:normal" x="1740" y="523.8">      <tspan x="1740" y="523.8">4 Bytes float ch 1</tspan>    </text>  </g>  <g>    <rect style="fill: #ffa500" x="1799.62" y="500" width="120.75" height="38"/>    <rect style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x="1799.62" y="500" width="120.75" height="38"/>    <text font-size="12.8" style="fill: #000000;text-anchor:middle;font-family:sans-serif;font-style:normal;font-weight:normal" x="1860" y="523.8">      <tspan x="1860" y="523.8">4 Bytes float ch 2</tspan>    </text>  </g>  <g>    <rect style="fill: #90ee90" x="1920" y="500" width="120" height="38"/>    <rect style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x="1920" y="500" width="120" height="38"/>    <text font-size="12.8" style="fill: #000000;text-anchor:middle;font-family:sans-serif;font-style:normal;font-weight:normal" x="1980" y="523.8">      <tspan x="1980" y="523.8">4 Bytes uint32</tspan>    </text>  </g>  <g>    <rect style="fill: #d88ff2" x="2039.62" y="500" width="120.75" height="38"/>    <rect style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x="2039.62" y="500" width="120.75" height="38"/>    <text font-size="12.8" style="fill: #000000;text-anchor:middle;font-family:sans-serif;font-style:normal;font-weight:normal" x="2100" y="523.8">      <tspan x="2100" y="523.8">4 Bytes float ch 1</tspan>    </text>  </g>  <g>    <rect style="fill: #ffa500" x="2159.62" y="500" width="120.75" height="38"/>    <rect style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x="2159.62" y="500" width="120.75" height="38"/>    <text font-size="12.8" style="fill: #000000;text-anchor:middle;font-family:sans-serif;font-style:normal;font-weight:normal" x="2220" y="523.8">      <tspan x="2220" y="523.8">4 Bytes float ch 2</tspan>    </text>  </g>  <g>    <rect style="fill: #75daff" x="730" y="500" width="52" height="38"/>    <rect style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x="730" y="500" width="52" height="38"/>    <text font-size="12.8" style="fill: #000000;text-anchor:middle;font-family:sans-serif;font-style:normal;font-weight:normal" x="756" y="523.8">      <tspan x="756" y="523.8">0x64</tspan>    </text>  </g>  <g>    <rect style="fill: #75daff" x="782" y="500" width="52" height="38"/>    <rect style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x="782" y="500" width="52" height="38"/>    <text font-size="12.8" style="fill: #000000;text-anchor:middle;font-family:sans-serif;font-style:normal;font-weight:normal" x="808" y="523.8">      <tspan x="808" y="523.8">0x00</tspan>    </text>  </g>  <g>    <rect style="fill: #75daff" x="730" y="462" width="208" height="38"/>    <rect style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x="730" y="462" width="208" height="38"/>    <text font-size="12.8" style="fill: #000000;text-anchor:middle;font-family:sans-serif;font-style:normal;font-weight:normal" x="834" y="485.8">      <tspan x="834" y="485.8">0x00000064(100)</tspan>    </text>  </g>  <g>    <rect style="fill: #75daff" x="834" y="500" width="52" height="38"/>    <rect style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x="834" y="500" width="52" height="38"/>    <text font-size="12.8" style="fill: #000000;text-anchor:middle;font-family:sans-serif;font-style:normal;font-weight:normal" x="860" y="523.8">      <tspan x="860" y="523.8">0x00</tspan>    </text>  </g>  <g>    <rect style="fill: #75daff" x="886" y="500" width="52" height="38"/>    <rect style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x="886" y="500" width="52" height="38"/>    <text font-size="12.8" style="fill: #000000;text-anchor:middle;font-family:sans-serif;font-style:normal;font-weight:normal" x="912" y="523.8">      <tspan x="912" y="523.8">0x00</tspan>    </text>  </g>  <g>    <rect style="fill: #75daff" x="730" y="424" width="208" height="38"/>    <rect style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x="730" y="424" width="208" height="38"/>    <text font-size="12.8" style="fill: #000000;text-anchor:middle;font-family:sans-serif;font-style:normal;font-weight:normal" x="834" y="447.8">      <tspan x="834" y="447.8">Response tuple count</tspan>    </text>  </g>  <g>    <rect style="fill: #90ee90" x="938" y="424" width="208" height="38"/>    <rect style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x="938" y="424" width="208" height="38"/>    <text font-size="12.8" style="fill: #000000;text-anchor:middle;font-family:sans-serif;font-style:normal;font-weight:normal" x="1042" y="447.8">      <tspan x="1042" y="447.8">Triplet rel time in us</tspan>    </text>  </g>  <g>    <rect style="fill: #d88ff2" x="1146" y="424" width="208" height="38"/>    <rect style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x="1146" y="424" width="208" height="38"/>    <text font-size="12.8" style="fill: #000000;text-anchor:middle;font-family:sans-serif;font-style:normal;font-weight:normal" x="1250" y="447.8">      <tspan x="1250" y="447.8">Channel 1 measurement</tspan>    </text>  </g>  <g>    <rect style="fill: #ffa500" x="1354" y="424" width="208" height="38"/>    <rect style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x="1354" y="424" width="208" height="38"/>    <text font-size="12.8" style="fill: #000000;text-anchor:middle;font-family:sans-serif;font-style:normal;font-weight:normal" x="1458" y="447.8">      <tspan x="1458" y="447.8">Channel 2 measurement</tspan>    </text>  </g></svg> </p> The following pseudo code should give you an example how to reassemble the measurement result list floats <p class="code"> val = byte[0]<br /> val = val | (byte[1] << 8)<br /> val = val | (byte[2] << 16)<br /> val = val | (byte[3] << 24)<br /> </p> This command  will wait for the internal array buffer to be filled completely before it will return. The buffer is filled at 10 kHz by the Powermeter. Because of this the command will never block longer than 1 second.</div>
        <div class="example">&gt;&gt; FETC:ARR?<br />&lt;&lt; 40, measurement result list as binary data</div>
        <div class="note">Photodiode only!</div>
        <div class="info">If you have problems parsing the binary format. You can also user <a class="linkSCPI" title="Blocks until previously initiated measurement is complete and returns result finally" href="#FETC?">FETC?</a> to query the same data in text representation. This method is simply slower.</div>
      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="MEAS">
        <span class="scpi" id="FETC#:STAT?">FETC#:STAT?</span><span class="briefScpi">Tests if there is measurement data to fetch</span>
      </div>
      <div class="content">
        <div class="scpiLong">FETCh<div class="tooltip" data-id="3">#</div>:STATe?</div>
        <div class="sufixDocu">Suffixe<ul>
          <li id="3"><span class="param">Sufix 1</span><span class="paramType">uint</span> <span class="paramDesc">light sensor frontend channel 1 or 2. Default is 1.</span></li>
        </ul></div>

        <div class="resultDocu">Response<ul>
          <li><span class="param">state</span><span class="paramType">bool</span> <span class="paramDesc">1 if data is ready to fetch. 0 if not.</span></li>
        </ul></div>

        <div class="details">Call this function to test if there is data to fetch. If the function returns 1 you can call <a class="linkSCPI" title="Blocks until previously initiated measurement is complete and returns result finally" href="#FETC?">FETC?</a> or <a class="linkSCPI" title="Returns binary data block of array measure buffer." href="#FETC:ARR?">FETC:ARR?</a> depending on measure mode. If the function returns 0 measurement system is still waiting for measure result.</div>
        <div class="example">&gt;&gt; FETC:STAT?<br />&lt;&lt; 1</div>
        <div class="info">You might check DataReadyToFetch flag in the Operation Status register <a class="linkSCPI" title="Queries the SCPI Operation Condition register" href="#STAT:OPER:COND?">STAT:OPER:COND?</a>.</div>
      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="MEAS">
        <span class="scpi" id="INIT">INIT</span><span class="briefScpi">Starts a new single measurement for a previously configured unit</span>
      </div>
      <div class="content">
        <div class="scpiLong">INITiate[:IMMediate]</div>
        <div class="details">Use this command to start a new single measurement for a previously configured unit. Fetch is part of the SCPI measurement system <a class="linkSCPI" title="Aborts any previously started measurement" href="#ABOR">ABOR</a>, <a class="linkSCPI" title="Configures SCPI measure system for power measurement" href="#CONF">CONF</a>, <a class="linkSCPI" title="Starts a new continuos measurement for a previously configured unit" href="#INIT:CONT">INIT:CONT</a>, <a class="linkSCPI" title="Blocks until previously initiated measurement is complete and returns result finally" href="#FETC?">FETC?</a>. The command requires any of the configure commands has been called previously. If the Powermeter receives an <a class="linkSCPI" title="Starts a new single measurement for a previously configured unit" href="#INIT">INIT</a> command it will start a single new measurement. The maximal measurement frequency is 1 kHz (averaging 1). All measurement results within the 1 millisecond period are averaged. Call <a class="linkSCPI" title="Sets the averaging for slow measurements" href="#SENS#:AVER">SENS#:AVER</a> to change averaging.  All temperature, resistance pulse width units are measured independently of the averaging.<br /> Use <a class="linkSCPI" title="Blocks until previously initiated measurement is complete and returns result finally" href="#FETC?">FETC?</a> to wait until the measurement has been taken and the result is ready. If you want to check if the measurement has been taken without blocking the remote interface call <a class="linkSCPI" title="Queries the SCPI Operation Condition register" href="#STAT:OPER:COND?">STAT:OPER:COND?</a> and call <a class="linkSCPI" title="Blocks until previously initiated measurement is complete and returns result finally" href="#FETC?">FETC?</a> or check the DataReadyToFetch flag before calling fetch.</div>
        <div class="example">&gt;&gt; INIT</div>
        <div class="info">Init is part of the SCPI measurement system command sequence: <a class="linkSCPI" title="Aborts any previously started measurement" href="#ABOR">ABOR</a>, <a class="linkSCPI" title="Configures SCPI measure system for power measurement" href="#CONF">CONF</a>, INIT, <a class="linkSCPI" title="Blocks until previously initiated measurement is complete and returns result finally" href="#FETC?">FETC?</a>.</div>
      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="MEAS">
        <span class="scpi" id="INIT:CONT">INIT:CONT</span><span class="briefScpi">Starts a new continuos measurement for a previously configured unit</span>
      </div>
      <div class="content">
        <div class="scpiLong">INITiate:CONTinuous</div>
        <div class="details">Use this command to start a new continuos measurement for a previously configured unit. Fetch is part of the SCPI measurement system <a class="linkSCPI" title="Aborts any previously started measurement" href="#ABOR">ABOR</a>, <a class="linkSCPI" title="Configures SCPI measure system for power measurement" href="#CONF">CONF</a>, <a class="linkSCPI" title="Starts a new continuos measurement for a previously configured unit" href="#INIT:CONT">INIT:CONT</a>, <a class="linkSCPI" title="Blocks until previously initiated measurement is complete and returns result finally" href="#FETC?">FETC?</a>. The command requires any of the configure commands has been called previously. If the Powermeter receives a <a class="linkSCPI" title="Starts a new continuos measurement for a previously configured unit" href="#INIT:CONT">INIT:CONT</a> command it will start a new continuos measurement. A continuos measurement always updates the measurement value of the remote interface. So once started you can call <a class="linkSCPI" title="Blocks until previously initiated measurement is complete and returns result finally" href="#FETC?">FETC?</a> as often as you want to get consecutive measure results. The results are not queued within the Powermeter! To stop a continuos measurement use the <a class="linkSCPI" title="Aborts any previously started measurement" href="#ABOR">ABOR</a> command.</div>
        <div class="example">&gt;&gt; INIT:CONT</div>
        <div class="info">Init is part of the SCPI measurement system command sequence: <a class="linkSCPI" title="Aborts any previously started measurement" href="#ABOR">ABOR</a>, <a class="linkSCPI" title="Configures SCPI measure system for power measurement" href="#CONF">CONF</a>, INIT:COND, <a class="linkSCPI" title="Blocks until previously initiated measurement is complete and returns result finally" href="#FETC?">FETC?</a>.</div>
      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="MEAS">
        <span class="scpi" id="MEAS?">MEAS?</span><span class="briefScpi">Measures the sensor averaged power in Watt or dBm</span>
      </div>
      <div class="content">
        <div class="scpiLong">MEASure[:SCALar][:POWer]?</div>
        <div class="resultDocu">Response<ul>
          <li><span class="param">power</span><span class="paramType">float</span> <span class="paramDesc">power in Watt or dBm. May be INFINITY or -INFINITY if signal out of measurement range</span></li>
        </ul></div>

        <div class="details">Use this command to measure an averaged power in Watt or dBm for photodiode, thermopile or four quadrant thermopile sensors. The unit depends on the power unit configuration <a class="linkSCPI" title="Selects the power unit" href="#SENS#:POW:UNIT">SENS#:POW:UNIT</a>. If the Powermeter receives a <a class="linkSCPI" title="Measures the sensor averaged power in Watt or dBm" href="#MEAS?">MEAS?</a> command it will start a new measurement and return result when enough data has been averaged. The maximal command call frequency is 1 kHz (averaging 1). All measurement results within the 1 millisecond period are averaged. The averaging might be adapted by <a class="linkSCPI" title="Sets the averaging for slow measurements" href="#SENS#:AVER">SENS#:AVER</a> command. During the measurement the remote interface will block and can not process any further SCPI requests. The result of this command may be absolute or relative depending on delta mode offset and enable state. For closer details read <a class="linkSCPI" title="Sets relative power measurement offset" href="#SENS#:POW:REF">SENS#:POW:REF</a> and <a class="linkSCPI" title="Enables power delta mode" href="#SENS#:POW:REF:STAT">SENS#:POW:REF:STAT</a>. By default delta is disabled. For thermopile sensors the accelerator might be enabled by <a class="linkSCPI" title="Enables/Disables thermopile prediction" href="#INP:THER:ACC:AUTO">INP:THER:ACC:AUTO</a>. When enabled and electrical sensor signal changed significantly, the measure command will return software calculated values for a short time. Acceleration feature does not apply for four quadrant thermopile sensors. For the four quadrant thermopile sensors the command will return the total power.<br /> For closer details about this measurement mode read <a href="#slowMeasSystem" title="Slow Measurement Mode"> Slow measurement</a> section.</div>
        <div class="example">&gt;&gt; MEAS?<br />&lt;&lt; 3.34e-5</div>
        <div class="note">Photodiode and thermopile and four quadrant thermopile only. Be careful when measuring single pulse events. Read details carefully.</div>
        <div class="info">MEAS is a convenience command for the SCPI command sequence: <a class="linkSCPI" title="Aborts any previously started measurement" href="#ABOR">ABOR</a>; <a class="linkSCPI" title="Configures SCPI measure system for power measurement" href="#CONF">CONF</a>; <a class="linkSCPI" title="Starts a new single measurement for a previously configured unit" href="#INIT">INIT</a>; <a class="linkSCPI" title="Blocks until previously initiated measurement is complete and returns result finally" href="#FETC?">FETC?</a>.</div>
      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="MEAS">
        <span class="scpi" id="MEAS:ARR?">MEAS:ARR?</span><span class="briefScpi">Measures a sequence of power values in array mode</span>
      </div>
      <div class="content">
        <div class="scpiLong">MEASure:ARRay[:POWer]? <div class="tooltip" data-id="8">&lt;samples&gt;</div>,<div class="tooltip" data-id="9">&lt;delta_t&gt;</div></div>
        <div class="paramDocu">Parameters<ul>
              <li id="8"><span class="param">samples</span><span class="paramType">uint</span> <span class="paramDesc">amount of samples to measure</span></li>
              <li id="9"><span class="param">delta_t</span><span class="paramType">uint</span> <span class="paramDesc">time in us between the samples. Needs to be >= 100</span></li>
        </ul></div>

        <div class="resultDocu">Response<ul>
          <li><span class="param">firstSample</span><span class="paramType">float</span> <span class="paramDesc">first sample of array</span></li>
        </ul></div>

        <div class="details">Starts a software triggered power measurement sequence. The array mode always stores 10000 power samples in Watt or dBm with 10 kHz in an internal buffer. So max time resolution between the samples is 100 us. Once the buffer is full the command will return the first sample. During the measurement the remote interface will block and can not process any further SCPI requests. To query the rest of samples continue to call <a class="linkSCPI" title="Blocks until previously initiated measurement is complete and returns result finally" href="#FETC?">FETC?</a> with an index. Alternatively call <a class="linkSCPI" title="Returns binary data block of array measure buffer." href="#FETC:ARR?">FETC:ARR?</a> multiple times afterwards. This SCPI command is a convenience function that includes the SCPI command sequence <a class="linkSCPI" title="Aborts any previously started measurement" href="#ABOR">ABOR</a>, <a class="linkSCPI" title="Measures a sequence of current values in array mode" href="#MEAS:ARR:CURR?">MEAS:ARR:CURR?</a>, <a class="linkSCPI" title="Starts a new single measurement for a previously configured unit" href="#INIT">INIT</a> and finally <a class="linkSCPI" title="Returns binary data block of array measure buffer." href="#FETC:ARR?">FETC:ARR?</a>. Ensure the product of delta_t / 100 * samples is always smaller or equal 10000. Normally it makes sense to disable bandwidth limitation for this measurement mode <a class="linkSCPI" title="Sets/Resets the the bandwidth limitation" href="#DIAG#:INP:PDI:BWID">DIAG#:INP:PDI:BWID</a>. <a class="linkSCPI" title="Sets the averaging for slow measurements" href="#SENS#:AVER">SENS#:AVER</a> is not applied for array mode. Also relative power measurements (See <a class="linkSCPI" title="Sets relative power measurement offset" href="#SENS#:POW:REF">SENS#:POW:REF</a>) are not supported in array mode.</div>
        <div class="example">&gt;&gt; MEAS:ARR? 10000, 100<br />&lt;&lt; 1.234e-10</div>
        <div class="note">Photodiode only!</div>

      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="MEAS">
        <span class="scpi" id="MEAS:ARR:CURR?">MEAS:ARR:CURR?</span><span class="briefScpi">Measures a sequence of current values in array mode</span>
      </div>
      <div class="content">
        <div class="scpiLong">MEASure:ARRay:CURRent? <div class="tooltip" data-id="10">&lt;samples&gt;</div>,<div class="tooltip" data-id="11">&lt;delta_t&gt;</div></div>
        <div class="paramDocu">Parameters<ul>
              <li id="10"><span class="param">samples</span><span class="paramType">uint</span> <span class="paramDesc">amount of samples to measure</span></li>
              <li id="11"><span class="param">delta_t</span><span class="paramType">uint</span> <span class="paramDesc">time in us between the samples. Needs to be >= 100</span></li>
        </ul></div>

        <div class="resultDocu">Response<ul>
          <li><span class="param">firstSample</span><span class="paramType">float</span> <span class="paramDesc">first sample of array</span></li>
        </ul></div>

        <div class="details">Starts a software triggered current measurement sequence. The array mode always stores 10000 power samples in Ampere with 10 kHz in an internal buffer. So max time resolution between the samples is 100 us. Once the buffer is full the command will return the first sample. During the measurement the remote interface will block and can not process any further SCPI requests. To query the rest of samples continue to call <a class="linkSCPI" title="Blocks until previously initiated measurement is complete and returns result finally" href="#FETC?">FETC?</a> with an index. Alternatively call <a class="linkSCPI" title="Returns binary data block of array measure buffer." href="#FETC:ARR?">FETC:ARR?</a> multiple times afterwards. This SCPI command is a convenience function that includes the SCPI command sequence <a class="linkSCPI" title="Aborts any previously started measurement" href="#ABOR">ABOR</a>, <a class="linkSCPI" title="Configures current array measurement" href="#CONF:ARR:CURR">CONF:ARR:CURR</a>, <a class="linkSCPI" title="Starts a new single measurement for a previously configured unit" href="#INIT">INIT</a> and finally <a class="linkSCPI" title="Returns binary data block of array measure buffer." href="#FETC:ARR?">FETC:ARR?</a>. Ensure the product of delta_t / 100 * samples is always smaller or equal 10000. Also keep delta_t a multiple of 100. Normally it makes sense to disable bandwidth limitation for this measurement mode by using <a class="linkSCPI" title="Sets/Resets the the bandwidth limitation" href="#DIAG#:INP:PDI:BWID">DIAG#:INP:PDI:BWID</a>. <a class="linkSCPI" title="Sets the averaging for slow measurements" href="#SENS#:AVER">SENS#:AVER</a> is not applied for array mode. Also relative current measurements (See <a class="linkSCPI" title="Sets relative current measurement offset" href="#SENS#:CURR:REF">SENS#:CURR:REF</a>) are not supported in array mode.</div>
        <div class="example">&gt;&gt; MEAS:ARR:CURR? 10000, 100<br />&lt;&lt; 1.234e-10</div>
        <div class="note">Photodiode only!</div>

      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="MEAS">
        <span class="scpi" id="MEAS:ARR:PDEN?">MEAS:ARR:PDEN?</span><span class="briefScpi">Measures a sequence of power density values in array mode</span>
      </div>
      <div class="content">
        <div class="scpiLong">MEASure:ARRay:PDENsity? <div class="tooltip" data-id="12">&lt;samples&gt;</div>,<div class="tooltip" data-id="13">&lt;delta_t&gt;</div></div>
        <div class="paramDocu">Parameters<ul>
              <li id="12"><span class="param">samples</span><span class="paramType">uint</span> <span class="paramDesc">amount of samples to measure</span></li>
              <li id="13"><span class="param">delta_t</span><span class="paramType">uint</span> <span class="paramDesc">time in us between the samples. Needs to be >= 100</span></li>
        </ul></div>

        <div class="resultDocu">Response<ul>
          <li><span class="param">firstSample</span><span class="paramType">float</span> <span class="paramDesc">first sample of array</span></li>
        </ul></div>

        <div class="details">Starts a software triggered power density measurement sequence. The array mode always stores 10000 power density samples in W/cm^2 with 10 kHz in an internal buffer. So max time resolution between the samples is 100 us. To change the density reference beam size use <a class="linkSCPI" title="Gets the beam diameter" href="#SENS#:CORR:BEAM?">SENS#:CORR:BEAM?</a> command. Once the buffer is full the command will return the first sample. During the measurement the remote interface will block and can not process any further SCPI requests. To query the rest of samples continue to call <a class="linkSCPI" title="Blocks until previously initiated measurement is complete and returns result finally" href="#FETC?">FETC?</a> with an index. Alternatively call <a class="linkSCPI" title="Returns binary data block of array measure buffer." href="#FETC:ARR?">FETC:ARR?</a> multiple times afterwards. This SCPI command is a convenience function that includes the SCPI command sequence <a class="linkSCPI" title="Aborts any previously started measurement" href="#ABOR">ABOR</a>, <a class="linkSCPI" title="Configures power density array measurement" href="#CONF:ARR:PDEN">CONF:ARR:PDEN</a>, <a class="linkSCPI" title="Starts a new single measurement for a previously configured unit" href="#INIT">INIT</a> and finally <a class="linkSCPI" title="Returns binary data block of array measure buffer." href="#FETC:ARR?">FETC:ARR?</a>. Ensure the product of delta_t / 100 * samples is always smaller or equal 10000. Also keep delta_t a multiple of 100. Normally it makes sense to disable bandwidth limitation for this measurement mode by using <a class="linkSCPI" title="Sets/Resets the the bandwidth limitation" href="#DIAG#:INP:PDI:BWID">DIAG#:INP:PDI:BWID</a>. <a class="linkSCPI" title="Sets the averaging for slow measurements" href="#SENS#:AVER">SENS#:AVER</a> is not applied for array mode.</div>
        <div class="example">&gt;&gt; MEAS:ARR:PDEN? 1000, 1000<br />&lt;&lt; 1.234e-10</div>
        <div class="note">Photodiode only!</div>

      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="MEAS">
        <span class="scpi" id="MEAS:CURR?">MEAS:CURR?</span><span class="briefScpi">Measures the sensor averaged current</span>
      </div>
      <div class="content">
        <div class="scpiLong">MEASure[:SCALar]:CURRent[:DC]?</div>
        <div class="resultDocu">Response<ul>
          <li><span class="param">current</span><span class="paramType">float</span> <span class="paramDesc">current in Ampere. May be INFINITY or -INFINITY if signal out of measurement range</span></li>
        </ul></div>

        <div class="details">Use this command to measure an averaged current in Ampere for photodiode sensors. If the Powermeter receives a <a class="linkSCPI" title="Measures the sensor averaged current" href="#MEAS:CURR?">MEAS:CURR?</a> command, it will start a new measurement and return result when enough data has been averaged. The maximal command call frequency is 1 kHz (averaging 1). All measurement results within the 1 millisecond period are averaged. The averaging might be adapted by <a class="linkSCPI" title="Sets the averaging for slow measurements" href="#SENS#:AVER">SENS#:AVER</a> command. During the measurement the remote interface will block and can not process any further SCPI requests. The result of this command may be absolute or relative depending on delta mode offset and enable state. For closer details read <a class="linkSCPI" title="Sets relative power measurement offset" href="#SENS#:POW:REF">SENS#:POW:REF</a> and <a class="linkSCPI" title="Enables power delta mode" href="#SENS#:POW:REF:STAT">SENS#:POW:REF:STAT</a>. By default delta is disabled.<br /> For closer details about this measurement mode read <a href="#slowMeasSystem" title="Slow Measurement Mode"> Slow measurement</a> section.</div>
        <div class="example">&gt;&gt; MEAS:CURR?<br />&lt;&lt; 3.34e-5</div>
        <div class="note">Photodiode only. Be careful when measuring single pulse events. Read details carefully.</div>
        <div class="info">MEAS:CURR? is a convenience command for the SCPI command sequence: <a class="linkSCPI" title="Aborts any previously started measurement" href="#ABOR">ABOR</a>; <a class="linkSCPI" title="Configures SCPI measure system for current measurement" href="#CONF#:CURR">CONF#:CURR</a>; <a class="linkSCPI" title="Starts a new single measurement for a previously configured unit" href="#INIT">INIT</a>; <a class="linkSCPI" title="Blocks until previously initiated measurement is complete and returns result finally" href="#FETC?">FETC?</a>.</div>
      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="MEAS">
        <span class="scpi" id="MEAS:FREQ?">MEAS:FREQ?</span><span class="briefScpi">Measures the modulated sensor signal frequency in Hz</span>
      </div>
      <div class="content">
        <div class="scpiLong">MEASure[:SCALar]:FREQuency?</div>
        <div class="resultDocu">Response<ul>
          <li><span class="param">frequ</span><span class="paramType">float</span> <span class="paramDesc">frquency in Hz. Might be 0 Hz for CW signals</span></li>
        </ul></div>

        <div class="details">Use this command to measure the modulation frequency of the input signal in Hz. The Powermeter updates the frequency with 2 Hz or less if the input frequency is less. The frequency measurement  accuracy depends on the input signal frequency. The Powermeter uses edge counting for input signals larger than approximately 500Hz and period measure for input signals for slower signal frequencies. Edge counting always results in a whole number frequency. If you input a CW signal frequency is read back as 0. Averaging <a class="linkSCPI" title="Sets the averaging for slow measurements" href="#SENS#:AVER">SENS#:AVER</a> is not applied for frequency measurements!</div>
        <div class="example">&gt;&gt; MEAS:FREQ?<br />&lt;&lt; 423.32</div>
        <div class="note">Check different bandwidth of amplification stages for high frequencies.</div>
        <div class="info">MEAS:FREQ? is a convenience command for the SCPI command sequence: <a class="linkSCPI" title="Aborts any previously started measurement" href="#ABOR">ABOR</a>; <a class="linkSCPI" title="Configures SCPI measure system for energy frequency measurement" href="#CONF:FREQ">CONF:FREQ</a>; <a class="linkSCPI" title="Starts a new single measurement for a previously configured unit" href="#INIT">INIT</a>; <a class="linkSCPI" title="Blocks until previously initiated measurement is complete and returns result finally" href="#FETC?">FETC?</a>.</div>
      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="MEAS">
        <span class="scpi" id="MEAS:RES?">MEAS:RES?</span><span class="briefScpi">Measures sensor head NTC resistance</span>
      </div>
      <div class="content">
        <div class="scpiLong">MEASure[:SCALar]:RESistance?</div>
        <div class="resultDocu">Response<ul>
          <li><span class="param">res</span><span class="paramType">float</span> <span class="paramDesc">NTC resistance in Ohm</span></li>
        </ul></div>

        <div class="details">Use this command to measure the sensor head NTC resistance in Ohm. The value is updated at 1 Hz. Averaging <a class="linkSCPI" title="Sets the averaging for slow measurements" href="#SENS#:AVER">SENS#:AVER</a> is not applied for resistance measurements! To measure the NTC temperature directly use <a class="linkSCPI" title="Measures sensor head temperature in °C" href="#MEAS:TEMP?">MEAS:TEMP?</a>. If you want to measure the external NTC resistance you have to use the command <a class="linkSCPI" title="Returns the resistance of external NTC" href="#SENS#:RES:DATA?">SENS#:RES:DATA?</a>.</div>
        <div class="example">&gt;&gt; MEAS:RES?<br />&lt;&lt; 3434.33</div>
        <div class="note">Not all sensor heads feature temperature measurement via NTC.</div>
        <div class="info">MEAS:RES? is a convenience command for the SCPI command sequence: <a class="linkSCPI" title="Aborts any previously started measurement" href="#ABOR">ABOR</a>; <a class="linkSCPI" title="Configures SCPI measure system for head resistance measurement" href="#CONF:RES">CONF:RES</a>; <a class="linkSCPI" title="Starts a new single measurement for a previously configured unit" href="#INIT">INIT</a>; <a class="linkSCPI" title="Blocks until previously initiated measurement is complete and returns result finally" href="#FETC?">FETC?</a>.</div>
      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="MEAS">
        <span class="scpi" id="MEAS:TEMP?">MEAS:TEMP?</span><span class="briefScpi">Measures sensor head temperature in °C</span>
      </div>
      <div class="content">
        <div class="scpiLong">MEASure[:SCALar]:TEMPerature?</div>
        <div class="resultDocu">Response<ul>
          <li><span class="param">temperature</span><span class="paramType">float</span> <span class="paramDesc">temperature in °C</span></li>
        </ul></div>

        <div class="details">Use this command to measure the sensor head temperature in °C. The sensor head temperature is updated at 1 Hz. Averaging <a class="linkSCPI" title="Sets the averaging for slow measurements" href="#SENS#:AVER">SENS#:AVER</a> is not applied for head temperature measurements! To measure the NTC resistance use <a class="linkSCPI" title="Measures sensor head NTC resistance" href="#MEAS:RES?">MEAS:RES?</a>. If you want to measure the external NTC temperature you have to use the command <a class="linkSCPI" title="Returns the temperature of external NTC sensor" href="#SENS#:TEMP:DATA?">SENS#:TEMP:DATA?</a>.</div>
        <div class="example">&gt;&gt; MEAS:FREQ?<br />&lt;&lt; 34.55</div>
        <div class="note">Not all sensor heads feature temperature measurement.</div>
        <div class="info">MEAS:TEMP? is a convenience command for the SCPI command sequence: <a class="linkSCPI" title="Aborts any previously started measurement" href="#ABOR">ABOR</a>; <a class="linkSCPI" title="Configures SCPI measurement system for head temperature measurement" href="#CONF:TEMP">CONF:TEMP</a>; <a class="linkSCPI" title="Starts a new single measurement for a previously configured unit" href="#INIT">INIT</a>; <a class="linkSCPI" title="Blocks until previously initiated measurement is complete and returns result finally" href="#FETC?">FETC?</a>.</div>
      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="MEAS">
        <span class="scpi" id="MEAS:VOLT?">MEAS:VOLT?</span><span class="briefScpi">Measures the sensor averaged voltage</span>
      </div>
      <div class="content">
        <div class="scpiLong">MEASure[:SCALar]:VOLTage[:DC]?</div>
        <div class="resultDocu">Response<ul>
          <li><span class="param">voltage</span><span class="paramType">float</span> <span class="paramDesc">voltage in Volt. May be INFINITY or -INFINITY if signal out of measurement range</span></li>
        </ul></div>

        <div class="details">Use this command to measure an averaged voltage in Volt for  thermopile or 4 quadrant thermopile sensors. The execution of this measure command depends on the connected sensor type.<br /> For thermopile and four quadrant thermopile sensors the command it will start a new measurement and return result when enough data has been averaged. The maximal command call frequency is 1 kHz. (averaging 1). All measurement results within the 1 millisecond period are averaged. The averaging might be adapted by <a class="linkSCPI" title="Sets the averaging for slow measurements" href="#SENS#:AVER">SENS#:AVER</a> command. For thermopile sensors the accelerator might be enabled by <a class="linkSCPI" title="Enables/Disables thermopile prediction" href="#INP:THER:ACC:AUTO">INP:THER:ACC:AUTO</a>. When enabled and electrical sensor signal changed significantly, the measure command will return software calculated values for a short time. Acceleration feature does not apply for four quadrant thermopile sensors. For the four quadrant thermopile sensors the command will return the total voltage. Use <a class="linkSCPI" title="Measures the single voltages of four qudrant thermopile sensors in V" href="#MEAS:Q4V?">MEAS:Q4V?</a> to query the single quadrant voltages.<br /> You may also call <a class="linkSCPI" title="Starts a new single measurement for a previously configured unit" href="#INIT">INIT</a> to start a new measurement, call <a class="linkSCPI" title="Tests if there is measurement data to fetch" href="#FETC#:STAT?">FETC#:STAT?</a> or check for the DataReadyToFetch flag in the Operation Status register <a class="linkSCPI" title="Queries the SCPI Operation Condition register" href="#STAT:OPER:COND?">STAT:OPER:COND?</a> before calling <a class="linkSCPI" title="Blocks until previously initiated measurement is complete and returns result finally" href="#FETC?">FETC?</a> finally. You may query the Questionable Status register <a class="linkSCPI" title="Queries the SCPI Questionable Condition register" href="#STAT:QUES:COND?">STAT:QUES:COND?</a> so see if measurement results are valid and not infinity.<br /> For both sensors types the remote interface will block and can not process any further SCPI requests until measurement result has been taken. The result of this command may be absolute or relative depending on delta mode offset and enable state. For closer details read <a class="linkSCPI" title="Sets relative voltage measurement offset" href="#SENS#:VOLT:REF">SENS#:VOLT:REF</a> and <a class="linkSCPI" title="Enables voltage delta mode" href="#SENS#:VOLT:REF:STAT">SENS#:VOLT:REF:STAT</a>. By default delta is disabled.<br /> For closer details about this measurement mode read <a href="#slowMeasSystem" title="Slow Measurement Mode"> Slow measurement</a> section.</div>
        <div class="example">&gt;&gt; MEAS:VOLT?<br />&lt;&lt; 3.34e-5</div>
        <div class="note">Thermopile and four quadrant thermopile only. Be careful when measuring single pulse events. Read details carefully.</div>
        <div class="info">MEAS:VOLT? is a convenience command for the SCPI command sequence: <a class="linkSCPI" title="Aborts any previously started measurement" href="#ABOR">ABOR</a>; <a class="linkSCPI" title="Configures SCPI measure system for voltage measurement" href="#CONF:VOLT">CONF:VOLT</a>; <a class="linkSCPI" title="Starts a new single measurement for a previously configured unit" href="#INIT">INIT</a>; <a class="linkSCPI" title="Blocks until previously initiated measurement is complete and returns result finally" href="#FETC?">FETC?</a>.</div>
      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="MEAS">
        <span class="scpi" id="MEAS#:PDEN?">MEAS#:PDEN?</span><span class="briefScpi">Measures the sensor averaged power density</span>
      </div>
      <div class="content">
        <div class="scpiLong">MEASure<div class="tooltip" data-id="7">#</div>[:SCALar]:PDENsity?</div>
        <div class="sufixDocu">Suffixe<ul>
          <li id="7"><span class="param">Sufix 1</span><span class="paramType">uint</span> <span class="paramDesc">light sensor frontend channel 1 or 2. Default is 1.</span></li>
        </ul></div>

        <div class="resultDocu">Response<ul>
          <li><span class="param">density</span><span class="paramType">float</span> <span class="paramDesc">energy density in Watt per square cm. May be INFINITY or -INFINITY if signal out of measurement range</span></li>
        </ul></div>

        <div class="details">Use this command to measure an averaged power density in Watt per square cm for photodiodes, thermopile or four quadrant thermopile sensors. If the Powermeter receives a <a class="linkSCPI" title="Measures the sensor averaged power density" href="#MEAS#:PDEN?">MEAS#:PDEN?</a> command it will start a new measurement and return result when enough data has been averaged. The maximal command call frequency is 1 kHz (averaging 1). All measurement results within the 1 millisecond period are averaged. To change the density reference beam size use <a class="linkSCPI" title="Gets the beam diameter" href="#SENS#:CORR:BEAM?">SENS#:CORR:BEAM?</a> command. The averaging might be adapted by <a class="linkSCPI" title="Sets the averaging for slow measurements" href="#SENS#:AVER">SENS#:AVER</a> command. During the measurement the remote interface will block and can not process any further SCPI requests. The result of this command may be absolute or relative depending on delta mode offset and enable state. For closer details read <a class="linkSCPI" title="Sets relative power measurement offset" href="#SENS#:POW:REF">SENS#:POW:REF</a> and <a class="linkSCPI" title="Enables power delta mode" href="#SENS#:POW:REF:STAT">SENS#:POW:REF:STAT</a>. By default delta is disabled. For thermopile sensors the accelerator might be enabled by <a class="linkSCPI" title="Enables/Disables thermopile prediction" href="#INP:THER:ACC:AUTO">INP:THER:ACC:AUTO</a>. When enabled and electrical sensor signal changed significantly, the measure command will return software calculated values for a short time. Acceleration feature does not apply for four quadrant thermopile sensors. For the four quadrant thermopile sensors the command will return the total power density.<br /> For closer details about this measurement mode read <a href="#slowMeasSystem" title="Slow Measurement Mode"> Slow measurement</a> section.</div>
        <div class="example">&gt;&gt; MEAS:PDEN?<br />&lt;&lt; 3.34e-5</div>
        <div class="note">Photodiode and thermopile and four quadrant thermopile only. Be careful when measuring single pulse events. Read details carefully.</div>
        <div class="info">MEAS:PDEN? is a convenience command for the SCPI command sequence: <a class="linkSCPI" title="Aborts any previously started measurement" href="#ABOR">ABOR</a>; <a class="linkSCPI" title="Configures SCPI measure system for power density measurement" href="#CONF:PDEN">CONF:PDEN</a>; <a class="linkSCPI" title="Starts a new single measurement for a previously configured unit" href="#INIT">INIT</a>; <a class="linkSCPI" title="Blocks until previously initiated measurement is complete and returns result finally" href="#FETC?">FETC?</a>.</div>
      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="MEAS">
        <span class="scpi" id="READ?">READ?</span><span class="briefScpi">Starts and queries measurement result for a previously configured unit</span>
      </div>
      <div class="content">
        <div class="scpiLong">READ?</div>
        <div class="resultDocu">Response<ul>
          <li><span class="param">measurement</span><span class="paramType">float</span> <span class="paramDesc">measurement result. May be INFINITY or -INFINITY if signal out of measurement range</span></li>
        </ul></div>

        <div class="details">Use this command to start and query measure results of the previously configured unit. The command requires any of the configure commands have been called previously. If the Powermeter receives a <a class="linkSCPI" title="Starts and queries measurement result for a previously configured unit" href="#READ?">READ?</a> command it will start a new measurement and return result when enough data has been averaged. The maximal frequency is 1 kHz (averaging 1). All measurement results within the 1 millisecond period are averaged. During the measurement the remote interface will block and can not process any further SCPI requests. The result of this command may be absolute or relative depending on delta mode offset and enable state. For closer details read for example <a class="linkSCPI" title="Enables voltage delta mode" href="#SENS#:VOLT:REF:STAT">SENS#:VOLT:REF:STAT</a> and <a class="linkSCPI" title="Enables voltage delta mode" href="#SENS#:VOLT:REF:STAT">SENS#:VOLT:REF:STAT</a>. By default delta is disabled.</div>
        <div class="example">&gt;&gt; READ?<br />&lt;&lt; 1.34324e-5</div>
        <div class="info">Read is a convenience command for the SCPI command sequence: <a class="linkSCPI" title="Aborts any previously started measurement" href="#ABOR">ABOR</a>; <a class="linkSCPI" title="Starts a new single measurement for a previously configured unit" href="#INIT">INIT</a> and <a class="linkSCPI" title="Blocks until previously initiated measurement is complete and returns result finally" href="#FETC?">FETC?</a>.</div>
      </div>
    </div>
    <hr>
    <p id="SENS"><a href="#SENS#:AVER" data-subsystem="SENS" class="unfoldSection">Unfold all SENS Commands</a></p>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="SENS">
        <span class="scpi" id="SENS#:AVER">SENS#:AVER</span><span class="briefScpi">Sets the averaging for slow measurements</span>
      </div>
      <div class="content">
        <div class="scpiLong">SENSe<div class="tooltip" data-id="131">#</div>:AVERage[:COUNt] <div class="tooltip" data-id="132">&lt;pres&gt;</div></div>
        <div class="sufixDocu">Suffixe<ul>
          <li id="131"><span class="param">Sufix 1</span><span class="paramType">uint</span> <span class="paramDesc">channel the sensor head is connected to. Here always 1 (default).</span></li>
        </ul></div>

        <div class="paramDocu">Parameters<ul>
              <li id="132"><span class="param">pres</span><span class="paramType">uint</span> <span class="paramDesc">whole number prescaler value starting with 1.</span></li>
        </ul></div>

        <div class="details">This command sets the averaging prescaler for slow light signal related measurements like <a class="linkSCPI" title="Measures the sensor averaged power in Watt or dBm" href="#MEAS?">MEAS?</a>, <a class="linkSCPI" title="Measures the sensor averaged current" href="#MEAS:CURR?">MEAS:CURR?</a>, <a class="linkSCPI" title="Measures the sensor averaged power density" href="#MEAS#:PDEN?">MEAS#:PDEN?</a>. The whole number allows slowing down the measurement rate from max 1kHz with prescaler set to 1. When prescaler is set to 2, the Powermeter will always average 2 slow measurements and end in a 500Hz update rate. Other units of the MEAS system like <a class="linkSCPI" title="Measures the modulated sensor signal frequency in Hz" href="#MEAS:FREQ?">MEAS:FREQ?</a>, <a class="linkSCPI" title="Measures sensor head temperature in °C" href="#MEAS:TEMP?">MEAS:TEMP?</a> are not averaged with the given prescaler. There is no prescaler for the fast measurement stream! Prescaler is only available for Photodiode in CW measure mode.</div>
        <div class="example">&gt;&gt; SENS:AVER 10</div>
        <div class="note">Photodiode in CW mode only.</div>
        <div class="info">Averaging is only applied to light measurements like Watt, Ampere, dBm.</div>
      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="SENS">
        <span class="scpi" id="SENS#:AVER?">SENS#:AVER?</span><span class="briefScpi">Gets the averaging for slow measurements</span>
      </div>
      <div class="content">
        <div class="scpiLong">SENSe<div class="tooltip" data-id="133">#</div>:AVERage[:COUNt]?</div>
        <div class="sufixDocu">Suffixe<ul>
          <li id="133"><span class="param">Sufix 1</span><span class="paramType">uint</span> <span class="paramDesc">channel the sensor head is connected to. Here always 1 (default).</span></li>
        </ul></div>

        <div class="resultDocu">Response<ul>
          <li><span class="param">pres</span><span class="paramType">uint</span> <span class="paramDesc">whole number prescaler value starting with 1.</span></li>
        </ul></div>

        <div class="details">This command gets the averaging prescaler for slow light signal related measurements. For closer details read <a class="linkSCPI" title="Sets the averaging for slow measurements" href="#SENS#:AVER">SENS#:AVER</a>.</div>
        <div class="example">&gt;&gt; SENS:AVER?<br />&lt;&lt; 10</div>
        <div class="note">Photodiode in CW mode only.</div>
        <div class="info">Averaging is only applied to light measurements like Watt, Ampere.</div>
      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="SENS">
        <span class="scpi" id="SENS#:CORR">SENS#:CORR</span><span class="briefScpi">Sets the light attenuation</span>
      </div>
      <div class="content">
        <div class="scpiLong">SENSe<div class="tooltip" data-id="63">#</div>:CORRection[:LOSS[:INPut[:MAGNitude]]] <div class="tooltip" data-id="64">&lt;attenuation&gt;</div></div>
        <div class="sufixDocu">Suffixe<ul>
          <li id="63"><span class="param">Sufix 1</span><span class="paramType">uint</span> <span class="paramDesc">channel the sensor head is connected to. Here always 1 (default).</span></li>
        </ul></div>

        <div class="paramDocu">Parameters<ul>
              <li id="64"><span class="param">attenuation</span><span class="paramType">float,MIN,DEF,MAX</span> <span class="paramDesc">attenuation in dBm. MIN, DEF or MAX to set special values.</span></li>
        </ul></div>

        <div class="details">Use this command to set attenuation of light in dBm. If your setup uses a filter in front of the sensor this attenuation ensures Powermeter displays corrected values. Attenuation parameter is stored persistently and restored after reboot. If connected sensor is changed the parameter is reset to 0.</div>
        <div class="example">&gt;&gt; SENS:CORR 0.1</div>

      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="SENS">
        <span class="scpi" id="SENS#:CORR?">SENS#:CORR?</span><span class="briefScpi">Gets the light attenuation</span>
      </div>
      <div class="content">
        <div class="scpiLong">SENSe<div class="tooltip" data-id="61">#</div>:CORRection[:LOSS[:INPut[:MAGNitude]]]? <div class="tooltip" data-id="62">&lt;special&gt;</div></div>
        <div class="sufixDocu">Suffixe<ul>
          <li id="61"><span class="param">Sufix 1</span><span class="paramType">uint</span> <span class="paramDesc">channel the sensor head is connected to. Here always 1 (default).</span></li>
        </ul></div>

        <div class="paramDocu">Parameters<ul>
           <li id="62"><span class="optParam">special</span><span class="paramType">MIN,MAX</span> <span class="paramDesc">Optional! nothing to query actual value. MIN or MAX to query special attenuation values.</span></li>
        </ul></div>

        <div class="resultDocu">Response<ul>
          <li><span class="param">attenuation</span><span class="paramType">float</span> <span class="paramDesc">attenuation in dBm</span></li>
        </ul></div>

        <div class="details">Use this command to query attenuation of light in dBm. If your setup uses a filter in front of the sensor this attenuation ensures Powermeter displays corrected values. Attenuation parameter is stored persistently and restored after reboot. If connected sensor is changed the parameter is reset to 0.</div>
        <div class="example">&gt;&gt; SENS:CORR?<br />&lt;&lt; 0.1</div>

      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="SENS">
        <span class="scpi" id="SENS#:CORR:BEAM">SENS#:CORR:BEAM</span><span class="briefScpi">Sets the beam diameter</span>
      </div>
      <div class="content">
        <div class="scpiLong">SENSe<div class="tooltip" data-id="129">#</div>:CORRection:BEAMdiameter <div class="tooltip" data-id="130">&lt;diam&gt;</div></div>
        <div class="sufixDocu">Suffixe<ul>
          <li id="129"><span class="param">Sufix 1</span><span class="paramType">uint</span> <span class="paramDesc">channel the sensor head is connected to. Here always 1 (default).</span></li>
        </ul></div>

        <div class="paramDocu">Parameters<ul>
              <li id="130"><span class="param">diam</span><span class="paramType">float,MIN,DEF,MAX</span> <span class="paramDesc">circular beam diameter in mm. MIN, DEF or MAX to set special values.</span></li>
        </ul></div>

        <div class="details">Use this command to set the circular beam diameter in mm. The beam diameter is used to calculate power and energy density values. If you have other beam shapes you have to calculate the matching diameter for a circle with the same area manually. Beam diameter is stored persistently and restored after reboot. If connected sensor type gets changed, defaults are applied. If the sensor type stays same the parameter gets coerced automatically.</div>
        <div class="example">&gt;&gt; SENS:BEAM 1.5</div>
        <div class="note">In your setup ensure the beam hits the entire sensor area. Parameter range depend on connected sensor aperture.</div>

      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="SENS">
        <span class="scpi" id="SENS#:CORR:BEAM?">SENS#:CORR:BEAM?</span><span class="briefScpi">Gets the beam diameter</span>
      </div>
      <div class="content">
        <div class="scpiLong">SENSe<div class="tooltip" data-id="127">#</div>:CORRection:BEAMdiameter? <div class="tooltip" data-id="128">&lt;special&gt;</div></div>
        <div class="sufixDocu">Suffixe<ul>
          <li id="127"><span class="param">Sufix 1</span><span class="paramType">uint</span> <span class="paramDesc">channel the sensor head is connected to. Here always 1 (default).</span></li>
        </ul></div>

        <div class="paramDocu">Parameters<ul>
           <li id="128"><span class="optParam">special</span><span class="paramType">MIN,MAX</span> <span class="paramDesc">Optional! nothing to query actual value. MIN,DEF or MAX to query special beam diameter values</span></li>
        </ul></div>

        <div class="resultDocu">Response<ul>
          <li><span class="param">beamdiameter</span><span class="paramType">float</span> <span class="paramDesc">circular beam diameter in mm.</span></li>
        </ul></div>

        <div class="details">Use this command to get the circular beam diameter in mm. The beam diameter is used to calculate power and energy density values.  If you have other beam shapes you have to calculate the matching diameter for a circle with the same area manually. Beam diameter is stored persistently and restored after reboot.</div>
        <div class="example">&gt;&gt; SENS:BEAM?<br />&lt;&lt; 1.5</div>
        <div class="note">In your setup ensure the beam hits the entire sensor area. Parameter range depend on connected sensor aperture.</div>

      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="SENS">
        <span class="scpi" id="SENS#:CORR:COEF:BETA">SENS#:CORR:COEF:BETA</span><span class="briefScpi">Sets external thermistor equation Beta parameter</span>
      </div>
      <div class="content">
        <div class="scpiLong">SENSe<div class="tooltip" data-id="97">#</div>:CORRection:COEFficient:BETA <div class="tooltip" data-id="98">&lt;beta&gt;</div></div>
        <div class="sufixDocu">Suffixe<ul>
          <li id="97"><span class="param">Sufix 1</span><span class="paramType">uint</span> <span class="paramDesc">external NTC channel number 5 for NTC1 or 6 for NTC2. Default is 5.</span></li>
        </ul></div>

        <div class="paramDocu">Parameters<ul>
              <li id="98"><span class="param">beta</span><span class="paramType">float,MIN,MAX,AUTO</span> <span class="paramDesc">Beta value. MIN, DEF or MAX for special parameters.</span></li>
        </ul></div>

        <div class="details">Use this command to set the Beta parameter being part of the external NTC temperature calculation. To parameter value depends on the used thermistor. A wrong parameter value results in a wrong temperature measurement. To get the temperature measurement result use <a class="linkSCPI" title="Returns the temperature of external NTC sensor" href="#SENS#:TEMP:DATA?">SENS#:TEMP:DATA?</a>.</div>
        <div class="example">&gt;&gt; SENS:CORR:COEF:BETA 3988</div>

      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="SENS">
        <span class="scpi" id="SENS#:CORR:COEF:BETA?">SENS#:CORR:COEF:BETA?</span><span class="briefScpi">Sets external thermistor equation Beta parameter</span>
      </div>
      <div class="content">
        <div class="scpiLong">SENSe<div class="tooltip" data-id="95">#</div>:CORRection:COEFficient:BETA?  <div class="tooltip" data-id="96">&lt;special&gt;</div></div>
        <div class="sufixDocu">Suffixe<ul>
          <li id="95"><span class="param">Sufix 1</span><span class="paramType">uint</span> <span class="paramDesc">external NTC channel number 5 for NTC1 or 6 for NTC2. Default is 5.</span></li>
        </ul></div>

        <div class="paramDocu">Parameters<ul>
           <li id="96"><span class="optParam">special</span><span class="paramType">MIN,MAX,AUTO</span> <span class="paramDesc">Optional! Nothing to query actual value. MIN, MAX or DEF for special values.</span></li>
        </ul></div>

        <div class="resultDocu">Response<ul>
          <li><span class="param">beta</span><span class="paramType">float</span> <span class="paramDesc">thermistor beat parameter.</span></li>
        </ul></div>

        <div class="details">Use this command to set the Beta parameter being part of the external NTC temperature calculation. The parameter value depends on the used thermistor. A wrong parameter value results in a wrong temperature measurement. To get the temperature measurement result use <a class="linkSCPI" title="Returns the temperature of external NTC sensor" href="#SENS#:TEMP:DATA?">SENS#:TEMP:DATA?</a>.</div>
        <div class="example">&gt;&gt; SENS:CORR:COEF:BETA?<br />&lt;&lt; 3988</div>

      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="SENS">
        <span class="scpi" id="SENS#:CORR:COEF:RES">SENS#:CORR:COEF:RES</span><span class="briefScpi">Sets external thermistor equation R0 parameter</span>
      </div>
      <div class="content">
        <div class="scpiLong">SENSe<div class="tooltip" data-id="93">#</div>:CORRection:COEFficient:RESistance <div class="tooltip" data-id="94">&lt;resist&gt;</div></div>
        <div class="sufixDocu">Suffixe<ul>
          <li id="93"><span class="param">Sufix 1</span><span class="paramType">uint</span> <span class="paramDesc">external NTC channel number 5 for NTC1 or 6 for NTC2. Default is 5.</span></li>
        </ul></div>

        <div class="paramDocu">Parameters<ul>
              <li id="94"><span class="param">resist</span><span class="paramType">float,MIN,MAX,AUTO</span> <span class="paramDesc">R0 Resistance in Ohm. MIN, DEF or MAX for special parameters.</span></li>
        </ul></div>

        <div class="details">Use this command to set the R0 parameter in Ohm being part of the external NTC temperature calculation. To parameter value depends on the used thermistor. A wrong parameter value results in a wrong temperature measurement. To get the temperature measurement result use <a class="linkSCPI" title="Returns the temperature of external NTC sensor" href="#SENS#:TEMP:DATA?">SENS#:TEMP:DATA?</a>.</div>
        <div class="example">&gt;&gt; SENS:CORR:COEF:RES 10000</div>

      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="SENS">
        <span class="scpi" id="SENS#:CORR:COEF:RES?">SENS#:CORR:COEF:RES?</span><span class="briefScpi">Gets external thermistor equation R0 parameter</span>
      </div>
      <div class="content">
        <div class="scpiLong">SENSe<div class="tooltip" data-id="91">#</div>:CORRection:COEFficient:RESistance? <div class="tooltip" data-id="92">&lt;special&gt;</div></div>
        <div class="sufixDocu">Suffixe<ul>
          <li id="91"><span class="param">Sufix 1</span><span class="paramType">uint</span> <span class="paramDesc">external NTC channel number 5 for NTC1 or 6 for NTC2. Default is 5.</span></li>
        </ul></div>

        <div class="paramDocu">Parameters<ul>
           <li id="92"><span class="optParam">special</span><span class="paramType">MIN,MAX,AUTO</span> <span class="paramDesc">Optional! Nothing to query actual value in Ohm. MIN, MAX or DEF for special values.</span></li>
        </ul></div>

        <div class="resultDocu">Response<ul>
          <li><span class="param">R0</span><span class="paramType">float</span> <span class="paramDesc">thermistor R0 parameter in Ohm.</span></li>
        </ul></div>

        <div class="details">Use this command to query the R0 parameter in Ohm being part of the external NTC temperature calculation. To parameter value depends on the used thermistor. A wrong parameter value results in a wrong temperature measurement. To get the temperature measurement result use <a class="linkSCPI" title="Returns the temperature of external NTC sensor" href="#SENS#:TEMP:DATA?">SENS#:TEMP:DATA?</a>.</div>
        <div class="example">&gt;&gt; SENS:CORR:COEF:RES?<br />&lt;&lt; 10000</div>

      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="SENS">
        <span class="scpi" id="SENS#:CORR:COLL:ZERO">SENS#:CORR:COLL:ZERO</span><span class="briefScpi">Starts a zeroing of the sensor</span>
      </div>
      <div class="content">
        <div class="scpiLong">SENSe<div class="tooltip" data-id="134">#</div>:CORRection:COLLect:ZERO[:INITiate]</div>
        <div class="sufixDocu">Suffixe<ul>
          <li id="134"><span class="param">Sufix 1</span><span class="paramType">uint</span> <span class="paramDesc">channel the sensor head is connected to. Here always 1 (default).</span></li>
        </ul></div>

        <div class="details">Use this command to start a dark current or voltage correction of sensor. Before starting the correction ensure light source is not hitting the sensor area and the sensor is completely covered. This command only starts the zeroing procedure running in background. During zero procedure, measuring is not possible. To test if zeroing is still running use <a class="linkSCPI" title="Tests if zeroing procedure is running" href="#SENS#:CORR:COLL:ZERO:STAT?">SENS#:CORR:COLL:ZERO:STAT?</a> or poll for Zeroing flag in <a class="linkSCPI" title="Queries the SCPI Operation Condition register" href="#STAT:OPER:COND?">STAT:OPER:COND?</a>. If sensor is not completely covered zeroing procedure may time out automatically with an error code. If you want to abort previously started procedure use the <a class="linkSCPI" title="Aborts previously started zeroing procedure" href="#SENS#:CORR:COLL:ZERO:ABOR">SENS#:CORR:COLL:ZERO:ABOR</a> command. If zeroing is complete (by error or success) you may want to test for error using <a class="linkSCPI" title="Reads and removes oldest element of SCPI error queue" href="#SYST:ERR?">SYST:ERR?</a> command. To make relative measurements compared to ambient light use delta mode commands like <a class="linkSCPI" title="Sets relative power measurement offset" href="#SENS#:POW:REF">SENS#:POW:REF</a> because maximal zero correction parameter is limited to a small current. If you want to set a custom zero value use <a class="linkSCPI" title="Sets zero correction to a given value" href="#SENS#:CORR:COLL:ZERO:MAGN">SENS#:CORR:COLL:ZERO:MAGN</a> command. You may check for zeroing flag bit 7 in the Operation Status register <a class="linkSCPI" title="Queries the SCPI Operation Condition register" href="#STAT:OPER:COND?">STAT:OPER:COND?</a> so see zeroing is ongoing.</div>
        <div class="example">&gt;&gt; SENS:CORR:COLL:ZERO</div>
        <div class="note">Do not use zeroing to make relative measurements compared to ambient light. Zeroing is only available for Photodiode in CW mode.</div>
        <div class="info">Best practice is to cover the entire sensor aperture during zero procedure.</div>
      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="SENS">
        <span class="scpi" id="SENS#:CORR:COLL:ZERO:ABOR">SENS#:CORR:COLL:ZERO:ABOR</span><span class="briefScpi">Aborts previously started zeroing procedure</span>
      </div>
      <div class="content">
        <div class="scpiLong">SENSe<div class="tooltip" data-id="135">#</div>:CORRection:COLLect:ZERO:ABORt</div>
        <div class="sufixDocu">Suffixe<ul>
          <li id="135"><span class="param">Sufix 1</span><span class="paramType">uint</span> <span class="paramDesc">channel the sensor head is connected to. Here always 1 (default).</span></li>
        </ul></div>

        <div class="details">Use this command to abort a previously started zeroing. The zeroing of sensor is running as asynchronous background operation. Aborting the sequence will end the background operation and enables measuring with the old zero value. To start a new zeroing use <a class="linkSCPI" title="Starts a zeroing of the sensor" href="#SENS#:CORR:COLL:ZERO">SENS#:CORR:COLL:ZERO</a> command.</div>
        <div class="example">&gt;&gt; SENS:CORR:COLL:ABOR</div>
        <div class="note">Zeroing is only available for Photodiode in CW mode.</div>
        <div class="info">Aborting if zeroing is already complete or has never been started has no effect.</div>
      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="SENS">
        <span class="scpi" id="SENS#:CORR:COLL:ZERO:MAGN">SENS#:CORR:COLL:ZERO:MAGN</span><span class="briefScpi">Sets zero correction to a given value</span>
      </div>
      <div class="content">
        <div class="scpiLong">SENSe<div class="tooltip" data-id="137">#</div>:CORRection:COLLect:ZERO:MAGNitude <div class="tooltip" data-id="138">&lt;zero&gt;</div></div>
        <div class="sufixDocu">Suffixe<ul>
          <li id="137"><span class="param">Sufix 1</span><span class="paramType">uint</span> <span class="paramDesc">channel the sensor head is connected to. Here always 1 (default).</span></li>
        </ul></div>

        <div class="paramDocu">Parameters<ul>
              <li id="138"><span class="param">zero</span><span class="paramType">float,MIN,MAX,DEF</span> <span class="paramDesc">zero current in Ampere. MIN, DEF or MAX to set special values.</span></li>
        </ul></div>

        <div class="details">Use this command to set the zero correction value in Ampere. Setting zero directly will not start the asynchronous zeroing procedure. In most cases you want the Powermeter to measure zero itself using <a class="linkSCPI" title="Starts a zeroing of the sensor" href="#SENS#:CORR:COLL:ZERO">SENS#:CORR:COLL:ZERO</a> command. To make relative measurements compared to ambient light use delta mode commands like <a class="linkSCPI" title="Sets relative power measurement offset" href="#SENS#:POW:REF">SENS#:POW:REF</a> because maximal zero correction parameter is limited to a small current. Zero parameter is not stored persistently. It will be lost after reboot!</div>
        <div class="example">&gt;&gt; SENS:CORR:COLL:ZERO:MAGN 0</div>
        <div class="note">Zero parameter is only accessible for Photodiode in CW mode.</div>
        <div class="info">Set to 0 to disable zero correction.</div>
      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="SENS">
        <span class="scpi" id="SENS#:CORR:COLL:ZERO:MAGN?">SENS#:CORR:COLL:ZERO:MAGN?</span><span class="briefScpi">Queries the zero correction value</span>
      </div>
      <div class="content">
        <div class="scpiLong">SENSe<div class="tooltip" data-id="139">#</div>:CORRection:COLLect:ZERO:MAGNitude? <div class="tooltip" data-id="140">&lt;special&gt;</div></div>
        <div class="sufixDocu">Suffixe<ul>
          <li id="139"><span class="param">Sufix 1</span><span class="paramType">uint</span> <span class="paramDesc">channel the sensor head is connected to. Here always 1 (default).</span></li>
        </ul></div>

        <div class="paramDocu">Parameters<ul>
           <li id="140"><span class="optParam">special</span><span class="paramType">MIN,MAX,DEF</span> <span class="paramDesc">Optional! nothing to query actual value. MIN, MAX or DEF to query special zero values.</span></li>
        </ul></div>

        <div class="resultDocu">Response<ul>
          <li><span class="param">zero</span><span class="paramType">float</span> <span class="paramDesc">zero current in Ampere.</span></li>
        </ul></div>

        <div class="details">Use this command to get the zero correction value in Ampere. To update the zero value you can either call <a class="linkSCPI" title="Starts a zeroing of the sensor" href="#SENS#:CORR:COLL:ZERO">SENS#:CORR:COLL:ZERO</a> or the <a class="linkSCPI" title="Sets zero correction to a given value" href="#SENS#:CORR:COLL:ZERO:MAGN">SENS#:CORR:COLL:ZERO:MAGN</a> command. Zero parameter is not stored persistently. It will be lost after reboot!</div>
        <div class="example">&gt;&gt; SENS:CORR:COLL:ZERO:MAGN?<br />&lt;&lt; 1.234e-4</div>
        <div class="note">Zero parameter is only accessible for Photodiode in CW mode.</div>

      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="SENS">
        <span class="scpi" id="SENS#:CORR:COLL:ZERO:STAT?">SENS#:CORR:COLL:ZERO:STAT?</span><span class="briefScpi">Tests if zeroing procedure is running</span>
      </div>
      <div class="content">
        <div class="scpiLong">SENSe<div class="tooltip" data-id="136">#</div>:CORRection:COLLect:ZERO:STATe?</div>
        <div class="sufixDocu">Suffixe<ul>
          <li id="136"><span class="param">Sufix 1</span><span class="paramType">uint</span> <span class="paramDesc">channel the sensor head is connected to. Here always 1 (default).</span></li>
        </ul></div>

        <div class="resultDocu">Response<ul>
          <li><span class="param">state</span><span class="paramType">bool</span> <span class="paramDesc">0 if no zeroing is running, 1 otherwise.</span></li>
        </ul></div>

        <div class="details">Use this command to test if previously started zeroing procedure is running in background. The zeroing of sensor is running as asynchronous background operation. The zeroing will terminate automatically in case of error or success. Once terminated this command will return 0. If no zeroing has been started the command will also return 0. Another way to test if zeroing is complete is to poll for zeroing flag bit 7 in the Operation Status register <a class="linkSCPI" title="Queries the SCPI Operation Condition register" href="#STAT:OPER:COND?">STAT:OPER:COND?</a>.</div>
        <div class="example">&gt;&gt; SENS:CORR:COLL:STATe<br />&lt;&lt; 1</div>
        <div class="note">Zeroing is only available for Photodiode in CW mode.</div>

      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="SENS">
        <span class="scpi" id="SENS#:CORR:CSET:POIN">SENS#:CORR:CSET:POIN</span><span class="briefScpi">Sets customer calibration set point tuples</span>
      </div>
      <div class="content">
        <div class="scpiLong">SENSe<div class="tooltip" data-id="111">#</div>:CORRection:CSET:POINts <div class="tooltip" data-id="112">&lt;wavelength1&gt;</div>,<div class="tooltip" data-id="113">&lt;factor1&gt;</div>,<div class="tooltip" data-id="114">&lt;wavelength2&gt;</div>,<div class="tooltip" data-id="115">&lt;factor2&gt;</div>,<div class="tooltip" data-id="116">&lt;wavelength3&gt;</div>,<div class="tooltip" data-id="117">&lt;factor3&gt;</div></div>
        <div class="sufixDocu">Suffixe<ul>
          <li id="111"><span class="param">Sufix 1</span><span class="paramType">uint</span> <span class="paramDesc">channel the sensor head is connected to. Here always 1 (default).</span></li>
        </ul></div>

        <div class="paramDocu">Parameters<ul>
              <li id="112"><span class="param">wavelength1</span><span class="paramType">uint</span> <span class="paramDesc">wavelength in nm.</span></li>
              <li id="113"><span class="param">factor1</span><span class="paramType">float</span> <span class="paramDesc">correction factor between 0.8 and 1.2.</span></li>
           <li id="114"><span class="optParam">wavelength2</span><span class="paramType">uint</span> <span class="paramDesc">Optional! wavelength in nm. Needs to be larger than previous wavelength.</span></li>
           <li id="115"><span class="optParam">factor2</span><span class="paramType">float</span> <span class="paramDesc">Optional! correction factor between 0.8 and 1.2.</span></li>
           <li id="116"><span class="optParam">wavelength3</span><span class="paramType">uint</span> <span class="paramDesc">Optional! wavelength in nm. Needs to be larger than previous wavelength.</span></li>
           <li id="117"><span class="optParam">factor3</span><span class="paramType">float</span> <span class="paramDesc">Optional! correction factor between 0.8 and 1.2.</span></li>
        </ul></div>

        <div class="details">Use this as the second command of the command sequence to write the custom calibration to inject the tuple list for the correction wavelength factor. The command sequence of writing a customer calibration is <a class="linkSCPI" title="Starts updating customer calibration by writing slot meta information" href="#SENS#:CORR:CSET:PRE">SENS#:CORR:CSET:PRE</a>, <a class="linkSCPI" title="Sets customer calibration set point tuples" href="#SENS#:CORR:CSET:POIN">SENS#:CORR:CSET:POIN</a> and <a class="linkSCPI" title="Stores previously configured customer calibration in persistent memory" href="#SENS#:CORR:CSET#:DEF">SENS#:CORR:CSET#:DEF</a>. To enable the new configuration use <a class="linkSCPI" title="Enables/Disables customer calibration" href="#SENS#:CORR:CSET#:STAT">SENS#:CORR:CSET#:STAT</a> and reboot the device or send the command: <a class="linkSCPI" title="Reconnect connected sensor" href="#SENS#:REI">SENS#:REI</a>. The command supports a variable parameter list length. Every correction can store up to to 10 wavelength - factor tuples. The command may be called with multiple tuples at once. The wavelength needs to be strictly ascending starting with the lowest.<br/> The factor can be determined with a comparison measurement with a known wavelength and power level. Best practice is to average some measurements, performed with the commands <a class="linkSCPI" title="Sets the averaging for slow measurements" href="#SENS#:AVER">SENS#:AVER</a> 300 and <a class="linkSCPI" title="Measures the sensor averaged power in Watt or dBm" href="#MEAS?">MEAS?</a>. (the example shows an averaging of 300) The factor can then be calculated with the formula factor = avgMeasPower / refPower.</div>
        <div class="example">&gt;&gt; SENS1:CORR:CSET:POIN 400,1.1,450,1.1,500,1.1,550,1.1</div>
        <div class="note">Second command of customer calibration command sequence. Next is <a class="linkSCPI" title="Stores previously configured customer calibration in persistent memory" href="#SENS#:CORR:CSET#:DEF">SENS#:CORR:CSET#:DEF</a>.</div>
        <div class="info">A maximum of 10 set points tuples are allowed. Only tuples of ascending wavelength and factor allowed.</div>
      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="SENS">
        <span class="scpi" id="SENS#:CORR:CSET:PRE">SENS#:CORR:CSET:PRE</span><span class="briefScpi">Starts updating customer calibration by writing slot meta information</span>
      </div>
      <div class="content">
        <div class="scpiLong">SENSe<div class="tooltip" data-id="104">#</div>:CORRection:CSET:PREamble <div class="tooltip" data-id="105">&lt;serial&gt;</div>,<div class="tooltip" data-id="106">&lt;calDate&gt;</div>,<div class="tooltip" data-id="107">&lt;author&gt;</div>,<div class="tooltip" data-id="108">&lt;filterPos&gt;</div></div>
        <div class="sufixDocu">Suffixe<ul>
          <li id="104"><span class="param">Sufix 1</span><span class="paramType">uint</span> <span class="paramDesc">channel the sensor head is connected to. Here always 1 (default).</span></li>
        </ul></div>

        <div class="paramDocu">Parameters<ul>
              <li id="105"><span class="param">serial</span><span class="paramType">string</span> <span class="paramDesc">serial number of the related sensor. Max 19 characters.</span></li>
              <li id="106"><span class="param">calDate</span><span class="paramType">string</span> <span class="paramDesc">date of calibration. Max 19 characters.</span></li>
              <li id="107"><span class="param">author</span><span class="paramType">string</span> <span class="paramDesc">author of calibration. Max 19 characters.</span></li>
              <li id="108"><span class="param">filterPos</span><span class="paramType">1 or 2</span> <span class="paramDesc">for related sensor filter position the correction is for.</span></li>
        </ul></div>

        <div class="details">Use this as first command of the customer calibration write command sequence to start writing the correction meta information. The command sequence of writing a customer calibration is SENS1:CORR:CSET:PRE, <a class="linkSCPI" title="Sets customer calibration set point tuples" href="#SENS#:CORR:CSET:POIN">SENS#:CORR:CSET:POIN</a> and <a class="linkSCPI" title="Stores previously configured customer calibration in persistent memory" href="#SENS#:CORR:CSET#:DEF">SENS#:CORR:CSET#:DEF</a>. To enable the new configuration use <a class="linkSCPI" title="Enables/Disables customer calibration" href="#SENS#:CORR:CSET#:STAT">SENS#:CORR:CSET#:STAT</a> and reboot device or send command <a class="linkSCPI" title="Reconnect connected sensor" href="#SENS#:REI">SENS#:REI</a>. After storing the meta information it is mandatory to add at least one wavelength set point by using <a class="linkSCPI" title="Sets customer calibration set point tuples" href="#SENS#:CORR:CSET:POIN">SENS#:CORR:CSET:POIN</a>. To get the serial of the connected sensor call <a class="linkSCPI" title="Sensor head identification query" href="#SYST:SENS:IDN?">SYST:SENS:IDN?</a>.</div>
        <div class="example">&gt;&gt; SENS1:CORR:CSET:PRE "191113108","2021-11-28","test",1</div>
        <div class="note">First command of customer calibration command sequence. Next is <a class="linkSCPI" title="Sets customer calibration set point tuples" href="#SENS#:CORR:CSET:POIN">SENS#:CORR:CSET:POIN</a>.</div>
        <div class="info">To delete a customer calibration use empty strings. For example SENSe1:CORR:CSET:PRE "","","",1.</div>
      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="SENS">
        <span class="scpi" id="SENS#:CORR:CSET#:DEF">SENS#:CORR:CSET#:DEF</span><span class="briefScpi">Stores previously configured customer calibration in persistent memory</span>
      </div>
      <div class="content">
        <div class="scpiLong">SENSe<div class="tooltip" data-id="120">#</div>:CORRection:CSET<div class="tooltip" data-id="121">#</div>:DEFine</div>
        <div class="sufixDocu">Suffixe<ul>
          <li id="120"><span class="param">Sufix 1</span><span class="paramType">uint</span> <span class="paramDesc">channel the sensor head is connected to. Here always 1 (default).</span></li>
          <li id="121"><span class="param">Sufix 2</span><span class="paramType">uint</span> <span class="paramDesc">customer calibration memory slot between 1 and 4.</span></li>
        </ul></div>

        <div class="details">Stores the previously configured customer calibration object stored in volatile memory to persistent Powermeter storage. Only when stored persistently the correction object ready for usage. The command sequence of writing a customer calibration is <a class="linkSCPI" title="Starts updating customer calibration by writing slot meta information" href="#SENS#:CORR:CSET:PRE">SENS#:CORR:CSET:PRE</a>, <a class="linkSCPI" title="Sets customer calibration set point tuples" href="#SENS#:CORR:CSET:POIN">SENS#:CORR:CSET:POIN</a> and SENS1:CORR:CSET3:DEF. To enable the new configuration use <a class="linkSCPI" title="Enables/Disables customer calibration" href="#SENS#:CORR:CSET#:STAT">SENS#:CORR:CSET#:STAT</a> and reboot device or send command <a class="linkSCPI" title="Reconnect connected sensor" href="#SENS#:REI">SENS#:REI</a>.</div>
        <div class="example">&gt;&gt; SENS1:CORR:CSET3:DEF</div>
        <div class="note">Third command of customer calibration command sequence. Next is <a class="linkSCPI" title="Reconnect connected sensor" href="#SENS#:REI">SENS#:REI</a>.</div>

      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="SENS">
        <span class="scpi" id="SENS#:CORR:CSET#:POIN?">SENS#:CORR:CSET#:POIN?</span><span class="briefScpi">Returns customer calibration set point tuples</span>
      </div>
      <div class="content">
        <div class="scpiLong">SENSe<div class="tooltip" data-id="118">#</div>:CORRection:CSET<div class="tooltip" data-id="119">#</div>:POINts?</div>
        <div class="sufixDocu">Suffixe<ul>
          <li id="118"><span class="param">Sufix 1</span><span class="paramType">uint</span> <span class="paramDesc">channel the sensor head is connected to. Here always 1 (default).</span></li>
          <li id="119"><span class="param">Sufix 2</span><span class="paramType">uint</span> <span class="paramDesc">customer calibration memory slot between 1 and 4.</span></li>
        </ul></div>

        <div class="resultDocu">Response<ul>
          <li><span class="param">wavelength1</span><span class="paramType">uint</span> <span class="paramDesc">wavelength in nm.</span></li>
          <li><span class="param">factor1</span><span class="paramType">float</span> <span class="paramDesc">correction factor between 0.8 and 1.2.</span></li>
          <li><span class="param">wavelength2</span><span class="paramType">uint</span> <span class="paramDesc">wavelength in nm.</span></li>
          <li><span class="param">factor2</span><span class="paramType">float</span> <span class="paramDesc">correction factor between 0.8 and 1.2.</span></li>
          <li><span class="param">wavelength3</span><span class="paramType">uint</span> <span class="paramDesc">wavelength in nm.</span></li>
          <li><span class="param">factor3</span><span class="paramType">float</span> <span class="paramDesc">correction factor between 0.8 and 1.2.</span></li>
        </ul></div>

        <div class="details">Call this command to query the customer calibration set points tuples. The result tuple list length is variable. All tuples are returned at once.To get the amount of returned tuples call <a class="linkSCPI" title="Returns customer calibration meta information of given slot" href="#SENS#:CORR:CSET#:PRE?">SENS#:CORR:CSET#:PRE?</a> previously or parse to the end. Be aware this function only access data in the persistent customer calibration storage. So if you called <a class="linkSCPI" title="Sets customer calibration set point tuples" href="#SENS#:CORR:CSET:POIN">SENS#:CORR:CSET:POIN</a> previously and the data has not yet been stored in persistent memory by the command <a class="linkSCPI" title="Stores previously configured customer calibration in persistent memory" href="#SENS#:CORR:CSET#:DEF">SENS#:CORR:CSET#:DEF</a>, this command will return old information!</div>
        <div class="example">&gt;&gt; SENS1:CORR:CSET3:POIN?<br />&lt;&lt; 400,1.1,450,1.1,500,1.1,550,1.1</div>
        <div class="info">A maximum of 10 set points tuples is possible. Wavelength is ascending starting with lowest first.</div>
      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="SENS">
        <span class="scpi" id="SENS#:CORR:CSET#:PRE?">SENS#:CORR:CSET#:PRE?</span><span class="briefScpi">Returns customer calibration meta information of given slot</span>
      </div>
      <div class="content">
        <div class="scpiLong">SENSe<div class="tooltip" data-id="109">#</div>:CORRection:CSET<div class="tooltip" data-id="110">#</div>:PREamble?</div>
        <div class="sufixDocu">Suffixe<ul>
          <li id="109"><span class="param">Sufix 1</span><span class="paramType">uint</span> <span class="paramDesc">channel the sensor head is connected to. Here always 1 (default).</span></li>
          <li id="110"><span class="param">Sufix 2</span><span class="paramType">uint</span> <span class="paramDesc">customer calibration memory slot between 1 and 4.</span></li>
        </ul></div>

        <div class="resultDocu">Response<ul>
          <li><span class="param">serial</span><span class="paramType">string</span> <span class="paramDesc">serial number of the related sensor. Max 19 characters.</span></li>
          <li><span class="param">calDate</span><span class="paramType">string</span> <span class="paramDesc">date of calibration. Max 19 characters.</span></li>
          <li><span class="param">author</span><span class="paramType">string</span> <span class="paramDesc">author of calibration. Max 19 characters.</span></li>
          <li><span class="param">filterPos</span><span class="paramType">uint</span> <span class="paramDesc">1 or 2 for related sensor filter position the correction is for.</span></li>
          <li><span class="param">setPoints</span><span class="paramType">uint</span> <span class="paramDesc">number of setpoins defined.</span></li>
        </ul></div>

        <div class="details">Use this command to query the customer calibration meta information. Be aware this function only access data in the persistent customer calibration storage. So if you called <a class="linkSCPI" title="Starts updating customer calibration by writing slot meta information" href="#SENS#:CORR:CSET:PRE">SENS#:CORR:CSET:PRE</a> previously and the data has not yet been stored in persistent memory by the command <a class="linkSCPI" title="Stores previously configured customer calibration in persistent memory" href="#SENS#:CORR:CSET#:DEF">SENS#:CORR:CSET#:DEF</a>, this command will return old information!</div>
        <div class="example">&gt;&gt; SENS1:CORR:CSET3:PRE?<br />&lt;&lt; "191113108","2019-11-28","Musterman",1,2</div>
        <div class="info">If customer calibration is empty you get a result: "","","",0,0.</div>
      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="SENS">
        <span class="scpi" id="SENS#:CORR:CSET#:STAT">SENS#:CORR:CSET#:STAT</span><span class="briefScpi">Enables/Disables customer calibration</span>
      </div>
      <div class="content">
        <div class="scpiLong">SENSe<div class="tooltip" data-id="101">#</div>:CORRection:CSET<div class="tooltip" data-id="102">#</div>:STATe <div class="tooltip" data-id="103">&lt;state&gt;</div></div>
        <div class="sufixDocu">Suffixe<ul>
          <li id="101"><span class="param">Sufix 1</span><span class="paramType">uint</span> <span class="paramDesc">channel the sensor head is connected to. Here always 1 (default).</span></li>
          <li id="102"><span class="param">Sufix 2</span><span class="paramType">uint</span> <span class="paramDesc">customer calibration memory slot between 1 and 4.</span></li>
        </ul></div>

        <div class="paramDocu">Parameters<ul>
              <li id="103"><span class="param">state</span><span class="paramType">bool</span> <span class="paramDesc">1 to enable customer calibration. 0 to disable slot.</span></li>
        </ul></div>

        <div class="details">Use this command to enable/disable a customer calibration at given slot. The Powermeter stores up to 5 different customer calibrations. A customer calibration allows the customer to modify the sensor correction at fixed wavelength set points. It is stored within the Powermeter and belongs to a single sensor identified by it's serial number. The factory calibration of the Powermeter and the sensor is not touched by the customer calibration. The Powermeter automatically applies the customer calibration if the related sensor is connected and the calibration is active. To test if a customer calibration is active and currently used see UseCustomerCalibration flag bit 0 or 1 in <a class="linkSCPI" title="Queries the SCPI Auxiliary Condition register" href="#STAT:AUX:COND?">STAT:AUX:COND?</a>. The command sequence of writing a new customer calibration is <a class="linkSCPI" title="Starts updating customer calibration by writing slot meta information" href="#SENS#:CORR:CSET:PRE">SENS#:CORR:CSET:PRE</a>, <a class="linkSCPI" title="Sets customer calibration set point tuples" href="#SENS#:CORR:CSET:POIN">SENS#:CORR:CSET:POIN</a> and <a class="linkSCPI" title="Stores previously configured customer calibration in persistent memory" href="#SENS#:CORR:CSET#:DEF">SENS#:CORR:CSET#:DEF</a>. To enable the new configuration use <a class="linkSCPI" title="Enables/Disables customer calibration" href="#SENS#:CORR:CSET#:STAT">SENS#:CORR:CSET#:STAT</a> and reboot device or send command <a class="linkSCPI" title="Reconnect connected sensor" href="#SENS#:REI">SENS#:REI</a>.</div>
        <div class="example">&gt;&gt; SENS1:CORR:CSET1:STAT 1</div>
        <div class="note">Be aware a wrong customer calibration will result in unexpected measurement results.</div>

      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="SENS">
        <span class="scpi" id="SENS#:CORR:CSET#:STAT?">SENS#:CORR:CSET#:STAT?</span><span class="briefScpi">Tests if customer calibration is enabled/disabled</span>
      </div>
      <div class="content">
        <div class="scpiLong">SENSe<div class="tooltip" data-id="99">#</div>:CORRection:CSET<div class="tooltip" data-id="100">#</div>:STATe?</div>
        <div class="sufixDocu">Suffixe<ul>
          <li id="99"><span class="param">Sufix 1</span><span class="paramType">uint</span> <span class="paramDesc">channel the sensor head is connected to. Here always 1 (default).</span></li>
          <li id="100"><span class="param">Sufix 2</span><span class="paramType">uint</span> <span class="paramDesc">customer calibration memory slot between 1 and 4.</span></li>
        </ul></div>

        <div class="resultDocu">Response<ul>
          <li><span class="param">state</span><span class="paramType">bool</span> <span class="paramDesc">1 if customer calibration is enabled. 0 if slot is disabled.</span></li>
        </ul></div>

        <div class="details">Use this command to test if a customer calibration at given slot is enabled/disabled. For closer details read command <a class="linkSCPI" title="Enables/Disables customer calibration" href="#SENS#:CORR:CSET#:STAT">SENS#:CORR:CSET#:STAT</a>.</div>
        <div class="example">&gt;&gt; SENS1:CORR:CSET1:STAT?<br />&lt;&lt; 0</div>
        <div class="note">Be aware a wrong customer calibration will result in unexpected measurement results.</div>

      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="SENS">
        <span class="scpi" id="SENS#:CORR:POW">SENS#:CORR:POW</span><span class="briefScpi">Sets photodiode sensor adapter responsivity</span>
      </div>
      <div class="content">
        <div class="scpiLong">SENSe<div class="tooltip" data-id="83">#</div>:CORRection:POWer[:PDIode][:RESPonse] <div class="tooltip" data-id="84">&lt;resp&gt;</div></div>
        <div class="sufixDocu">Suffixe<ul>
          <li id="83"><span class="param">Sufix 1</span><span class="paramType">uint</span> <span class="paramDesc">channel the sensor head is connected to. Here always 1 (default).</span></li>
        </ul></div>

        <div class="paramDocu">Parameters<ul>
              <li id="84"><span class="param">resp</span><span class="paramType">float,MIN,DEF,MAX</span> <span class="paramDesc">Wavelength dependent responsivity in A/W. MIN, DEF or MAX for special parameters.</span></li>
        </ul></div>

        <div class="details">Use this command to set the photodiode responsivity in A/W for photodiode sensor adapters. The responsivity is used to calculate the power based on the photodiode current. The correct responsivity depends on the light wavelength. Adapters are sensor without head EEPROM. When working with adapters ensure the correct adapter type is selected by using <a class="linkSCPI" title="Sets the default adapter sensor type" href="#INP:ADAP">INP:ADAP</a>. If you are working with a sensor with EEPROM use <a class="linkSCPI" title="Sets the wavelength" href="#SENS#:CORR:WAV">SENS#:CORR:WAV</a> to change responsivity.</div>
        <div class="example">&gt;&gt; SENS:CORR:POW 1.34</div>
        <div class="note">Photododiode adapter sensors without EEPROM only!</div>

      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="SENS">
        <span class="scpi" id="SENS#:CORR:POW?">SENS#:CORR:POW?</span><span class="briefScpi">Gets photodiode sensor responsivity</span>
      </div>
      <div class="content">
        <div class="scpiLong">SENSe<div class="tooltip" data-id="85">#</div>:CORRection:POWer[:PDIode][:RESPonse]? <div class="tooltip" data-id="86">&lt;special&gt;</div></div>
        <div class="sufixDocu">Suffixe<ul>
          <li id="85"><span class="param">Sufix 1</span><span class="paramType">uint</span> <span class="paramDesc">channel the sensor head is connected to. Here always 1 (default).</span></li>
        </ul></div>

        <div class="paramDocu">Parameters<ul>
           <li id="86"><span class="optParam">special</span><span class="paramType">float, MIN,MAX,AUTO</span> <span class="paramDesc">Optional! Nothing to query actual value in A/W. MIN, MAX or DEF for special values. Wavelength in nm to get the responsivity for (Not for adapters).</span></li>
        </ul></div>

        <div class="resultDocu">Response<ul>
          <li><span class="param">resp</span><span class="paramType">float</span> <span class="paramDesc">responsivity in A/W.</span></li>
        </ul></div>

        <div class="details">Use this command to query the photodiode responsivity in A/W for photodiode sensors with or without EEPROM. The wavelength related responsivity is used to calculate the power based on the photodiode current. Thorlabs photodiode sensors store the responsivity curve internally. To query the EEPROM curve you can call this command multiple times with a wavelength in nm as parameter (Not for adapters). If no parameter is given the function returns the currently used responsivity.</div>
        <div class="example">&gt;&gt; SENS:CORR:POW?<br />&lt;&lt; 1.34</div>

      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="SENS">
        <span class="scpi" id="SENS#:CORR:POW:THER">SENS#:CORR:POW:THER</span><span class="briefScpi">Sets thermopile sensor adapter responsivity</span>
      </div>
      <div class="content">
        <div class="scpiLong">SENSe<div class="tooltip" data-id="87">#</div>:CORRection:POWer:THERmopile[:RESPonse] <div class="tooltip" data-id="88">&lt;resp&gt;</div></div>
        <div class="sufixDocu">Suffixe<ul>
          <li id="87"><span class="param">Sufix 1</span><span class="paramType">uint</span> <span class="paramDesc">channel the sensor head is connected to. Here always 1 (default).</span></li>
        </ul></div>

        <div class="paramDocu">Parameters<ul>
              <li id="88"><span class="param">resp</span><span class="paramType">float,MIN,DEF,MAX</span> <span class="paramDesc">Wavelength dependent responsivity in V/W. MIN, DEF or MAX for special parameters.</span></li>
        </ul></div>

        <div class="details">Use this command to set the thermopile responsivity in V/W for thermopile sensor adapters. The responsivity is used to calculate the power based on the thermopile voltage. The correct responsivity depends on the wavelength of the light. Adapters are sensor without head EEPROM. When working with adapters ensure the correct adapter type is selected by using <a class="linkSCPI" title="Sets the default adapter sensor type" href="#INP:ADAP">INP:ADAP</a>. If you are working with a sensor with EEPROM use <a class="linkSCPI" title="Sets the wavelength" href="#SENS#:CORR:WAV">SENS#:CORR:WAV</a> to change responsivity.</div>
        <div class="example">&gt;&gt; SENS:CORR:POW:THER 1e-2</div>
        <div class="note">Thermopile adapter sensors without EEPROM only!</div>
        <div class="info">Responsivity is a wavelength dependent value.</div>
      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="SENS">
        <span class="scpi" id="SENS#:CORR:POW:THER?">SENS#:CORR:POW:THER?</span><span class="briefScpi">Gets thermopile sensor responsivity</span>
      </div>
      <div class="content">
        <div class="scpiLong">SENSe<div class="tooltip" data-id="89">#</div>:CORRection:POWer:THERmopile[:RESPonse]? <div class="tooltip" data-id="90">&lt;special&gt;</div></div>
        <div class="sufixDocu">Suffixe<ul>
          <li id="89"><span class="param">Sufix 1</span><span class="paramType">uint</span> <span class="paramDesc">channel the sensor head is connected to. Here always 1 (default).</span></li>
        </ul></div>

        <div class="paramDocu">Parameters<ul>
           <li id="90"><span class="optParam">special</span><span class="paramType">float, MIN,DEF,MAX</span> <span class="paramDesc">Optional! Nothing to query actual value in V/J. MIN, MAX or DEF for special values. Wavelength in nm to get the responsivity for (Not for adapters).</span></li>
        </ul></div>

        <div class="resultDocu">Response<ul>
          <li><span class="param">resp</span><span class="paramType">float</span> <span class="paramDesc">responsivity in V/W.</span></li>
        </ul></div>

        <div class="details">Use this command to query the thermopile responsivity in V/W for thermopile sensors with or without EEPROM. The wavelength related responsivity is used to calculate the power based on the thermopile voltage. Thorlabs thermopile sensors store the responsivity curve internally. To query the EEPROM curve you can call this command multiple times with a wavelength in nm as parameter (Not for adapters). If no parameter is given the function returns the currently used responsivity.</div>
        <div class="example">&gt;&gt; SENS:CORR:POW:THER?<br />&lt;&lt; 0.09</div>

      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="SENS">
        <span class="scpi" id="SENS#:CORR:WAV">SENS#:CORR:WAV</span><span class="briefScpi">Sets the wavelength</span>
      </div>
      <div class="content">
        <div class="scpiLong">SENSe<div class="tooltip" data-id="50">#</div>:CORRection:WAVelength <div class="tooltip" data-id="51">&lt;wavelength&gt;</div></div>
        <div class="sufixDocu">Suffixe<ul>
          <li id="50"><span class="param">Sufix 1</span><span class="paramType">uint</span> <span class="paramDesc">channel the sensor head is connected to. Here always 1 (default).</span></li>
        </ul></div>

        <div class="paramDocu">Parameters<ul>
              <li id="51"><span class="param">wavelength</span><span class="paramType">uint,MIN,MAX</span> <span class="paramDesc">wavelength as whole number in nm. MIN or MAX to set special values.</span></li>
        </ul></div>

        <div class="details">Use this command to set wavelength of light in nm. Wavelength is used to calculate sensor responsivity. If you are using adapter sensors you have to set responsivity directly for example using <a class="linkSCPI" title="Sets photodiode sensor adapter responsivity" href="#SENS#:CORR:POW">SENS#:CORR:POW</a>. Wavelength parameter is stored persistently and restored after reboot. If connected sensor type get changed, defaults are applied. If the sensor type stays same the parameter gets coerced automatically.</div>
        <div class="example">&gt;&gt; SENS:CORR:WAV 850</div>
        <div class="note">Only supported for sensor heads with EEPROM. Adapter types have to set responsivity directly.</div>

      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="SENS">
        <span class="scpi" id="SENS#:CORR:WAV?">SENS#:CORR:WAV?</span><span class="briefScpi">Gets the wavelength</span>
      </div>
      <div class="content">
        <div class="scpiLong">SENSe<div class="tooltip" data-id="48">#</div>:CORRection:WAVelength? <div class="tooltip" data-id="49">&lt;special&gt;</div></div>
        <div class="sufixDocu">Suffixe<ul>
          <li id="48"><span class="param">Sufix 1</span><span class="paramType">uint</span> <span class="paramDesc">channel the sensor head is connected to. Here always 1 (default).</span></li>
        </ul></div>

        <div class="paramDocu">Parameters<ul>
           <li id="49"><span class="optParam">special</span><span class="paramType">MIN,MAX</span> <span class="paramDesc">Optional! nothing to query actual value. MIN or MAX to query special wavelength values.</span></li>
        </ul></div>

        <div class="resultDocu">Response<ul>
          <li><span class="param">wavelength</span><span class="paramType">float</span> <span class="paramDesc">wavelength in nm</span></li>
        </ul></div>

        <div class="details">Use this command to query the wavelength in nm. If you are using adapter sensors you have to get responsivity directly using <a class="linkSCPI" title="Gets photodiode sensor responsivity" href="#SENS#:CORR:POW?">SENS#:CORR:POW?</a>. Wavelength parameter is stored persistently and restored after reboot. If connected sensor type get changed, defaults are applied. If the sensor type stays same the parameter gets coerced automatically.</div>
        <div class="example">&gt;&gt; SENS:CORR:WAV? MIN<br />&lt;&lt; 1020</div>
        <div class="note">Only supported for sensor heads with EEPROM. Adapter types have to get responsivity directly.</div>

      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="SENS">
        <span class="scpi" id="SENS#:CURR:RANG">SENS#:CURR:RANG</span><span class="briefScpi">Sets a measurement range for given current</span>
      </div>
      <div class="content">
        <div class="scpiLong">SENSe<div class="tooltip" data-id="67">#</div>:CURRent[:DC]:RANGe[:UPPer] <div class="tooltip" data-id="68">&lt;current&gt;</div></div>
        <div class="sufixDocu">Suffixe<ul>
          <li id="67"><span class="param">Sufix 1</span><span class="paramType">uint</span> <span class="paramDesc">channel the sensor head is connected to. Here always 1 (default).</span></li>
        </ul></div>

        <div class="paramDocu">Parameters<ul>
              <li id="68"><span class="param">current</span><span class="paramType">float,MIN,MAX</span> <span class="paramDesc">Set gain to measure given current best. MIN or MAX for most or least sensitive gain.</span></li>
        </ul></div>

        <div class="details">This command sets a manual range for given current in Ampere. Setting a manual range will automatically disable auto ranging see <a class="linkSCPI" title="Sets current measurement auto ranging" href="#SENS#:CURR:RANG:AUTO">SENS#:CURR:RANG:AUTO</a>. Changing the measurement range will always interrupt the measurement for up to 10 milliseconds to allow the analogue hardware to settle. After this time measurement is continued automatically. For MIN you set the most sensitive gain level. For MAX you set least sensitive gain level. Manual range is stored persistently and gets restored after reboot. You may check for ranging flag bit 2 in the Operation Status register <a class="linkSCPI" title="Queries the SCPI Operation Condition register" href="#STAT:OPER:COND?">STAT:OPER:COND?</a> so see if ranging is complete.</div>
        <div class="example">&gt;&gt; SENS:CURR:RANG 2.56e-5</div>
        <div class="note">Photodiode sensors only!</div>
        <div class="info">Setting a manual range automatically disables auto ranging. See <a class="linkSCPI" title="Sets current measurement auto ranging" href="#SENS#:CURR:RANG:AUTO">SENS#:CURR:RANG:AUTO</a>.</div>
      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="SENS">
        <span class="scpi" id="SENS#:CURR:RANG?">SENS#:CURR:RANG?</span><span class="briefScpi">Returns measurement range for current measurement</span>
      </div>
      <div class="content">
        <div class="scpiLong">SENSe<div class="tooltip" data-id="65">#</div>:CURRent[:DC]:RANGe[:UPPer]? <div class="tooltip" data-id="66">&lt;current&gt;</div></div>
        <div class="sufixDocu">Suffixe<ul>
          <li id="65"><span class="param">Sufix 1</span><span class="paramType">uint</span> <span class="paramDesc">channel the sensor head is connected to. Here always 1 (default).</span></li>
        </ul></div>

        <div class="paramDocu">Parameters<ul>
           <li id="66"><span class="optParam">current</span><span class="paramType">float,MIN,MAX,AUTO</span> <span class="paramDesc">Optional! current to get range maximum for. Special parameter to query special range info.</span></li>
        </ul></div>

        <div class="resultDocu">Response<ul>
          <li><span class="param">rangeMax</span><span class="paramType">float</span> <span class="paramDesc">maximum current in Ampere supported by measurement range. For special parameter AUTO it will be a list.</span></li>
        </ul></div>

        <div class="details">This command returns measurement range information for current measurement in Ampere. If no parameter has been provided the actual used range maximal current is returned. When called with a current in Ampere float parameter the function will return the measurement range maximum fitting given current best. The following special parameters may be used: <ul class="detailListing"> <li><b>MIN</b> maximum current, supported by the most sensitive gain level.</li> <li><b>MAX</b> maximum current, supported by the least sensitive gain level.</li> <li><b>AUTO</b> list of maximum current for all measurement ranges. Starts with the least sensitive gain level.</li> </ul></div>
        <div class="example">&gt;&gt; SENS:CURR:RANG? MAX<br />&lt;&lt; 9.93-e5</div>
        <div class="note">Photodiode sensors only!</div>

      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="SENS">
        <span class="scpi" id="SENS#:CURR:RANG:AUTO">SENS#:CURR:RANG:AUTO</span><span class="briefScpi">Sets current measurement auto ranging</span>
      </div>
      <div class="content">
        <div class="scpiLong">SENSe<div class="tooltip" data-id="53">#</div>:CURRent[:DC]:RANGe:AUTO <div class="tooltip" data-id="54">&lt;state&gt;</div></div>
        <div class="sufixDocu">Suffixe<ul>
          <li id="53"><span class="param">Sufix 1</span><span class="paramType">uint</span> <span class="paramDesc">channel the sensor head is connected to. Here always 1 (default).</span></li>
        </ul></div>

        <div class="paramDocu">Parameters<ul>
              <li id="54"><span class="param">state</span><span class="paramType">bool</span> <span class="paramDesc">1 to enable auto ranging, 0 otherwise.</span></li>
        </ul></div>

        <div class="details">This command enables or disables auto ranging feature for current measurement. If auto ranging is active the Powermeter compares the measured signal to the currently used measure range. If the signal is out of optimal range, the Powermeter automatically changes the measurement range. Changing the measurement range will always interrupt the measurement for up to 10 milliseconds to allow the analogue hardware to settle. After this time measurement is continued automatically. You can ask for the actual range by using command <a class="linkSCPI" title="Returns measurement range for current measurement" href="#SENS#:CURR:RANG?">SENS#:CURR:RANG?</a>. If you set a manual range like command <a class="linkSCPI" title="Sets a measurement range for given current" href="#SENS#:CURR:RANG">SENS#:CURR:RANG</a> auto ranging gets disabled automatically. Auto range enable state is stored persistently and restored after reboot. If you change sensor type auto ranging is enabled by default. You may check for ranging flag bit 2 in the Operation Status register <a class="linkSCPI" title="Queries the SCPI Operation Condition register" href="#STAT:OPER:COND?">STAT:OPER:COND?</a> so see if ranging took place complete.</div>
        <div class="example">&gt;&gt; SENS:CURR:RANG:AUTO 1</div>
        <div class="info">For scope mode it makes sense to disable auto ranging to get a seamless result stream.</div>
      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="SENS">
        <span class="scpi" id="SENS#:CURR:RANG:AUTO?">SENS#:CURR:RANG:AUTO?</span><span class="briefScpi">Tests if current auto ranging is enabled</span>
      </div>
      <div class="content">
        <div class="scpiLong">SENSe<div class="tooltip" data-id="52">#</div>:CURRent[:DC]:RANGe:AUTO?</div>
        <div class="sufixDocu">Suffixe<ul>
          <li id="52"><span class="param">Sufix 1</span><span class="paramType">uint</span> <span class="paramDesc">channel the sensor head is connected to. Here always 1 (default).</span></li>
        </ul></div>

        <div class="resultDocu">Response<ul>
          <li><span class="param">state</span><span class="paramType">bool</span> <span class="paramDesc">1 if auto ranging is active, 0 otherwise.</span></li>
        </ul></div>

        <div class="details">This command tests if auto ranging is enabled for current measurement. For closer details read <a class="linkSCPI" title="Sets current measurement auto ranging" href="#SENS#:CURR:RANG:AUTO">SENS#:CURR:RANG:AUTO</a>. You can query the currently used range by using <a class="linkSCPI" title="Returns measurement range for current measurement" href="#SENS#:CURR:RANG?">SENS#:CURR:RANG?</a>. Auto range enable state is stored persistently and restored after reboot.</div>
        <div class="example">&gt;&gt; SENS:CURR:RANG:AUTO?<br />&lt;&lt; 0</div>

      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="SENS">
        <span class="scpi" id="SENS#:CURR:REF">SENS#:CURR:REF</span><span class="briefScpi">Sets relative current measurement offset</span>
      </div>
      <div class="content">
        <div class="scpiLong">SENSe<div class="tooltip" data-id="147">#</div>:CURRent[:DC]:REFerence <div class="tooltip" data-id="148">&lt;refCurr&gt;</div></div>
        <div class="sufixDocu">Suffixe<ul>
          <li id="147"><span class="param">Sufix 1</span><span class="paramType">uint</span> <span class="paramDesc">channel the sensor head is connected to. Here always 1 (default).</span></li>
        </ul></div>

        <div class="paramDocu">Parameters<ul>
              <li id="148"><span class="param">refCurr</span><span class="paramType">float,MIN,MAX,DEF</span> <span class="paramDesc">reference current in Ampere. MIN, DEF or MAX to set special values.</span></li>
        </ul></div>

        <div class="details">Use this command to define a current offset in Ampere for relative measurements. To switch to relative measurements ensure delta mode is also enabled by <a class="linkSCPI" title="Enables current delta mode" href="#SENS#:CURR:REF:STAT">SENS#:CURR:REF:STAT</a>. All non zero parameter values result in a relative measurement. To get relative measurement results call <a class="linkSCPI" title="Measures the sensor averaged current" href="#MEAS:CURR?">MEAS:CURR?</a> afterwards. The fast measurement stream does not support relative measurements. Relative current offset parameter is not stored persistently. It will be reset to zero after reboot!</div>
        <div class="example">&gt;&gt; SENS:CURR:REF 0.3</div>
        <div class="note">Delta current measurement is only available for Photodiode sensors.</div>

      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="SENS">
        <span class="scpi" id="SENS#:CURR:REF?">SENS#:CURR:REF?</span><span class="briefScpi">Gets relative current measurement offset</span>
      </div>
      <div class="content">
        <div class="scpiLong">SENSe<div class="tooltip" data-id="149">#</div>:CURRent[:DC]:REFerence? <div class="tooltip" data-id="150">&lt;special&gt;</div></div>
        <div class="sufixDocu">Suffixe<ul>
          <li id="149"><span class="param">Sufix 1</span><span class="paramType">uint</span> <span class="paramDesc">channel the sensor head is connected to. Here always 1 (default).</span></li>
        </ul></div>

        <div class="paramDocu">Parameters<ul>
           <li id="150"><span class="optParam">special</span><span class="paramType">MIN,MAX,DEF</span> <span class="paramDesc">Optional! nothing to query actual value. MIN, MAX or DEF to query special offset values.</span></li>
        </ul></div>

        <div class="resultDocu">Response<ul>
          <li><span class="param">refCurr</span><span class="paramType">float</span> <span class="paramDesc">reference current in Ampere.</span></li>
        </ul></div>

        <div class="details">Use this command to query the actual current offset in Ampere for relative measurements. Even if a non zero offset has been set it will not be used until delta mode is enabled by command <a class="linkSCPI" title="Enables current delta mode" href="#SENS#:CURR:REF:STAT">SENS#:CURR:REF:STAT</a>. Relative current offset parameter is not stored persistently. It will be reset to zero after reboot!</div>
        <div class="example">&gt;&gt; SENS:CURR:REF?<br />&lt;&lt; 0.3</div>
        <div class="note">Delta current measurement is only available for Photodiode sensors.</div>

      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="SENS">
        <span class="scpi" id="SENS#:CURR:REF:STAT">SENS#:CURR:REF:STAT</span><span class="briefScpi">Enables current delta mode</span>
      </div>
      <div class="content">
        <div class="scpiLong">SENSe<div class="tooltip" data-id="151">#</div>:CURRent[:DC]:REFerence:STATe <div class="tooltip" data-id="152">&lt;enable&gt;</div></div>
        <div class="sufixDocu">Suffixe<ul>
          <li id="151"><span class="param">Sufix 1</span><span class="paramType">uint</span> <span class="paramDesc">channel the sensor head is connected to. Here always 1 (default).</span></li>
        </ul></div>

        <div class="paramDocu">Parameters<ul>
              <li id="152"><span class="param">enable</span><span class="paramType">bool</span> <span class="paramDesc">1 to enable delta mode, 0 otherwise.</span></li>
        </ul></div>

        <div class="details">Use this command to enable current delta mode. If delta mode is enabled and a non zero offset has been set by <a class="linkSCPI" title="Sets relative current measurement offset" href="#SENS#:CURR:REF">SENS#:CURR:REF</a> previously, <a class="linkSCPI" title="Measures the sensor averaged current" href="#MEAS:CURR?">MEAS:CURR?</a> will return relative measurements. If delta mode gets disabled again the measurement command will return absolute measurements again even if a non zero offset has been set previously. Delta mode is always disabled after reboot or sensor change.</div>
        <div class="example">&gt;&gt; SENS:CURR:REF:STAT 1</div>
        <div class="note">Delta current measurement is only available for Photodiode sensors.</div>

      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="SENS">
        <span class="scpi" id="SENS#:CURR:REF:STAT?">SENS#:CURR:REF:STAT?</span><span class="briefScpi">Tests if current delta mode is enabled</span>
      </div>
      <div class="content">
        <div class="scpiLong">SENSe<div class="tooltip" data-id="153">#</div>:CURRent[:DC]:REFerence:STATe?</div>
        <div class="sufixDocu">Suffixe<ul>
          <li id="153"><span class="param">Sufix 1</span><span class="paramType">uint</span> <span class="paramDesc">channel the sensor head is connected to. Here always 1 (default).</span></li>
        </ul></div>

        <div class="resultDocu">Response<ul>
          <li><span class="param">enable</span><span class="paramType">bool</span> <span class="paramDesc">1 if delta mode enabled, 0 otherwise.</span></li>
        </ul></div>

        <div class="details">Use this command to test if current delta mode is enabled. If delta mode is enabled and a non zero offset has been set by <a class="linkSCPI" title="Sets relative current measurement offset" href="#SENS#:CURR:REF">SENS#:CURR:REF</a> previously, <a class="linkSCPI" title="Measures the sensor averaged current" href="#MEAS:CURR?">MEAS:CURR?</a> will return relative measurements.</div>
        <div class="example">&gt;&gt; SENS:CURR:REF:STAT?<br />&lt;&lt; 1</div>
        <div class="note">Delta current measurement is only available for Photodiode sensors.</div>

      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="SENS">
        <span class="scpi" id="SENS#:POW:RANG">SENS#:POW:RANG</span><span class="briefScpi">Sets a measurement range for given power</span>
      </div>
      <div class="content">
        <div class="scpiLong">SENSe<div class="tooltip" data-id="71">#</div>:POWer[:DC]:RANGe[:UPPer] <div class="tooltip" data-id="72">&lt;power&gt;</div></div>
        <div class="sufixDocu">Suffixe<ul>
          <li id="71"><span class="param">Sufix 1</span><span class="paramType">uint</span> <span class="paramDesc">channel the sensor head is connected to. Here always 1 (default).</span></li>
        </ul></div>

        <div class="paramDocu">Parameters<ul>
              <li id="72"><span class="param">power</span><span class="paramType">float,MIN,MAX</span> <span class="paramDesc">Set gain to measure given power best. MIN or MAX for most or least sensitive gain.</span></li>
        </ul></div>

        <div class="details">This command sets a manual range for given power in W. Setting a manual range will automatically disable auto ranging see <a class="linkSCPI" title="Sets power measurement auto ranging" href="#SENS#:POW:RANG:AUTO">SENS#:POW:RANG:AUTO</a>. Changing the measurement range will always interrupt the measurement for up to 10 milliseconds to allow the analogue hardware to settle. After this time measurement is continued automatically. For MIN you set the most sensitive gain level. For MAX you set least sensitive gain level. Manual range is stored persistently and gets restored after reboot. You may check for ranging flag bit 2 in the Operation Status register <a class="linkSCPI" title="Queries the SCPI Operation Condition register" href="#STAT:OPER:COND?">STAT:OPER:COND?</a> so see if ranging is complete.</div>
        <div class="example">&gt;&gt; SENS:CURR:RANG 2.56e-5</div>
        <div class="note">Photodiode and Thermopile sensors only!</div>
        <div class="info">Setting a manual range automatically disables auto ranging. See <a class="linkSCPI" title="Sets power measurement auto ranging" href="#SENS#:POW:RANG:AUTO">SENS#:POW:RANG:AUTO</a>.</div>
      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="SENS">
        <span class="scpi" id="SENS#:POW:RANG?">SENS#:POW:RANG?</span><span class="briefScpi">Returns measurement range for power measurement</span>
      </div>
      <div class="content">
        <div class="scpiLong">SENSe<div class="tooltip" data-id="69">#</div>:POWer[:DC]:RANGe[:UPPer]? <div class="tooltip" data-id="70">&lt;power&gt;</div></div>
        <div class="sufixDocu">Suffixe<ul>
          <li id="69"><span class="param">Sufix 1</span><span class="paramType">uint</span> <span class="paramDesc">channel the sensor head is connected to. Here always 1 (default).</span></li>
        </ul></div>

        <div class="paramDocu">Parameters<ul>
           <li id="70"><span class="optParam">power</span><span class="paramType">float,MIN,MAX,AUTO</span> <span class="paramDesc">Optional! power to get range maximum for. Special parameter to query special range info.</span></li>
        </ul></div>

        <div class="resultDocu">Response<ul>
          <li><span class="param">rangeMax</span><span class="paramType">float</span> <span class="paramDesc">maximum power in Watt supported by measurement range. For special parameter AUTO it will be a list.</span></li>
        </ul></div>

        <div class="details">This command returns measurement range information for power measurement in Watt. If no parameter has been provided the actual used range maximum power is returned. When called with a power in Watt float parameter the function will return the measurement range maximum fitting given power best. The following special parameters may be used: <ul class="detailListing"> <li><b>MIN</b> maximum power, supported by the most sensitive gain level.</li> <li><b>MAX</b> maximum power, supported by the least sensitive gain level.</li> <li><b>AUTO</b> list of maximum power for all measurement ranges. Starts with the least sensitive gain level.</li> </ul></div>
        <div class="example">&gt;&gt; SENS:POW:RANG? 2-e5<br />&lt;&lt; 3.23-e5</div>
        <div class="note">Photodiode and Thermopile sensors only!</div>

      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="SENS">
        <span class="scpi" id="SENS#:POW:RANG:AUTO">SENS#:POW:RANG:AUTO</span><span class="briefScpi">Sets power measurement auto ranging</span>
      </div>
      <div class="content">
        <div class="scpiLong">SENSe<div class="tooltip" data-id="56">#</div>:POWer[:DC]:RANGe:AUTO <div class="tooltip" data-id="57">&lt;state&gt;</div></div>
        <div class="sufixDocu">Suffixe<ul>
          <li id="56"><span class="param">Sufix 1</span><span class="paramType">uint</span> <span class="paramDesc">channel the sensor head is connected to. Here always 1 (default).</span></li>
        </ul></div>

        <div class="paramDocu">Parameters<ul>
              <li id="57"><span class="param">state</span><span class="paramType">bool</span> <span class="paramDesc">1 to enable auto ranging, 0 otherwise.</span></li>
        </ul></div>

        <div class="details">This command enables or disables auto ranging feature for power measurement. If auto ranging is active the Powermeter compares the measured signal to the currently used measure range. If the signal is out of optimal range, the Powermeter automatically changes the measurement range. Changing the measurement range will always interrupt the measurement for up to 10 milliseconds to allow the analogue hardware to settle. After this time measurement is continued automatically. You can ask for the actual range by using command <a class="linkSCPI" title="Returns measurement range for power measurement" href="#SENS#:POW:RANG?">SENS#:POW:RANG?</a>. If you set an manual range like command <a class="linkSCPI" title="Sets a measurement range for given power" href="#SENS#:POW:RANG">SENS#:POW:RANG</a> auto ranging gets disabled automatically. Auto range enable state is stored persistently and restored after reboot. If you change sensor type auto ranging is enabled by default. You may check for ranging flag bit 2 in the Operation Status register <a class="linkSCPI" title="Queries the SCPI Operation Condition register" href="#STAT:OPER:COND?">STAT:OPER:COND?</a> so see if ranging took place complete.</div>
        <div class="example">&gt;&gt; SENS:POW:RANG:AUTO 1</div>
        <div class="info">For scope mode it makes sense to disable auto ranging to get a seamless result stream.</div>
      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="SENS">
        <span class="scpi" id="SENS#:POW:RANG:AUTO?">SENS#:POW:RANG:AUTO?</span><span class="briefScpi">Tests if power auto ranging is enabled</span>
      </div>
      <div class="content">
        <div class="scpiLong">SENSe<div class="tooltip" data-id="55">#</div>:POWer[:DC]:RANGe:AUTO?</div>
        <div class="sufixDocu">Suffixe<ul>
          <li id="55"><span class="param">Sufix 1</span><span class="paramType">uint</span> <span class="paramDesc">channel the sensor head is connected to. Here always 1 (default).</span></li>
        </ul></div>

        <div class="resultDocu">Response<ul>
          <li><span class="param">state</span><span class="paramType">bool</span> <span class="paramDesc">1 if auto ranging is active, 0 otherwise.</span></li>
        </ul></div>

        <div class="details">This command tests if auto ranging is enabled for power measurement. For closer details read <a class="linkSCPI" title="Sets power measurement auto ranging" href="#SENS#:POW:RANG:AUTO">SENS#:POW:RANG:AUTO</a>. You can query the currently used range by using <a class="linkSCPI" title="Returns measurement range for power measurement" href="#SENS#:POW:RANG?">SENS#:POW:RANG?</a>. Auto range enable state is stored persistently and restored after reboot.</div>
        <div class="example">&gt;&gt; SENS:POW:RANG:AUTO?<br />&lt;&lt; 0</div>

      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="SENS">
        <span class="scpi" id="SENS#:POW:REF">SENS#:POW:REF</span><span class="briefScpi">Sets relative power measurement offset</span>
      </div>
      <div class="content">
        <div class="scpiLong">SENSe<div class="tooltip" data-id="154">#</div>:POWer[:DC]:REFerence <div class="tooltip" data-id="155">&lt;refPow&gt;</div></div>
        <div class="sufixDocu">Suffixe<ul>
          <li id="154"><span class="param">Sufix 1</span><span class="paramType">uint</span> <span class="paramDesc">channel the sensor head is connected to. Here always 1 (default).</span></li>
        </ul></div>

        <div class="paramDocu">Parameters<ul>
              <li id="155"><span class="param">refPow</span><span class="paramType">float,MIN,MAX,DEF</span> <span class="paramDesc">reference power in W. MIN, DEF or MAX to set special values.</span></li>
        </ul></div>

        <div class="details">Use this command to define a power offset in Watt for relative measurements. To switch to relative measurements ensure delta mode is also enabled by <a class="linkSCPI" title="Enables power delta mode" href="#SENS#:POW:REF:STAT">SENS#:POW:REF:STAT</a>. All non zero parameter values result in a relative measurement. To get relative measurement results call <a class="linkSCPI" title="Measures the sensor averaged power in Watt or dBm" href="#MEAS?">MEAS?</a> afterwards. The fast measurement stream does not support relative measurements. Relative power offset parameter is not stored persistently. It will be reset to zero after reboot!</div>
        <div class="example">&gt;&gt; SENS:POW:REF 2</div>
        <div class="note">Delta power measurement is only available for Photodiode sensors.</div>

      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="SENS">
        <span class="scpi" id="SENS#:POW:REF?">SENS#:POW:REF?</span><span class="briefScpi">Gets relative power measurement offset</span>
      </div>
      <div class="content">
        <div class="scpiLong">SENSe<div class="tooltip" data-id="156">#</div>:POWer[:DC]:REFerence? <div class="tooltip" data-id="157">&lt;special&gt;</div></div>
        <div class="sufixDocu">Suffixe<ul>
          <li id="156"><span class="param">Sufix 1</span><span class="paramType">uint</span> <span class="paramDesc">channel the sensor head is connected to. Here always 1 (default).</span></li>
        </ul></div>

        <div class="paramDocu">Parameters<ul>
           <li id="157"><span class="optParam">special</span><span class="paramType">MIN,MAX,DEF</span> <span class="paramDesc">Optional! nothing to query actual value. MIN, MAX or DEF to query special offset values.</span></li>
        </ul></div>

        <div class="resultDocu">Response<ul>
          <li><span class="param">refPow</span><span class="paramType">float</span> <span class="paramDesc">reference power in W.</span></li>
        </ul></div>

        <div class="details">Use this command to query the actual power offset in Watt for relative measurements. Even if a non zero offset has been set it will not be used until delta mode is enabled by command <a class="linkSCPI" title="Enables power delta mode" href="#SENS#:POW:REF:STAT">SENS#:POW:REF:STAT</a>. Relative power offset parameter is not stored persistently. It will be reset to zero after reboot!</div>
        <div class="example">&gt;&gt; SENS:POW:REF?<br />&lt;&lt; 2</div>
        <div class="note">Delta power measurement is only available for Photodiode sensors.</div>

      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="SENS">
        <span class="scpi" id="SENS#:POW:REF:STAT">SENS#:POW:REF:STAT</span><span class="briefScpi">Enables power delta mode</span>
      </div>
      <div class="content">
        <div class="scpiLong">SENSe<div class="tooltip" data-id="158">#</div>:POWer[:DC]:REFerence:STATe <div class="tooltip" data-id="159">&lt;enable&gt;</div></div>
        <div class="sufixDocu">Suffixe<ul>
          <li id="158"><span class="param">Sufix 1</span><span class="paramType">uint</span> <span class="paramDesc">channel the sensor head is connected to. Here always 1 (default).</span></li>
        </ul></div>

        <div class="paramDocu">Parameters<ul>
              <li id="159"><span class="param">enable</span><span class="paramType">bool</span> <span class="paramDesc">1 to enable delta mode, 0 otherwise.</span></li>
        </ul></div>

        <div class="details">Use this command to enable power delta mode. If delta mode is enabled and a non zero offset has been set by <a class="linkSCPI" title="Sets relative power measurement offset" href="#SENS#:POW:REF">SENS#:POW:REF</a> previously, <a class="linkSCPI" title="Measures the sensor averaged power in Watt or dBm" href="#MEAS?">MEAS?</a> will return relative measurements. If delta mode gets disabled again the measurement command will return absolute measurements again even if a non zero offset has been set previously. Delta mode is always disabled after reboot or sensor change.</div>
        <div class="example">&gt;&gt; SENS:POW:REF:STAT 1</div>
        <div class="note">Delta power measurement is only available for Photodiode sensors.</div>

      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="SENS">
        <span class="scpi" id="SENS#:POW:REF:STAT?">SENS#:POW:REF:STAT?</span><span class="briefScpi">Tests if power delta mode is enabled</span>
      </div>
      <div class="content">
        <div class="scpiLong">SENSe<div class="tooltip" data-id="160">#</div>:POWer[:DC]:REFerence:STATe?</div>
        <div class="sufixDocu">Suffixe<ul>
          <li id="160"><span class="param">Sufix 1</span><span class="paramType">uint</span> <span class="paramDesc">channel the sensor head is connected to. Here always 1 (default).</span></li>
        </ul></div>

        <div class="resultDocu">Response<ul>
          <li><span class="param">enable</span><span class="paramType">bool</span> <span class="paramDesc">1 if delta mode enabled, 0 otherwise.</span></li>
        </ul></div>

        <div class="details">Use this command to test if power delta mode is enabled. If delta mode is enabled and a non zero offset has been set by <a class="linkSCPI" title="Sets relative power measurement offset" href="#SENS#:POW:REF">SENS#:POW:REF</a> previously, <a class="linkSCPI" title="Measures the sensor averaged current" href="#MEAS:CURR?">MEAS:CURR?</a> will return relative measurements.</div>
        <div class="example">&gt;&gt; SENS:POW:REF:STAT?<br />&lt;&lt; 1</div>
        <div class="note">Delta power measurement is only available for Photodiode sensors.</div>

      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="SENS">
        <span class="scpi" id="SENS#:POW:UNIT">SENS#:POW:UNIT</span><span class="briefScpi">Selects the power unit</span>
      </div>
      <div class="content">
        <div class="scpiLong">SENSe<div class="tooltip" data-id="78">#</div>:POWer[:DC]:UNIT <div class="tooltip" data-id="79">&lt;unit&gt;</div></div>
        <div class="sufixDocu">Suffixe<ul>
          <li id="78"><span class="param">Sufix 1</span><span class="paramType">uint</span> <span class="paramDesc">channel the sensor head is connected to. Here always 1 (default).</span></li>
        </ul></div>

        <div class="paramDocu">Parameters<ul>
              <li id="79"><span class="param">unit</span><span class="paramType">W, DBM</span> <span class="paramDesc">choose power unit between Watt or dBm.</span></li>
        </ul></div>

        <div class="details">Use this command to change the power unit between Watt and dBm. The unit affects the results of the <a class="linkSCPI" title="Measures the sensor averaged power in Watt or dBm" href="#MEAS?">MEAS?</a> command.</div>
        <div class="example">&gt;&gt; SENS:POW:UNIT DBM</div>

      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="SENS">
        <span class="scpi" id="SENS#:POW:UNIT?">SENS#:POW:UNIT?</span><span class="briefScpi">Returns the power unit</span>
      </div>
      <div class="content">
        <div class="scpiLong">SENSe<div class="tooltip" data-id="77">#</div>:POWer[:DC]:UNIT?</div>
        <div class="sufixDocu">Suffixe<ul>
          <li id="77"><span class="param">Sufix 1</span><span class="paramType">uint</span> <span class="paramDesc">channel the sensor head is connected to. Here always 1 (default).</span></li>
        </ul></div>

        <div class="resultDocu">Response<ul>
          <li><span class="param">unit</span><span class="paramType">W, DBM</span> <span class="paramDesc">power unit.</span></li>
        </ul></div>

        <div class="details">Use this command to query the actual used power unit. For closer details read command <a class="linkSCPI" title="Selects the power unit" href="#SENS#:POW:UNIT">SENS#:POW:UNIT</a>.</div>
        <div class="example">&gt;&gt; SENS:POW:UNIT?<br />&lt;&lt; DBM</div>

      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="SENS">
        <span class="scpi" id="SENS#:REI">SENS#:REI</span><span class="briefScpi">Reconnect connected sensor</span>
      </div>
      <div class="content">
        <div class="scpiLong">SENSe<div class="tooltip" data-id="123">#</div>:REInit</div>
        <div class="sufixDocu">Suffixe<ul>
          <li id="123"><span class="param">Sufix 1</span><span class="paramType">uint</span> <span class="paramDesc">channel the sensor head is connected to. Here always 1 (default).</span></li>
        </ul></div>

        <div class="details">Use this command to reconnect the connected sensor. The command simulates a unplug and plug in of the sensor connector. This final step is mandatory after changing the customer calibration for the sensor currently plugged in. Calling this command will interrupt the ongoing measurement.</div>
        <div class="example">&gt;&gt; SENS:REI</div>

      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="SENS">
        <span class="scpi" id="SENS#:RES:DATA?">SENS#:RES:DATA?</span><span class="briefScpi">Returns the resistance of external NTC</span>
      </div>
      <div class="content">
        <div class="scpiLong">SENSe<div class="tooltip" data-id="124">#</div>:RESistance:DATA?</div>
        <div class="sufixDocu">Suffixe<ul>
          <li id="124"><span class="param">Sufix 1</span><span class="paramType">uint</span> <span class="paramDesc">external   5 for NTC</span></li>
        </ul></div>

        <div class="resultDocu">Response<ul>
          <li><span class="param">resistance</span><span class="paramType">float</span> <span class="paramDesc">last measured resistance in Ohm.</span></li>
        </ul></div>

        <div class="details">Use this command to query the recent measured resistance of the external NTC in Ohm. The Powermeter updates the resistance at 1 Hz. If you query the resistance faster you will receive the same resistance multiple times. To query the sensor head NTC resistance use <a class="linkSCPI" title="Measures sensor head NTC resistance" href="#MEAS:RES?">MEAS:RES?</a>. If you use a special NTC temperature sensor ensure Beta <a class="linkSCPI" title="Sets external thermistor equation Beta parameter" href="#SENS#:CORR:COEF:BETA?">SENS#:CORR:COEF:BETA?</a> and R0 <a class="linkSCPI" title="Gets external thermistor equation R0 parameter" href="#SENS#:CORR:COEF:RES?">SENS#:CORR:COEF:RES?</a> parameters are correct. To get the external NTC temperature use <a class="linkSCPI" title="Returns the temperature of external NTC sensor" href="#SENS#:TEMP:DATA?">SENS#:TEMP:DATA?</a>.</div>
        <div class="example">&gt;&gt; SENS:RES:DATA?<br />&lt;&lt; 5988.23</div>

      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="SENS">
        <span class="scpi" id="SENS#:SER:JAB">SENS#:SER:JAB</span><span class="briefScpi">Enables/Disables serial jabber mode</span>
      </div>
      <div class="content">
        <div class="scpiLong">SENSe<div class="tooltip" data-id="125">#</div>:SERial:JABber <div class="tooltip" data-id="126">&lt;state&gt;</div></div>
        <div class="sufixDocu">Suffixe<ul>
          <li id="125"><span class="param">Sufix 1</span><span class="paramType">uint</span> <span class="paramDesc">channel the sensor head is connected to. Here always 1 (default).</span></li>
        </ul></div>

        <div class="paramDocu">Parameters<ul>
              <li id="126"><span class="param">state</span><span class="paramType">bool</span> <span class="paramDesc">1 to enable jabber mode. 0 to disable.</span></li>
        </ul></div>

        <div class="details">Use this command to enable or disable serial interface jabber mode. When jabber mode is enabled all measurement results are directly output on the serial interface WITHOUT the requirement to <a class="linkSCPI" title="Blocks until previously initiated measurement is complete and returns result finally" href="#FETC?">FETC?</a> results with a own SCPI command. Normally it makes sense to start a continous measurement by using <a class="linkSCPI" title="Starts a new continuos measurement for a previously configured unit" href="#INIT:CONT">INIT:CONT</a>. Once the continous measurement has been started ensure to call <a class="linkSCPI" title="Aborts any previously started measurement" href="#ABOR">ABOR</a> finally to stop output of jabber mode. Once the measurement has been stopped, you might also disable jabber mode again using this command.</div>
        <div class="example">&gt;&gt; SENS:SER:JAB 1</div>
        <div class="note">When jabber mode is enabled, <a class="linkSCPI" title="Starts and queries measurement result for a previously configured unit" href="#READ?">READ?</a>, <a class="linkSCPI" title="Blocks until previously initiated measurement is complete and returns result finally" href="#FETC?">FETC?</a> and all measurement commands e.g. <a class="linkSCPI" title="Measures the sensor averaged power in Watt or dBm" href="#MEAS?">MEAS?</a> are disfunctional.</div>

      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="SENS">
        <span class="scpi" id="SENS#:TEMP:DATA?">SENS#:TEMP:DATA?</span><span class="briefScpi">Returns the temperature of external NTC sensor</span>
      </div>
      <div class="content">
        <div class="scpiLong">SENSe<div class="tooltip" data-id="122">#</div>:TEMPerature:DATA?</div>
        <div class="sufixDocu">Suffixe<ul>
          <li id="122"><span class="param">Sufix 1</span><span class="paramType">uint</span> <span class="paramDesc">external NTC channel number 5 for NTC</span></li>
        </ul></div>

        <div class="resultDocu">Response<ul>
          <li><span class="param">temperature</span><span class="paramType">float</span> <span class="paramDesc">last measured temperature in degree Celsius.</span></li>
        </ul></div>

        <div class="details">Use this command to query the recent measured temperature of the external NTC temperature sensor in degree Celsius. The Powermeter updates both temperatures a 1 Hz. If you query the temperature faster you will receive the same temperature multiple times. To query the sensor head temperature use <a class="linkSCPI" title="Measures sensor head temperature in °C" href="#MEAS:TEMP?">MEAS:TEMP?</a>. If you use a special NTC temperature sensor ensure Beta <a class="linkSCPI" title="Sets external thermistor equation Beta parameter" href="#SENS#:CORR:COEF:BETA?">SENS#:CORR:COEF:BETA?</a> and R0 <a class="linkSCPI" title="Gets external thermistor equation R0 parameter" href="#SENS#:CORR:COEF:RES?">SENS#:CORR:COEF:RES?</a> parameters are correct. To get the external NTC resistance use <a class="linkSCPI" title="Returns the resistance of external NTC" href="#SENS#:RES:DATA?">SENS#:RES:DATA?</a>. Before calling this function you may check if the required sensor is connected by reading Auxiliary register <a class="linkSCPI" title="Queries the SCPI Auxiliary Condition register" href="#STAT:AUX:COND?">STAT:AUX:COND?</a>. </div>
        <div class="example">&gt;&gt; SENS3:TEMP:DATA?<br />&lt;&lt; 21.32</div>

      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="SENS">
        <span class="scpi" id="SENS#:VOLT:RANG">SENS#:VOLT:RANG</span><span class="briefScpi">Sets a measurement range for given voltage</span>
      </div>
      <div class="content">
        <div class="scpiLong">SENSe<div class="tooltip" data-id="75">#</div>:VOLTage[:DC]:RANGe[:UPPer] <div class="tooltip" data-id="76">&lt;voltage&gt;</div></div>
        <div class="sufixDocu">Suffixe<ul>
          <li id="75"><span class="param">Sufix 1</span><span class="paramType">uint</span> <span class="paramDesc">channel the sensor head is connected to. Here always 1 (default).</span></li>
        </ul></div>

        <div class="paramDocu">Parameters<ul>
              <li id="76"><span class="param">voltage</span><span class="paramType">float,MIN,MAX</span> <span class="paramDesc">Set gain to measure given voltage best. MIN or MAX for most or least sensitive gain.</span></li>
        </ul></div>

        <div class="details">This command sets a manual range for given voltage in W. Changing the measurement range will always interrupt the measurement for up to 10 milliseconds to allow the analogue hardware to settle. After this time measurement is continued automatically. For MIN you set the most sensitive gain level. For MAX you set least sensitive gain level. Manual range is stored persistently and gets restored after reboot. You may check for ranging flag bit 2 in the Operation Status register <a class="linkSCPI" title="Queries the SCPI Operation Condition register" href="#STAT:OPER:COND?">STAT:OPER:COND?</a> so see if ranging took place complete.</div>
        <div class="example">&gt;&gt; SENS:CURR:RANG 2.56e-5</div>
        <div class="note">Thermopile sensors only!</div>
        <div class="info">Setting a manual range automatically disables auto ranging for Thermopile. See <a class="linkSCPI" title="Sets voltage measurement auto ranging" href="#SENS#:VOLT:RANG:AUTO">SENS#:VOLT:RANG:AUTO</a>.</div>
      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="SENS">
        <span class="scpi" id="SENS#:VOLT:RANG?">SENS#:VOLT:RANG?</span><span class="briefScpi">Returns measurement range for voltage measurement</span>
      </div>
      <div class="content">
        <div class="scpiLong">SENSe<div class="tooltip" data-id="73">#</div>:VOLTage[:DC]:RANGe[:UPPer]? <div class="tooltip" data-id="74">&lt;voltage&gt;</div></div>
        <div class="sufixDocu">Suffixe<ul>
          <li id="73"><span class="param">Sufix 1</span><span class="paramType">uint</span> <span class="paramDesc">channel the sensor head is connected to. Here always 1 (default).</span></li>
        </ul></div>

        <div class="paramDocu">Parameters<ul>
           <li id="74"><span class="optParam">voltage</span><span class="paramType">float,MIN,MAX,AUTO</span> <span class="paramDesc">Optional! voltage to get range maximum for. Special parameter to query special range info.</span></li>
        </ul></div>

        <div class="resultDocu">Response<ul>
          <li><span class="param">rangeMax</span><span class="paramType">float</span> <span class="paramDesc">maximum voltage in Volt supported by measurement range. For special parameter AUTO it will be a list.</span></li>
        </ul></div>

        <div class="details">This command returns measurement range information for voltage measurement in Volt. If no parameter has been provided the actual used range maximum voltage is returned. When called with a voltage float parameter the function will return the measurement range maximum fitting given voltage best. The following special parameters may be used: <ul class="detailListing"> <li><b>MIN</b> maximum voltage, supported by the most sensitive gain level.</li> <li><b>MAX</b> maximum voltage, supported by the least sensitive gain level.</li> <li><b>AUTO</b> list of maximum voltage for all measurement ranges. Starts with the least sensitive gain level.</li> </ul></div>
        <div class="example">&gt;&gt; SENS:VOLT:RANG? AUTO<br />&lt;&lt; 98, 0.98, 0.98e-1, 0.98e-2, 0.98e-3, 0.98e-4, 0.98e-5</div>
        <div class="note">Thermopile sensors only!</div>

      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="SENS">
        <span class="scpi" id="SENS#:VOLT:RANG:AUTO">SENS#:VOLT:RANG:AUTO</span><span class="briefScpi">Sets voltage measurement auto ranging</span>
      </div>
      <div class="content">
        <div class="scpiLong">SENSe<div class="tooltip" data-id="59">#</div>:VOLTage[:DC]:RANGe:AUTO <div class="tooltip" data-id="60">&lt;state&gt;</div></div>
        <div class="sufixDocu">Suffixe<ul>
          <li id="59"><span class="param">Sufix 1</span><span class="paramType">uint</span> <span class="paramDesc">channel the sensor head is connected to. Here always 1 (default).</span></li>
        </ul></div>

        <div class="paramDocu">Parameters<ul>
              <li id="60"><span class="param">state</span><span class="paramType">bool</span> <span class="paramDesc">1 to enable auto ranging, 0 otherwise.</span></li>
        </ul></div>

        <div class="details">This command enables or disables auto ranging feature for thermopile voltage measurement. If auto ranging is active the Powermeter compares the measured signal to the currently used measure range. If the signal is out of optimal range, the Powermeter automatically changes the measurement range. Changing the measurement range will always interrupt the measurement for up to 10 milliseconds to allow the analogue hardware to settle. After this time measurement is continued automatically. You can ask for the actual range by using command <a class="linkSCPI" title="Returns measurement range for voltage measurement" href="#SENS#:VOLT:RANG?">SENS#:VOLT:RANG?</a>. If you set an manual range like command <a class="linkSCPI" title="Sets a measurement range for given voltage" href="#SENS#:VOLT:RANG">SENS#:VOLT:RANG</a> auto ranging gets disabled automatically. Auto range enable state is stored persistently and restored after reboot. If you change sensor type auto ranging is enabled by default. You may check for ranging flag bit 2 in the Operation Status register <a class="linkSCPI" title="Queries the SCPI Operation Condition register" href="#STAT:OPER:COND?">STAT:OPER:COND?</a> so see if ranging took place complete.</div>
        <div class="example">&gt;&gt; SENS:VOLT:RANG:AUTO 1</div>
        <div class="info">For scope mode it makes sense to disable auto ranging to get a seamless result stream.</div>
      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="SENS">
        <span class="scpi" id="SENS#:VOLT:RANG:AUTO?">SENS#:VOLT:RANG:AUTO?</span><span class="briefScpi">Tests if voltage auto ranging is enabled</span>
      </div>
      <div class="content">
        <div class="scpiLong">SENSe<div class="tooltip" data-id="58">#</div>:VOLTage[:DC]:RANGe:AUTO?</div>
        <div class="sufixDocu">Suffixe<ul>
          <li id="58"><span class="param">Sufix 1</span><span class="paramType">uint</span> <span class="paramDesc">channel the sensor head is connected to. Here always 1 (default).</span></li>
        </ul></div>

        <div class="resultDocu">Response<ul>
          <li><span class="param">state</span><span class="paramType">bool</span> <span class="paramDesc">1 if auto ranging is active, 0 otherwise.</span></li>
        </ul></div>

        <div class="details">This command tests if auto ranging is enabled for voltage measurement. For closer details read <a class="linkSCPI" title="Sets voltage measurement auto ranging" href="#SENS#:VOLT:RANG:AUTO">SENS#:VOLT:RANG:AUTO</a>. You can query the currently used range by using <a class="linkSCPI" title="Returns measurement range for voltage measurement" href="#SENS#:VOLT:RANG?">SENS#:VOLT:RANG?</a>. Auto range enable state is stored persistently and restored after reboot.</div>
        <div class="example">&gt;&gt; SENS:VOLT:RANG:AUTO?<br />&lt;&lt; 0</div>

      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="SENS">
        <span class="scpi" id="SENS#:VOLT:REF">SENS#:VOLT:REF</span><span class="briefScpi">Sets relative voltage measurement offset</span>
      </div>
      <div class="content">
        <div class="scpiLong">SENSe<div class="tooltip" data-id="161">#</div>:VOLTage[:DC]:REFerence <div class="tooltip" data-id="162">&lt;refVolt&gt;</div></div>
        <div class="sufixDocu">Suffixe<ul>
          <li id="161"><span class="param">Sufix 1</span><span class="paramType">uint</span> <span class="paramDesc">channel the sensor head is connected to. Here always 1 (default).</span></li>
        </ul></div>

        <div class="paramDocu">Parameters<ul>
              <li id="162"><span class="param">refVolt</span><span class="paramType">float,MIN,MAX,DEF</span> <span class="paramDesc">reference energy in W. MIN, DEF or MAX to set special values.</span></li>
        </ul></div>

        <div class="details">Use this command to define a voltage offset in Volt for relative measurements. To switch to relative measurements ensure delta mode is also enabled by <a class="linkSCPI" title="Enables voltage delta mode" href="#SENS#:VOLT:REF:STAT">SENS#:VOLT:REF:STAT</a>. All non zero parameter values result in a relative measurement. To get relative measurement results call <a class="linkSCPI" title="Measures the sensor averaged voltage" href="#MEAS:VOLT?">MEAS:VOLT?</a> afterwards. The fast measurement stream does not support relative measurements. Relative voltage offset parameter is not stored persistently. It will be reset to zero after reboot!</div>
        <div class="example">&gt;&gt; SENS:VOLT:REF 2</div>
        <div class="note">Delta voltage measurement is only available for Thermopile  sensors.</div>

      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="SENS">
        <span class="scpi" id="SENS#:VOLT:REF?">SENS#:VOLT:REF?</span><span class="briefScpi">Gets relative voltage measurement offset</span>
      </div>
      <div class="content">
        <div class="scpiLong">SENSe<div class="tooltip" data-id="163">#</div>:VOLTage[:DC]:REFerence? <div class="tooltip" data-id="164">&lt;special&gt;</div></div>
        <div class="sufixDocu">Suffixe<ul>
          <li id="163"><span class="param">Sufix 1</span><span class="paramType">uint</span> <span class="paramDesc">channel the sensor head is connected to. Here always 1 (default).</span></li>
        </ul></div>

        <div class="paramDocu">Parameters<ul>
           <li id="164"><span class="optParam">special</span><span class="paramType">MIN,MAX,DEF</span> <span class="paramDesc">Optional! nothing to query actual value. MIN, MAX or DEF to query special offset values.</span></li>
        </ul></div>

        <div class="resultDocu">Response<ul>
          <li><span class="param">refVolt</span><span class="paramType">float</span> <span class="paramDesc">reference voltage Volt.</span></li>
        </ul></div>

        <div class="details">Use this command to query the actual voltage offset in Volt for relative measurements. Even if a non zero offset has been set it will not be used until delta mode is enabled by command <a class="linkSCPI" title="Enables voltage delta mode" href="#SENS#:VOLT:REF:STAT">SENS#:VOLT:REF:STAT</a>. Relative voltage offset parameter is not stored persistently. It will be reset to zero after reboot!</div>
        <div class="example">&gt;&gt; SENS:VOLT:REF?<br />&lt;&lt; 2</div>
        <div class="note">Delta voltage measurement is only available for Thermopile sensors.</div>

      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="SENS">
        <span class="scpi" id="SENS#:VOLT:REF:STAT">SENS#:VOLT:REF:STAT</span><span class="briefScpi">Enables voltage delta mode</span>
      </div>
      <div class="content">
        <div class="scpiLong">SENSe<div class="tooltip" data-id="165">#</div>:VOLTage:REFerence:STATe <div class="tooltip" data-id="166">&lt;enable&gt;</div></div>
        <div class="sufixDocu">Suffixe<ul>
          <li id="165"><span class="param">Sufix 1</span><span class="paramType">uint</span> <span class="paramDesc">channel the sensor head is connected to. Here always 1 (default).</span></li>
        </ul></div>

        <div class="paramDocu">Parameters<ul>
              <li id="166"><span class="param">enable</span><span class="paramType">bool</span> <span class="paramDesc">1 to enable delta mode, 0 otherwise.</span></li>
        </ul></div>

        <div class="details">Use this command to enable voltage delta mode. If delta mode is enabled and a non zero offset has been set by <a class="linkSCPI" title="Sets relative voltage measurement offset" href="#SENS#:VOLT:REF">SENS#:VOLT:REF</a> previously, <a class="linkSCPI" title="Measures the sensor averaged voltage" href="#MEAS:VOLT?">MEAS:VOLT?</a> will return relative measurements. If delta mode gets disabled again the measurement command will return absolute measurements again even if a non zero offset has been set previously. Delta mode is always disabled after reboot or sensor change.</div>
        <div class="example">&gt;&gt; SENS:VOLT:REF:STAT 1</div>
        <div class="note">Delta voltage measurement is only available for Thermopile sensors.</div>

      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="SENS">
        <span class="scpi" id="SENS#:VOLT:REF:STAT?">SENS#:VOLT:REF:STAT?</span><span class="briefScpi">Tests if voltage delta mode is enabled</span>
      </div>
      <div class="content">
        <div class="scpiLong">SENSe<div class="tooltip" data-id="167">#</div>:VOLTage:REFerence:STATe?</div>
        <div class="sufixDocu">Suffixe<ul>
          <li id="167"><span class="param">Sufix 1</span><span class="paramType">uint</span> <span class="paramDesc">channel the sensor head is connected to. Here always 1 (default).</span></li>
        </ul></div>

        <div class="resultDocu">Response<ul>
          <li><span class="param">enable</span><span class="paramType">bool</span> <span class="paramDesc">1 if delta mode enabled, 0 otherwise.</span></li>
        </ul></div>

        <div class="details">Use this command to test if voltage delta mode is enabled. If delta mode is enabled and a non zero offset has been set by <a class="linkSCPI" title="Sets relative voltage measurement offset" href="#SENS#:VOLT:REF">SENS#:VOLT:REF</a> previously, <a class="linkSCPI" title="Measures the sensor averaged voltage" href="#MEAS:VOLT?">MEAS:VOLT?</a> will return relative measurements.</div>
        <div class="example">&gt;&gt; SENS:VOLT:REF:STAT?<br />&lt;&lt; 1</div>
        <div class="note">Delta voltage measurement is only available for Thermopile sensors.</div>

      </div>
    </div>
    <hr>
    <p id="SOUR"><a href="#SOUR:DIG:DATA" data-subsystem="SOUR" class="unfoldSection">Unfold all SOUR Commands</a></p>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="SOUR">
        <span class="scpi" id="SOUR:DIG:DATA">SOUR:DIG:DATA</span><span class="briefScpi">Sets GPIO output pin level to high or low</span>
      </div>
      <div class="content">
        <div class="scpiLong">SOURce:DIGital:DATA <div class="tooltip" data-id="179">&lt;bitmask&gt;</div></div>
        <div class="paramDocu">Parameters<ul>
              <li id="179"><span class="param">bitmask</span><span class="paramType">uint</span> <span class="paramDesc">bitmask with bit set to 1 for high output of pin. Set bit 0 for low output of pin.</span></li>
        </ul></div>

        <div class="details">The command sets the level of all powermeter external IO pins configured as output. A bit 1 will set a high output level for the mapped output pin. A bit 0 will drive output pin low. Max output voltage is 5V. Every output pin drives up 10mA.<br/> A bitmask in general uses the binary number representation to store multiple boolean states. The true state is mapped with a bit set to 1 a false state is represented by a 0 bit. Bit at position 0 is related to IO0, Bit at position 1 is mapped to IO1 and so on. To calculate the bitmask simply sum up the following numbers: <ul class="detailListing"> <li><b>DIO0:</b> 2^0=1</li> <li><b>DIO1:</b> 2^1=2</li> <li><b>DIO2:</b> 2^2=4</li> <li><b>DIO3:</b> 2^3=8</li> </ul> For for example to set DIO1 and DIO3 calculate bitmask = 2 + 8 = 10.</div>
        <div class="example">&gt;&gt; SOUR:DIG:DATA 2</div>
        <div class="note">Input pins use an internal pulldown resistor.</div>

      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="SOUR">
        <span class="scpi" id="SOUR:DIG:DATA?">SOUR:DIG:DATA?</span><span class="briefScpi">Reads back GPIO input/output levels</span>
      </div>
      <div class="content">
        <div class="scpiLong">SOURce:DIGital:DATA?</div>
        <div class="resultDocu">Response<ul>
          <li><span class="param">bitmask</span><span class="paramType">uint</span> <span class="paramDesc">bitmask with bit set to 1 for all inputs or outputs with high level. Bit set to 0 for low level or alternate pin usage.</span></li>
        </ul></div>

        <div class="details">The command reads the level of all powermeter external IO pins configured as output or input and sets bit 1 when pin level is high. For closer details about the bitmask calculations read command <a class="linkSCPI" title="Sets GPIO output pin level to high or low" href="#SOUR:DIG:DATA">SOUR:DIG:DATA</a>. Max output voltage is 5V. Every output pin drives up 10mA. Max input and voltage is 5V. To prevent floating input signals an internal pulldown resistor is connected.</div>
        <div class="example">&gt;&gt; SOUR:DIG:DATA?<br />&lt;&lt; 4</div>
        <div class="note">Input pins use an internal pulldown resistor.</div>

      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="SOUR">
        <span class="scpi" id="SOUR:DIG:ENAB">SOUR:DIG:ENAB</span><span class="briefScpi">Configures all GPIO pin as input our output simultaneously</span>
      </div>
      <div class="content">
        <div class="scpiLong">SOURce:DIGital:ENABle <div class="tooltip" data-id="180">&lt;bitmask&gt;</div></div>
        <div class="paramDocu">Parameters<ul>
              <li id="180"><span class="param">bitmask</span><span class="paramType">uint</span> <span class="paramDesc">bitmask with bit 1 for all pins configured in input direction. Set bit 0 for pin output direction.</span></li>
        </ul></div>

        <div class="details">The command will configure all pins as input where bitmask bit is 1. If bit is 0 the pin is configured as output. A bitmask of 3 for example sets GPIO0 and GPIO1 as input and GPIO2 and GPIO3 as output. For closer details about the bitmask calculations read command <a class="linkSCPI" title="Sets GPIO output pin level to high or low" href="#SOUR:DIG:DATA">SOUR:DIG:DATA</a>. Max output voltage is 5V. Every output pin drives up 10mA. Max input voltage is 5V. To prevent floating input signals an internal pulldown resistor is connected.</div>
        <div class="example">&gt;&gt; SOUR:DIG:ENAB 3</div>
        <div class="note">Input pins use an internal pulldown resistor.</div>

      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="SOUR">
        <span class="scpi" id="SOUR:DIG:ENAB?">SOUR:DIG:ENAB?</span><span class="briefScpi">Gets GPIO input/output pin direction</span>
      </div>
      <div class="content">
        <div class="scpiLong">SOURce:DIGital:ENABle?</div>
        <div class="resultDocu">Response<ul>
          <li><span class="param">bitmask</span><span class="paramType">uint</span> <span class="paramDesc">bitmask with bit set to 1 for all pins configured in input direction. Bit set to 0 for output or alternate usage.</span></li>
        </ul></div>

        <div class="details">The command will return a bitmask with a bit set to 1 for every signal configured as input. A bit value 0 represents a output or alternate pin function. A result of 6 for example says GPIO1 and GPIO2 are configured as input. All other pins are configured as output. For closer details about the bitmask calculations read command <a class="linkSCPI" title="Sets GPIO output pin level to high or low" href="#SOUR:DIG:DATA">SOUR:DIG:DATA</a>.</div>
        <div class="example">&gt;&gt; SOUR:DIG:ENAB?<br />&lt;&lt; 6</div>

      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="SOUR">
        <span class="scpi" id="SOUR#:VOLT?">SOUR#:VOLT?</span><span class="briefScpi">Queries analogue output voltage</span>
      </div>
      <div class="content">
        <div class="scpiLong">SOURce<div class="tooltip" data-id="181">#</div>:VOLTage[:LEVel][:IMMediate][:AMPLitude]? <div class="tooltip" data-id="182">&lt;special&gt;</div></div>
        <div class="sufixDocu">Suffixe<ul>
          <li id="181"><span class="param">Sufix 1</span><span class="paramType">uint</span> <span class="paramDesc">Analogue output channel from 2 to 3. Default 2. 2 is for power. 3 is for position (PM102 only)</span></li>
        </ul></div>

        <div class="paramDocu">Parameters<ul>
           <li id="182"><span class="optParam">special</span><span class="paramType">MIN,MAX</span> <span class="paramDesc">Optional! nothing to query actual output voltage. MIN or MAX to query special limit voltages.</span></li>
        </ul></div>

        <div class="resultDocu">Response<ul>
          <li><span class="param">voltage</span><span class="paramType">float</span> <span class="paramDesc">output voltage on channel</span></li>
        </ul></div>

        <div class="details"> Returns the currently output DAC voltage in Volt for the different channels. The generated analogue output is updated at max 1kHz by the Powermeter. The digital generated analogue output voltage gain is selected by the command <a class="linkSCPI" title="Sets the analogue output gain" href="#SOUR#:VOLT:CORR:SLOP">SOUR#:VOLT:CORR:SLOP</a>.</div>
        <div class="example">&gt;&gt; SOUR:VOLT?<br />&lt;&lt; 2.234</div>

      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="SOUR">
        <span class="scpi" id="SOUR#:VOLT:CORR:SLOP">SOUR#:VOLT:CORR:SLOP</span><span class="briefScpi">Sets the analogue output gain</span>
      </div>
      <div class="content">
        <div class="scpiLong">SOURce<div class="tooltip" data-id="185">#</div>:VOLTage:CORRection:SLOPe[:OUTPut][:MAGNitude] <div class="tooltip" data-id="186">&lt;slope&gt;</div></div>
        <div class="sufixDocu">Suffixe<ul>
          <li id="185"><span class="param">Sufix 1</span><span class="paramType">uint</span> <span class="paramDesc">Analogue output channel from 2 to 3. Default 2. 2 is for power. 3 is for position (PM102 only)</span></li>
        </ul></div>

        <div class="paramDocu">Parameters<ul>
              <li id="186"><span class="param">slope</span><span class="paramType">float,MIN,DEF,MAX</span> <span class="paramDesc">new gain slope for given channel. MIN,DEF or MAX to set predefined values.</span></li>
        </ul></div>

        <div class="details">Use this command to set the analogue output voltage conversion gain in A/W for photodiodes or V/W for thermopile sensors. The gain is used to convert the represented physical unit to the output voltage. It allows amplification (value smaller than 1) and attenuation(value bigger than 1) of the signal. The generated analogue output is updated at max 1kHz by the Powermeter. The signal is stable arccos measurement ranges. For higher bandwidth use the true analogue output of the device. The gain is stored persistently and is automatically used after reboot. To you can test the current output voltage by using command <a class="linkSCPI" title="Queries analogue output voltage" href="#SOUR#:VOLT?">SOUR#:VOLT?</a>.</div>
        <div class="example">&gt;&gt; SOUR:VOLT:CORR:SLOP 1e-3</div>

      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="SOUR">
        <span class="scpi" id="SOUR#:VOLT:CORR:SLOP?">SOUR#:VOLT:CORR:SLOP?</span><span class="briefScpi">Queries the analogue output gain level</span>
      </div>
      <div class="content">
        <div class="scpiLong">SOURce<div class="tooltip" data-id="183">#</div>:VOLTage:CORRection:SLOPe[:OUTPut][:MAGNitude]? <div class="tooltip" data-id="184">&lt;special&gt;</div></div>
        <div class="sufixDocu">Suffixe<ul>
          <li id="183"><span class="param">Sufix 1</span><span class="paramType">uint</span> <span class="paramDesc">Analogue output channel from 2 to 3. Default 2. 2 is for power. 3 is for position (PM102 only)</span></li>
        </ul></div>

        <div class="paramDocu">Parameters<ul>
           <li id="184"><span class="optParam">special</span><span class="paramType">MIN,DEF,MAX</span> <span class="paramDesc">Optional! nothing to query actual slope. MIN,DEF or MAX to query predefined values.</span></li>
        </ul></div>

        <div class="resultDocu">Response<ul>
          <li><span class="param">slope</span><span class="paramType">float</span> <span class="paramDesc">slope in V/W or V/J V/um depending on channel</span></li>
        </ul></div>

        <div class="details"> Use this query to get the analogue output voltage conversion gain.  The gain is used to convert the represented physical unit to the  output voltage. For closer details read <a class="linkSCPI" title="Sets the analogue output gain" href="#SOUR#:VOLT:CORR:SLOP">SOUR#:VOLT:CORR:SLOP</a>.</div>
        <div class="example">&gt;&gt; SOUR:VOLT:CORR:SLOP?<br />&lt;&lt; 1e3</div>

      </div>
    </div>
    <hr>
    <p id="STAT"><a href="#STAT:AUX?" data-subsystem="STAT" class="unfoldSection">Unfold all STAT Commands</a></p>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="STAT">
        <span class="scpi" id="STAT:AUX?">STAT:AUX?</span><span class="briefScpi">Queries the SCPI Auxiliary Event register</span>
      </div>
      <div class="content">
        <div class="scpiLong">STATus:AUXiliary[:EVENt]?</div>
        <div class="resultDocu">Response<ul>
          <li><span class="param">bitmask</span><span class="paramType">uint</span> <span class="paramDesc">16 bit wide event register bit mask.</span></li>
        </ul></div>

        <div class="details">Use this command to query the SCPI 16 bit wide Auxiliary Event register bit mask. The Auxiliary register set summarizes the operating state of auxiliary equipment connected to the Powermeter. Reading this event register automatically resets the register back to 0. The register content is a bit mask with a 1 bit for every event occurred since the register has been read the last time. If the same event occurs multiple times the event bit stays 1. To get the meaning of the single bits read the <a class="linkSCPI" title="Queries the SCPI Auxiliary Condition register" href="#STAT:AUX:COND?">STAT:AUX:COND?</a> description. The event register can store a change of the Auxiliary Condition register. The two transition registers are used to mask changes of the condition register to the event register. If a certain bit in the condition register is set from 0 to 1 and the bit at the same position in the positive transition register <a class="linkSCPI" title="Sets the SCPI Auxiliary Positive Transition register" href="#STAT:AUX:PTR">STAT:AUX:PTR</a> is set 1, the event register at the bit position is set to 1. It is also possible to detect condition register changes from 1 back to 0 using the negative transition register <a class="linkSCPI" title="Sets the SCPI Auxiliary Negative Transition register" href="#STAT:AUX:NTR">STAT:AUX:NTR</a> following the same logic.<br/> Changes of the event register can be summarized to the SCPI status register bit 0 if enabled by <a class="linkSCPI" title="Sets the SCPI Auxiliary Enable register" href="#STAT:AUX:ENAB">STAT:AUX:ENAB</a>. To read the SCPI status register call <a class="linkSCPI" title="Status byte query" href="#STB?">STB?</a>. After reboot or <a class="linkSCPI" title="Clears all SCPI event status registers" href="#CLS">CLS</a> the events are all reset to 0.<br/> Read <a href="#statusRegisterSetDesc" title="General SCPI Status register description">SCPI register set</a> section to get closer infos about the SCPI register system in general.</div>
        <div class="example">&gt;&gt; STAT:AUX?<br />&lt;&lt; 253</div>
        <div class="note">Some events might get lost if same event occurs multiple times without the event register being read in between.</div>
        <div class="info">Reading the event register automatically resets register to 0. To get the events bit positions read <a class="linkSCPI" title="Queries the SCPI Auxiliary Condition register" href="#STAT:AUX:COND?">STAT:AUX:COND?</a>.</div>
      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="STAT">
        <span class="scpi" id="STAT:AUX:COND?">STAT:AUX:COND?</span><span class="briefScpi">Queries the SCPI Auxiliary Condition register</span>
      </div>
      <div class="content">
        <div class="scpiLong">STATus:AUXiliary:CONDition?</div>
        <div class="resultDocu">Response<ul>
          <li><span class="param">bitmask</span><span class="paramType">uint</span> <span class="paramDesc">16 bit wide condition register bit mask.</span></li>
        </ul></div>

        <div class="details">Use this command to query the SCPI 16 bit wide Auxiliary Condition register bit mask. The Auxiliary register set summarizes the operating state of auxiliary equipment connected to the Powermeter.<br/> The register content is a bit mask with a 1 bit for every condition being active at the moment. Be aware some conditions might only last for a very short time period. In this case using the event register <a class="linkSCPI" title="Queries the SCPI Auxiliary Event register" href="#STAT:AUX?">STAT:AUX?</a> makes more sense. Configure the positive <a class="linkSCPI" title="Sets the SCPI Auxiliary Positive Transition register" href="#STAT:AUX:PTR">STAT:AUX:PTR</a> or negative <a class="linkSCPI" title="Sets the SCPI Auxiliary Negative Transition register" href="#STAT:AUX:NTR">STAT:AUX:NTR</a> transition register to map changes of the condition register to the event register. These are the masked events:<br/> <svg width="200px" viewBox="1544 115 282 328" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">  <g>    <rect style="fill: #ffffff" x="1684.63" y="118.69" width="140" height="324"/>    <rect style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x="1684.63" y="118.69" width="140" height="324"/>    <text font-size="14.1111" style="fill: #000000;text-anchor:end;font-family:sans-serif;font-style:normal;font-weight:700" x="1815.63" y="277.152">      <tspan x="1815.63" y="277.152">STAT:</tspan>      <tspan x="1815.63" y="294.79">AUX</tspan>    </text>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1544.63" y="128.69">    <tspan x="1544.63" y="128.69">Ext NTC connected</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1544.63" y="168.69">    <tspan x="1544.63" y="168.69">Custom cal supported</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="1564.63" y1="130.69" x2="1680.16" y2="130.69"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1672.39,135.69 1682.39,130.69 1672.39,125.69 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1688.63" y="134.69">    <tspan x="1688.63" y="134.69">bit 0(1)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1544.63" y="188.69">    <tspan x="1544.63" y="188.69">Custom cal active</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="1564.63" y1="150.69" x2="1680.16" y2="150.69"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1672.39,155.69 1682.39,150.69 1672.39,145.69 "/>  </g>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="1564.63" y1="170.69" x2="1680.16" y2="170.69"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1672.39,175.69 1682.39,170.69 1672.39,165.69 "/>  </g>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="1564.63" y1="190.69" x2="1680.16" y2="190.69"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1672.39,195.69 1682.39,190.69 1672.39,185.69 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1544.63" y="208.69">    <tspan x="1544.63" y="208.69">reserved/tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="1564.63" y1="210.69" x2="1680.16" y2="210.69"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1672.39,215.69 1682.39,210.69 1672.39,205.69 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1544.63" y="228.69">    <tspan x="1544.63" y="228.69">reserved</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="1564.63" y1="230.69" x2="1680.16" y2="230.69"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1672.39,235.69 1682.39,230.69 1672.39,225.69 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1544.63" y="248.69">    <tspan x="1544.63" y="248.69">reserved/tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="1564.63" y1="250.69" x2="1680.16" y2="250.69"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1672.39,255.69 1682.39,250.69 1672.39,245.69 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1544.63" y="268.69">    <tspan x="1544.63" y="268.69">reserved</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="1564.63" y1="270.69" x2="1680.16" y2="270.69"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1672.39,275.69 1682.39,270.69 1672.39,265.69 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1544.63" y="288.69">    <tspan x="1544.63" y="288.69">reserved</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="1564.63" y1="290.69" x2="1680.16" y2="290.69"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1672.39,295.69 1682.39,290.69 1672.39,285.69 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1544.63" y="308.69">    <tspan x="1544.63" y="308.69">reserved</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="1564.63" y1="310.69" x2="1680.16" y2="310.69"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1672.39,315.69 1682.39,310.69 1672.39,305.69 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1544.63" y="328.69">    <tspan x="1544.63" y="328.69">reserved</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="1564.63" y1="330.69" x2="1680.16" y2="330.69"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1672.39,335.69 1682.39,330.69 1672.39,325.69 "/>  </g>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="1564.63" y1="350.69" x2="1680.16" y2="350.69"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1672.39,355.69 1682.39,350.69 1672.39,345.69 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1544.63" y="368.69">    <tspan x="1544.63" y="368.69">reserved</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="1564.63" y1="370.69" x2="1680.16" y2="370.69"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1672.39,375.69 1682.39,370.69 1672.39,365.69 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1544.63" y="408.69">    <tspan x="1544.63" y="408.69">reserved</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="1564.63" y1="410.69" x2="1680.16" y2="410.69"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1672.39,415.69 1682.39,410.69 1672.39,405.69 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1544.63" y="428.69">    <tspan x="1544.63" y="428.69">reserved</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="1564.63" y1="430.69" x2="1680.16" y2="430.69"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1672.39,435.69 1682.39,430.69 1672.39,425.69 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1688.63" y="154.69">    <tspan x="1688.63" y="154.69">bit 1(2)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1688.63" y="174.69">    <tspan x="1688.63" y="174.69">bit 2(4)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1688.63" y="194.69">    <tspan x="1688.63" y="194.69">bit 3(8)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1688.63" y="214.69">    <tspan x="1688.63" y="214.69">bit 4(16)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1688.63" y="234.69">    <tspan x="1688.63" y="234.69">bit 5(32)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1688.63" y="254.69">    <tspan x="1688.63" y="254.69">bit 6(64)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1688.63" y="274.69">    <tspan x="1688.63" y="274.69">bit 7(128)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1688.63" y="294.69">    <tspan x="1688.63" y="294.69">bit 8(256)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1688.63" y="314.69">    <tspan x="1688.63" y="314.69">bit 9(512)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1688.63" y="334.69">    <tspan x="1688.63" y="334.69">bit 10(1024)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1688.63" y="354.69">    <tspan x="1688.63" y="354.69">bit 11(2048)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1688.63" y="374.69">    <tspan x="1688.63" y="374.69">bit 12(4096)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1688.63" y="394.69">    <tspan x="1688.63" y="394.69">bit 13(8192)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1688.63" y="414.69">    <tspan x="1688.63" y="414.69">bit 14(16384)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1688.63" y="434.69">    <tspan x="1688.63" y="434.69">bit 15(32768)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1544.63" y="388.69">    <tspan x="1544.63" y="388.69">reserved</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="1564.63" y1="390.69" x2="1680.16" y2="390.69"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1672.39,395.69 1682.39,390.69 1672.39,385.69 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1544.63" y="346.69">    <tspan x="1544.63" y="346.69">reserved</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1544.63" y="146.69">    <tspan x="1544.63" y="146.69">Environ. sens conn</tspan>  </text></svg> <ul class="detailListing"> <li><b>bit 0:</b> NTC connected. Use <a class="linkSCPI" title="Returns the temperature of external NTC sensor" href="#SENS#:TEMP:DATA?">SENS#:TEMP:DATA?</a> to read measured temperature.</li> <li><b>bit 2:</b> Customer calibration supported by device. Always 1.</li> <li><b>bit 3:</b> Customer calibration active. See <a class="linkSCPI" title="Enables/Disables customer calibration" href="#SENS#:CORR:CSET#:STAT">SENS#:CORR:CSET#:STAT</a>.</li> </ul> All other condition bits are reserved. Read <a href="#statusRegisterSetDesc" title="General SCPI Status register description">SCPI register set</a> section to get closer infos about the SCPI register system in general.</div>
        <div class="example">&gt;&gt; STAT:AUX:COND?<br />&lt;&lt; 2182</div>
        <div class="info">To test for short lasting conditions better use the event register <a class="linkSCPI" title="Queries the SCPI Auxiliary Event register" href="#STAT:AUX?">STAT:AUX?</a>.</div>
      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="STAT">
        <span class="scpi" id="STAT:AUX:ENAB">STAT:AUX:ENAB</span><span class="briefScpi">Sets the SCPI Auxiliary Enable register</span>
      </div>
      <div class="content">
        <div class="scpiLong">STATus:AUXiliary:ENABle <div class="tooltip" data-id="176">&lt;bitmask&gt;</div></div>
        <div class="paramDocu">Parameters<ul>
              <li id="176"><span class="param">bitmask</span><span class="paramType">uint</span> <span class="paramDesc">16 bit wide bit mask for enable register.</span></li>
        </ul></div>

        <div class="details">Use this command to set the SCPI 16 bit wide Auxiliary Enable register bit mask. The register content is a bit mask with a 1 bit for every enabled event that should be forwarded to the 8 bit wide SCPI status register bit 0. See <a class="linkSCPI" title="Status byte query" href="#STB?">STB?</a>. To get the meaning of the single bits read the <a class="linkSCPI" title="Queries the SCPI Auxiliary Condition register" href="#STAT:AUX:COND?">STAT:AUX:COND?</a> description. This enable register acts like a filter mask for all auxiliary events (see <a class="linkSCPI" title="Queries the SCPI Auxiliary Event register" href="#STAT:AUX?">STAT:AUX?</a>) that should be forwarded to the SCPI status register. If the enable mask is set with all bits 1, the SCPI status register bit 0 is set to 1 whenever one or multiple events are set in <a class="linkSCPI" title="Queries the SCPI Auxiliary Event register" href="#STAT:AUX?">STAT:AUX?</a> register.<br/> The idea behind this abstraction concept is to read the SCPI status register and check for bit 0 to change. Whenever the bit changed the user then may query this Auxiliary Event register to get the details. Changing the enable register with active events might directly update the summarized SCPI status register bit.<br/> Read <a href="#statusRegisterSetDesc" title="General SCPI Status register description">SCPI register set</a> section to get closer infos about the SCPI register system in general.</div>
        <div class="example">&gt;&gt; STAT:AUX:ENAB 65280</div>
        <div class="note">Ensure that the positive and negative transition registers are set accordingly to change the event register. Forwarding only works if event register gets updated.</div>
        <div class="info">The default value after reboot or <a class="linkSCPI" title="Clears all SCPI event status registers" href="#CLS">CLS</a> is 0. So no events are forwarded.</div>
      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="STAT">
        <span class="scpi" id="STAT:AUX:ENAB?">STAT:AUX:ENAB?</span><span class="briefScpi">Queries the SCPI Auxiliary Enable register</span>
      </div>
      <div class="content">
        <div class="scpiLong">STATus:AUXiliary:ENABle?</div>
        <div class="resultDocu">Response<ul>
          <li><span class="param">bitmask</span><span class="paramType">uint</span> <span class="paramDesc">16 bit wide enable register bit mask.</span></li>
        </ul></div>

        <div class="details">Use this command to query the SCPI 16 bit wide Auxiliary Enable register bit mask. The register content is a bit mask with a 1 bit for every enabled event that should be forwarded to the 8 bit wide SCPI status register bit 0. To get the meaning of the single bits read the <a class="linkSCPI" title="Queries the SCPI Auxiliary Condition register" href="#STAT:AUX:COND?">STAT:AUX:COND?</a> description. For closer details about the Auxiliary Enable register read <a class="linkSCPI" title="Sets the SCPI Auxiliary Enable register" href="#STAT:AUX:ENAB">STAT:AUX:ENAB</a>.<br/> Read <a href="#statusRegisterSetDesc" title="General SCPI Status register description">SCPI register set</a> section to get closer infos about the SCPI register system in general.</div>
        <div class="example">&gt;&gt; STAT:AUX:ENAB?<br />&lt;&lt; 65280</div>
        <div class="note">Ensure that the positive or negative transition registers are set accordingly to change the event register. Forwarding only works if event register gets updated.</div>
        <div class="info">The default value after reboot or <a class="linkSCPI" title="Clears all SCPI event status registers" href="#CLS">CLS</a> is 0. So no events are forwarded.</div>
      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="STAT">
        <span class="scpi" id="STAT:AUX:NTR">STAT:AUX:NTR</span><span class="briefScpi">Sets the SCPI Auxiliary Negative Transition register</span>
      </div>
      <div class="content">
        <div class="scpiLong">STATus:AUXiliary:NTRansition <div class="tooltip" data-id="178">&lt;bitmask&gt;</div></div>
        <div class="paramDocu">Parameters<ul>
              <li id="178"><span class="param">bitmask</span><span class="paramType">uint</span> <span class="paramDesc">16 bit wide bit mask for positive transition register.</span></li>
        </ul></div>

        <div class="details">Use this command to set the SCPI 16 bit wide Auxiliary Negative Transition register bit mask. The register content is a bit mask with a 1 bit for every condition change that should be mapped to the operation event register <a class="linkSCPI" title="Queries the SCPI Auxiliary Event register" href="#STAT:AUX?">STAT:AUX?</a>. As it is the negative transition register, only condition register bit changes from 1 back to 0 update the bits in the event register. To get the meaning of the single bits read the <a class="linkSCPI" title="Queries the SCPI Auxiliary Condition register" href="#STAT:AUX:COND?">STAT:AUX:COND?</a> description. For example if you set this register value to 2 (binary: 10 bit at position 1) and the external NTC is disconnected, it will also set the event register value bit at position 1 to 1.<br/> Read <a href="#statusRegisterSetDesc" title="General SCPI Status register description">SCPI register set</a> section to get closer infos about the SCPI register system in general.</div>
        <div class="example">&gt;&gt; STAT:AUX:NTR 260</div>
        <div class="info">The default value after reboot or <a class="linkSCPI" title="Clears all SCPI event status registers" href="#CLS">CLS</a> is 0. So no condition changes from bit 1 to 0 update the event register.</div>
      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="STAT">
        <span class="scpi" id="STAT:AUX:NTR?">STAT:AUX:NTR?</span><span class="briefScpi">Queries the SCPI Auxiliary Negative Transition register</span>
      </div>
      <div class="content">
        <div class="scpiLong">STATus:AUXiliary:NTRansition?</div>
        <div class="resultDocu">Response<ul>
          <li><span class="param">bitmask</span><span class="paramType">uint</span> <span class="paramDesc">16 bit wide bit mask for positive transition register.</span></li>
        </ul></div>

        <div class="details">Use this command to query the SCPI 16 bit wide Auxiliary Negative Transition register bit mask. The register content is a bit mask with a 1 bit for every condition change that should be mapped to the operation event register <a class="linkSCPI" title="Queries the SCPI Auxiliary Event register" href="#STAT:AUX?">STAT:AUX?</a>. For closer details about Negative Transition register read description <a class="linkSCPI" title="Sets the SCPI Auxiliary Negative Transition register" href="#STAT:AUX:NTR">STAT:AUX:NTR</a>.<br/> Read <a href="#statusRegisterSetDesc" title="General SCPI Status register description">SCPI register set</a> section to get closer infos about the SCPI register system in general.</div>
        <div class="example">&gt;&gt; STAT:AUX:NTR?<br />&lt;&lt; 260</div>
        <div class="info">The default value after reboot or <a class="linkSCPI" title="Clears all SCPI event status registers" href="#CLS">CLS</a> is 0. So no condition changes from bit 1 to 0 update the event register.</div>
      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="STAT">
        <span class="scpi" id="STAT:AUX:PTR">STAT:AUX:PTR</span><span class="briefScpi">Sets the SCPI Auxiliary Positive Transition register</span>
      </div>
      <div class="content">
        <div class="scpiLong">STATus:AUXiliary:PTRansition <div class="tooltip" data-id="177">&lt;bitmask&gt;</div></div>
        <div class="paramDocu">Parameters<ul>
              <li id="177"><span class="param">bitmask</span><span class="paramType">uint</span> <span class="paramDesc">16 bit wide bit mask for positive transition register.</span></li>
        </ul></div>

        <div class="details">Use this command to set the SCPI 16 bit wide Auxiliary Positive Transition register bit mask. The register content is a bit mask with a 1 bit for every condition change that should be mapped to the Auxiliary Event register <a class="linkSCPI" title="Queries the SCPI Auxiliary Event register" href="#STAT:AUX?">STAT:AUX?</a>. As it is the positive transition register, only condition register bit changes from 0 to 1 update the bits in the event register. To get the meaning of the single bits read the <a class="linkSCPI" title="Queries the SCPI Auxiliary Condition register" href="#STAT:AUX:COND?">STAT:AUX:COND?</a> description. For example if you set this register value to 2 (binary: 10 bit at position 1) and an external NTC is connected, it will also set the event register value bit at position 1 to 1.<br/> Read <a href="#statusRegisterSetDesc" title="General SCPI Status register description">SCPI register set</a> section to get closer infos about the SCPI register system in general.</div>
        <div class="example">&gt;&gt; STAT:AUX:PTR 8</div>
        <div class="info">The default value after reboot or <a class="linkSCPI" title="Clears all SCPI event status registers" href="#CLS">CLS</a> is 65535 (all bits 1). So all condition changes from bit 0 to 1 update the event register.</div>
      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="STAT">
        <span class="scpi" id="STAT:AUX:PTR?">STAT:AUX:PTR?</span><span class="briefScpi">Queries the SCPI Auxiliary Positive Transition register</span>
      </div>
      <div class="content">
        <div class="scpiLong">STATus:AUXiliary:PTRansition?</div>
        <div class="resultDocu">Response<ul>
          <li><span class="param">bitmask</span><span class="paramType">uint</span> <span class="paramDesc">16 bit wide bit mask for positive transition register.</span></li>
        </ul></div>

        <div class="details">Use this command to query the SCPI 16 bit wide Auxiliary Positive Transition register bit mask. The register content is a bit mask with a 1 bit for every condition change that should be mapped to the operation event register <a class="linkSCPI" title="Queries the SCPI Auxiliary Event register" href="#STAT:AUX?">STAT:AUX?</a>. For closer details about Positive Transition register read description <a class="linkSCPI" title="Sets the SCPI Auxiliary Positive Transition register" href="#STAT:AUX:PTR">STAT:AUX:PTR</a>.<br/> Read <a href="#statusRegisterSetDesc" title="General SCPI Status register description">SCPI register set</a> section to get closer infos about the SCPI register system in general.</div>
        <div class="example">&gt;&gt; STAT:AUX:PTR?<br />&lt;&lt; 65280</div>
        <div class="info">The default value after reboot or <a class="linkSCPI" title="Clears all SCPI event status registers" href="#CLS">CLS</a> is 65535 (all bits 1). So all condition changes from bit 0 to 1 update the event register.</div>
      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="STAT">
        <span class="scpi" id="STAT:OPER?">STAT:OPER?</span><span class="briefScpi">Queries the SCPI Operation Event register</span>
      </div>
      <div class="content">
        <div class="scpiLong">STATus:OPERation[:EVENt]?</div>
        <div class="resultDocu">Response<ul>
          <li><span class="param">bitmask</span><span class="paramType">uint</span> <span class="paramDesc">16 bit wide event register</span></li>
        </ul></div>

        <div class="details">Use this command to query the SCPI 16 bit wide Operation Event register bit mask. The operation register set monitors the operating state of the measurement system.<br/> Reading this event register automatically resets the register back to 0. The register content is a bit mask with a 1 bit for every event occurred since the register has been read the last time. If the same event occurs multiple times the event bit stays 1. To get the meaning of the single bits read the <a class="linkSCPI" title="Queries the SCPI Operation Condition register" href="#STAT:OPER:COND?">STAT:OPER:COND?</a> description. The event register can store a change of the Operation Condition register. The two transition registers are used to mask changes of the condition register to the event register. If a certain bit in the condition register is set from 0 to 1 and the bit at the same position in the positive transition register <a class="linkSCPI" title="Sets the SCPI Operation Positive Transition register" href="#STAT:OPER:PTR">STAT:OPER:PTR</a> is set 1, the event register at the bit position is set to 1. It is also possible to detect condition register changes from 1 back to 0 using the negative transition register <a class="linkSCPI" title="Sets the SCPI Operation Negative Transition register" href="#STAT:OPER:NTR">STAT:OPER:NTR</a> following the same logic.<br/> Using the event register makes sense for short lasting conditions like a range change for example. Changes of the event register can be summarized to the SCPI status register bit 7 if enabled by <a class="linkSCPI" title="Sets the SCPI Operation Enable register" href="#STAT:OPER:ENAB">STAT:OPER:ENAB</a>. To read the SCPI status register call <a class="linkSCPI" title="Status byte query" href="#STB?">STB?</a>. After reboot or <a class="linkSCPI" title="Clears all SCPI event status registers" href="#CLS">CLS</a> the events are all reset to 0. Read <a href="#statusRegisterSetDesc" title="General SCPI Status register description">SCPI register set</a> section to get closer infos about the SCPI register system in general.</div>
        <div class="example">&gt;&gt; STAT:OPER?<br />&lt;&lt; 253</div>
        <div class="note">Some events might get lost if same event occurs multiple times without the event register being read in between.</div>
        <div class="info">Reading the event register automatically resets register to 0. To get the events bit positions read <a class="linkSCPI" title="Queries the SCPI Operation Condition register" href="#STAT:OPER:COND?">STAT:OPER:COND?</a>.</div>
      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="STAT">
        <span class="scpi" id="STAT:OPER:COND?">STAT:OPER:COND?</span><span class="briefScpi">Queries the SCPI Operation Condition register</span>
      </div>
      <div class="content">
        <div class="scpiLong">STATus:OPERation:CONDition?</div>
        <div class="resultDocu">Response<ul>
          <li><span class="param">bitmask</span><span class="paramType">uint</span> <span class="paramDesc">16 bit wide condition register.</span></li>
        </ul></div>

        <div class="details">Use this command to query the SCPI 16 bit wide Operation Condition register bit mask. The operation register set monitors the operating state of the measurement system.<br/> The register content is a bit mask with a 1 bit for every condition being active at the moment. Be aware some conditions might only last for a very short time period like a range change for example. In this case using the event register <a class="linkSCPI" title="Queries the SCPI Operation Event register" href="#STAT:OPER?">STAT:OPER?</a> makes more sense. Configure the positive <a class="linkSCPI" title="Sets the SCPI Operation Positive Transition register" href="#STAT:OPER:PTR">STAT:OPER:PTR</a> or negative <a class="linkSCPI" title="Sets the SCPI Operation Negative Transition register" href="#STAT:OPER:NTR">STAT:OPER:NTR</a> transition register to map changes of the condition register to the event register. These are the masked events:<br/> <svg width="200px" viewBox="1574 1905 282 328" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">  <g>    <rect style="fill: #ffffff" x="1714.63" y="1908.67" width="140" height="324"/>    <rect style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x="1714.63" y="1908.67" width="140" height="324"/>    <text font-size="14.1111" style="fill: #000000;text-anchor:end;font-family:sans-serif;font-style:normal;font-weight:700" x="1845.63" y="2067.13">      <tspan x="1845.63" y="2067.13">STAT:</tspan>      <tspan x="1845.63" y="2084.77">OPER</tspan>    </text>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1574.63" y="1918.67">    <tspan x="1574.63" y="1918.67">reserved</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1574.63" y="1938.67">    <tspan x="1574.63" y="1938.67">reserved</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1574.63" y="1958.67">    <tspan x="1574.63" y="1958.67">Ranging</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="1594.63" y1="1920.67" x2="1710.16" y2="1920.67"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1702.39,1925.67 1712.39,1920.67 1702.39,1915.67 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1718.63" y="1924.67">    <tspan x="1718.63" y="1924.67">bit 0(1)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1574.63" y="1978.67">    <tspan x="1574.63" y="1978.67">reserved</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="1594.63" y1="1940.67" x2="1710.16" y2="1940.67"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1702.39,1945.67 1712.39,1940.67 1702.39,1935.67 "/>  </g>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="1594.63" y1="1960.67" x2="1710.16" y2="1960.67"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1702.39,1965.67 1712.39,1960.67 1702.39,1955.67 "/>  </g>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="1594.63" y1="1980.67" x2="1710.16" y2="1980.67"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1702.39,1985.67 1712.39,1980.67 1702.39,1975.67 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1574.63" y="1998.67">    <tspan x="1574.63" y="1998.67">Measuring</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="1594.63" y1="2000.67" x2="1710.16" y2="2000.67"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1702.39,2005.67 1712.39,2000.67 1702.39,1995.67 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1574.63" y="2018.67">    <tspan x="1574.63" y="2018.67">reserved</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="1594.63" y1="2020.67" x2="1710.16" y2="2020.67"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1702.39,2025.67 1712.39,2020.67 1702.39,2015.67 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1574.63" y="2038.67">    <tspan x="1574.63" y="2038.67">reserved</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="1594.63" y1="2040.67" x2="1710.16" y2="2040.67"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1702.39,2045.67 1712.39,2040.67 1702.39,2035.67 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1574.63" y="2058.67">    <tspan x="1574.63" y="2058.67">Zeroing</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="1594.63" y1="2060.67" x2="1710.16" y2="2060.67"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1702.39,2065.67 1712.39,2060.67 1702.39,2055.67 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1574.63" y="2078.67">    <tspan x="1574.63" y="2078.67">Sensor connected</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="1594.63" y1="2080.67" x2="1710.16" y2="2080.67"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1702.39,2085.67 1712.39,2080.67 1702.39,2075.67 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1574.63" y="2098.67">    <tspan x="1574.63" y="2098.67">Data ready to fetch</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="1594.63" y1="2100.67" x2="1710.16" y2="2100.67"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1702.39,2105.67 1712.39,2100.67 1702.39,2095.67 "/>  </g>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="1594.63" y1="2120.67" x2="1710.16" y2="2120.67"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1702.39,2125.67 1712.39,2120.67 1702.39,2115.67 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1574.63" y="2138.67">    <tspan x="1574.63" y="2138.67">reserved</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="1594.63" y1="2140.67" x2="1710.16" y2="2140.67"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1702.39,2145.67 1712.39,2140.67 1702.39,2135.67 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1574.63" y="2158.67">    <tspan x="1574.63" y="2158.67">reserved</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="1594.63" y1="2160.67" x2="1710.16" y2="2160.67"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1702.39,2165.67 1712.39,2160.67 1702.39,2155.67 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1574.63" y="2218.67">    <tspan x="1574.63" y="2218.67">reserved</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="1594.63" y1="2220.67" x2="1710.16" y2="2220.67"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1702.39,2225.67 1712.39,2220.67 1702.39,2215.67 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1718.63" y="1944.67">    <tspan x="1718.63" y="1944.67">bit 1(2)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1718.63" y="1964.67">    <tspan x="1718.63" y="1964.67">bit 2(4)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1718.63" y="1984.67">    <tspan x="1718.63" y="1984.67">bit 3(8)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1718.63" y="2004.67">    <tspan x="1718.63" y="2004.67">bit 4(16)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1718.63" y="2024.67">    <tspan x="1718.63" y="2024.67">bit 5(32)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1718.63" y="2044.67">    <tspan x="1718.63" y="2044.67">bit 6(64)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1718.63" y="2064.67">    <tspan x="1718.63" y="2064.67">bit 7(128)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1718.63" y="2084.67">    <tspan x="1718.63" y="2084.67">bit 8(256)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1718.63" y="2104.67">    <tspan x="1718.63" y="2104.67">bit 9(512)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1718.63" y="2124.67">    <tspan x="1718.63" y="2124.67">bit 10(1024)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1718.63" y="2144.67">    <tspan x="1718.63" y="2144.67">bit 11(2048)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1718.63" y="2164.67">    <tspan x="1718.63" y="2164.67">bit 12(4096)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1718.63" y="2184.67">    <tspan x="1718.63" y="2184.67">bit 13(8192)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1718.63" y="2204.67">    <tspan x="1718.63" y="2204.67">bit 14(16384)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1718.63" y="2224.67">    <tspan x="1718.63" y="2224.67">bit 15(32768)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1574.63" y="2116.67">    <tspan x="1574.63" y="2116.67">Thermo accelerator</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1574.63" y="2178.67">    <tspan x="1574.63" y="2178.67">reserved</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="1594.63" y1="2180.67" x2="1710.16" y2="2180.67"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1702.39,2185.67 1712.39,2180.67 1702.39,2175.67 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1574.63" y="2198.67">    <tspan x="1574.63" y="2198.67">reserved</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="1594.63" y1="2200.67" x2="1710.16" y2="2200.67"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1702.39,2205.67 1712.39,2200.67 1702.39,2195.67 "/>  </g></svg> <ul class="detailListing"> <li><b>bit 2:</b> Measure range changing ongoing condition. See <a class="linkSCPI" title="Sets a measurement range for given power" href="#SENS#:POW:RANG">SENS#:POW:RANG</a>.</li> <li><b>bit 4:</b> Measurements active conditions.</li> <li><b>bit 7:</b> Zeroing algorithm running. See <a class="linkSCPI" title="Starts a zeroing of the sensor" href="#SENS#:CORR:COLL:ZERO">SENS#:CORR:COLL:ZERO</a>.</li> <li><b>bit 8:</b> Sensor connected condition.</li> <li><b>bit 9:</b> Data ready to fetch condition. See <a class="linkSCPI" title="Measures the sensor averaged power in Watt or dBm" href="#MEAS?">MEAS?</a>.</li> <li><b>bit 10:</b> Thermopile accelerator active condition. See <a class="linkSCPI" title="Enables/Disables thermopile prediction" href="#INP:THER:ACC:AUTO">INP:THER:ACC:AUTO</a>.</li> </ul> All other condition bits are reserved. Read <a href="#statusRegisterSetDesc" title="General SCPI Status register description">SCPI register set</a> section to get closer infos about the SCPI register system in general.</div>
        <div class="example">&gt;&gt; STAT:OPER:COND?<br />&lt;&lt; 2182</div>
        <div class="info">To test for short lasting conditions like a range change use the event register <a class="linkSCPI" title="Queries the SCPI Operation Event register" href="#STAT:OPER?">STAT:OPER?</a>.</div>
      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="STAT">
        <span class="scpi" id="STAT:OPER:ENAB">STAT:OPER:ENAB</span><span class="briefScpi">Sets the SCPI Operation Enable register</span>
      </div>
      <div class="content">
        <div class="scpiLong">STATus:OPERation:ENABle <div class="tooltip" data-id="170">&lt;bitmask&gt;</div></div>
        <div class="paramDocu">Parameters<ul>
              <li id="170"><span class="param">bitmask</span><span class="paramType">uint</span> <span class="paramDesc">16 bit wide bit mask for enable register.</span></li>
        </ul></div>

        <div class="details">Use this command to set the SCPI 16 bit wide Operation Enable register bit mask. The register content is a bit mask with a 1 bit for every enabled event that should be forwarded to the 8 bit wide SCPI status register bit 7. See <a class="linkSCPI" title="Status byte query" href="#STB?">STB?</a>. To get the meaning of the single bits read the <a class="linkSCPI" title="Queries the SCPI Operation Condition register" href="#STAT:OPER:COND?">STAT:OPER:COND?</a> description. This enable register acts like a filter mask for all operation events (see <a class="linkSCPI" title="Queries the SCPI Operation Event register" href="#STAT:OPER?">STAT:OPER?</a>) that should be forwarded to the SCPI status register. If the enable mask is set with all bits 1, the SCPI status register bit 7 is set to 1 whenever one or multiple events are set in <a class="linkSCPI" title="Queries the SCPI Operation Event register" href="#STAT:OPER?">STAT:OPER?</a> register. The idea behind this abstraction concept is to read the SCPI status register and check for bit 7 to change. Whenever the bit changed the user then may query this Operation Event register to get the details. Changing the enable register with active events might directly update the summarized SCPI status register bit.<br/> Read <a href="#statusRegisterSetDesc" title="General SCPI Status register description">SCPI register set</a> section to get closer infos about the SCPI register system in general.</div>
        <div class="example">&gt;&gt; STAT:OPER:ENAB 65280</div>
        <div class="note">Ensure that the positive and negative transition registers are set accordingly to change the event register. Forwarding only works if event register gets updated.</div>
        <div class="info">The default value after reboot or <a class="linkSCPI" title="Clears all SCPI event status registers" href="#CLS">CLS</a> is 0. So no events are forwarded.</div>
      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="STAT">
        <span class="scpi" id="STAT:OPER:ENAB?">STAT:OPER:ENAB?</span><span class="briefScpi">Queries the SCPI Operation Enable register</span>
      </div>
      <div class="content">
        <div class="scpiLong">STATus:OPERation:ENABle?</div>
        <div class="resultDocu">Response<ul>
          <li><span class="param">bitmask</span><span class="paramType">uint</span> <span class="paramDesc">16 bit wide enable register.</span></li>
        </ul></div>

        <div class="details">Use this command to query the SCPI 16 bit wide Operation Enable register bit mask. The register content is a bit mask with a 1 bit for every enabled event that should be forwarded to the 8 bit wide SCPI status register bit 7. To get the meaning of the single bits read the <a class="linkSCPI" title="Queries the SCPI Operation Condition register" href="#STAT:OPER:COND?">STAT:OPER:COND?</a> description. For closer details about the Operation Enable register read <a class="linkSCPI" title="Sets the SCPI Operation Enable register" href="#STAT:OPER:ENAB">STAT:OPER:ENAB</a>.<br /> Read <a href="#statusRegisterSetDesc" title="General SCPI Status register description">SCPI register set</a> section to get closer infos about the SCPI register system in general.</div>
        <div class="example">&gt;&gt; STAT:OPER:ENAB?<br />&lt;&lt; 65280</div>
        <div class="note">Ensure that the positive or negative transition registers are set accordingly to change the event register. Forwarding only works if event register gets updated.</div>
        <div class="info">The default value after reboot or <a class="linkSCPI" title="Clears all SCPI event status registers" href="#CLS">CLS</a> is 0. So no events are forwarded.</div>
      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="STAT">
        <span class="scpi" id="STAT:OPER:NTR">STAT:OPER:NTR</span><span class="briefScpi">Sets the SCPI Operation Negative Transition register</span>
      </div>
      <div class="content">
        <div class="scpiLong">STATus:OPERation:NTRansition <div class="tooltip" data-id="172">&lt;bitmask&gt;</div></div>
        <div class="paramDocu">Parameters<ul>
              <li id="172"><span class="param">bitmask</span><span class="paramType">uint</span> <span class="paramDesc">16 bit wide bit mask for positive transition register.</span></li>
        </ul></div>

        <div class="details">Use this command to set the SCPI 16 bit wide Operation Negative Transition register bit mask. The register content is a bit mask with a 1 bit for every condition change that should be mapped to the operation event register <a class="linkSCPI" title="Queries the SCPI Operation Event register" href="#STAT:OPER?">STAT:OPER?</a>. As it is the negative transition register, only condition register bit changes from 1 back to 0 update the bits in the event register. To get the meaning of the single bits read the <a class="linkSCPI" title="Queries the SCPI Operation Condition register" href="#STAT:OPER:COND?">STAT:OPER:COND?</a> description. For example if you set this register value to 1024 (binary: 10000000000 bit at position 10) and the accelerator stops (condition register bit at position 10 changes back from 1 to 0) it will also set the event register value bit at position 10 to 1.<br/> Read <a href="#statusRegisterSetDesc" title="General SCPI Status register description">SCPI register set</a> section to get closer infos about the SCPI register system in general.</div>
        <div class="example">&gt;&gt; STAT:OPER:NTR 260</div>
        <div class="info">The default value after reboot or <a class="linkSCPI" title="Clears all SCPI event status registers" href="#CLS">CLS</a> is 0. So no condition changes from bit 1 to 0 update the event register.</div>
      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="STAT">
        <span class="scpi" id="STAT:OPER:NTR?">STAT:OPER:NTR?</span><span class="briefScpi">Queries the SCPI Operation Negative Transition register</span>
      </div>
      <div class="content">
        <div class="scpiLong">STATus:OPERation:NTRansition?</div>
        <div class="resultDocu">Response<ul>
          <li><span class="param">bitmask</span><span class="paramType">uint</span> <span class="paramDesc">16 bit wide bit mask for positive transition register.</span></li>
        </ul></div>

        <div class="details">Use this command to query the SCPI 16 bit wide Operation Negative Transition register bit mask. The register content is a bit mask with a 1 bit for every condition change that should be mapped to the operation event register <a class="linkSCPI" title="Queries the SCPI Operation Event register" href="#STAT:OPER?">STAT:OPER?</a>. For closer details about Negative Transition register read description <a class="linkSCPI" title="Sets the SCPI Operation Negative Transition register" href="#STAT:OPER:NTR">STAT:OPER:NTR</a>. Read <a href="#statusRegisterSetDesc" title="General SCPI Status register description">SCPI register set</a> section to get closer infos about the SCPI register system in general.</div>
        <div class="example">&gt;&gt; STAT:OPER:NTR?<br />&lt;&lt; 260</div>
        <div class="info">The default value after reboot or <a class="linkSCPI" title="Clears all SCPI event status registers" href="#CLS">CLS</a> is 0. So no condition changes from bit 1 to 0 update the event register.</div>
      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="STAT">
        <span class="scpi" id="STAT:OPER:PTR">STAT:OPER:PTR</span><span class="briefScpi">Sets the SCPI Operation Positive Transition register</span>
      </div>
      <div class="content">
        <div class="scpiLong">STATus:OPERation:PTRansition <div class="tooltip" data-id="171">&lt;bitmask&gt;</div></div>
        <div class="paramDocu">Parameters<ul>
              <li id="171"><span class="param">bitmask</span><span class="paramType">uint</span> <span class="paramDesc">16 bit wide bit mask for positive transition register.</span></li>
        </ul></div>

        <div class="details">Use this command to set the SCPI 16 bit wide Operation Positive Transition register bit mask. The register content is a bit mask with a 1 bit for every condition change that should be mapped to the Operation Event register <a class="linkSCPI" title="Queries the SCPI Operation Event register" href="#STAT:OPER?">STAT:OPER?</a>. As it is the positive transition register, only condition register bit changes from 0 to 1 update the bits in the event register. To get the meaning of the single bits read the <a class="linkSCPI" title="Queries the SCPI Operation Condition register" href="#STAT:OPER:COND?">STAT:OPER:COND?</a> description. For example if you set this register value to 4 (binary: 100 bit at position 3) and the Powermeter changes the range (condition register at position 3) it will also set the event register value bit at position 3 to 1.<br/> Read <a href="#statusRegisterSetDesc" title="General SCPI Status register description">SCPI register set</a> section to get closer infos about the SCPI register system in general.</div>
        <div class="example">&gt;&gt; STAT:OPER:PTR 36</div>
        <div class="info">The default value after reboot or <a class="linkSCPI" title="Clears all SCPI event status registers" href="#CLS">CLS</a> is 65535 (all bits 1). So all condition changes from bit 0 to 1 update the event register.</div>
      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="STAT">
        <span class="scpi" id="STAT:OPER:PTR?">STAT:OPER:PTR?</span><span class="briefScpi">Queries the SCPI Operation Positive Transition register</span>
      </div>
      <div class="content">
        <div class="scpiLong">STATus:OPERation:PTRansition?</div>
        <div class="resultDocu">Response<ul>
          <li><span class="param">bitmask</span><span class="paramType">uint</span> <span class="paramDesc">16 bit wide bit mask for positive transition register.</span></li>
        </ul></div>

        <div class="details">Use this command to query the SCPI 16 bit wide Operation Positive Transition register bit mask. The register content is a bit mask with a 1 bit for every condition change that should be mapped to the operation event register <a class="linkSCPI" title="Queries the SCPI Operation Event register" href="#STAT:OPER?">STAT:OPER?</a>. For closer details about Positive Transition register read description <a class="linkSCPI" title="Sets the SCPI Operation Positive Transition register" href="#STAT:OPER:PTR">STAT:OPER:PTR</a>.<br/> Read <a href="#statusRegisterSetDesc" title="General SCPI Status register description">SCPI register set</a> section to get closer infos about the SCPI register system in general.</div>
        <div class="example">&gt;&gt; STAT:OPER:PTR?<br />&lt;&lt; 65280</div>
        <div class="info">The default value after reboot or <a class="linkSCPI" title="Clears all SCPI event status registers" href="#CLS">CLS</a> is 65535 (all bits 1). So all condition changes from bit 0 to 1 update the event register.</div>
      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="STAT">
        <span class="scpi" id="STAT:PRES">STAT:PRES</span><span class="briefScpi">Pre-sets all SCPI status register with default values</span>
      </div>
      <div class="content">
        <div class="scpiLong">STATus:PRESet</div>
        <div class="details">Use this command to pre set all SCPI status registers with default values. All ENABle and NTRansition Registers are set to 0. The PTRansition registers are set to 65535. The event registers are cleared to 0.</div>
        <div class="example">&gt;&gt; STAT:PRES</div>

      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="STAT">
        <span class="scpi" id="STAT:QUES?">STAT:QUES?</span><span class="briefScpi">Queries the SCPI Questionable Event register</span>
      </div>
      <div class="content">
        <div class="scpiLong">STATus:QUEStionable[:EVENt]?</div>
        <div class="resultDocu">Response<ul>
          <li><span class="param">bitmask</span><span class="paramType">uint</span> <span class="paramDesc">16 bit wide event register bit mask.</span></li>
        </ul></div>

        <div class="details">Use this command to query the SCPI 16 bit wide Questionable Event register bit mask. The Questionable register set monitors the validity of the measurement results.<br/> Reading this event register automatically resets the register back to 0. The register content is a bit mask with a 1 bit for every event occurred since the register has been read the last time. If the same event occurs multiple times the event bit stays 1. To get the meaning of the single bits read the <a class="linkSCPI" title="Queries the SCPI Questionable Condition register" href="#STAT:QUES:COND?">STAT:QUES:COND?</a> description. The event register can store a change of the Questionable Condition register. The two transition registers are used to mask changes of the condition register to the event register. If a certain bit in the condition register is set from 0 to 1 and the bit at the same position in the positive transition register <a class="linkSCPI" title="Sets the SCPI Questionable Positive Transition register" href="#STAT:QUES:PTR">STAT:QUES:PTR</a> is set 1, the event register at the bit position is set to 1. It is also possible to detect condition register changes from 1 back to 0 using the negative transition register <a class="linkSCPI" title="Sets the SCPI Questionable Negative Transition register" href="#STAT:QUES:NTR">STAT:QUES:NTR</a> following the same logic.<br/> Using the event register makes sense for short lasting conditions like a small peak causing overflow of measure range for example. Changes of the event register can be summarized to the SCPI status register bit 3 if enabled by <a class="linkSCPI" title="Sets the SCPI Questionable Enable register" href="#STAT:QUES:ENAB">STAT:QUES:ENAB</a>. To read the SCPI status register call <a class="linkSCPI" title="Status byte query" href="#STB?">STB?</a>. After reboot or <a class="linkSCPI" title="Clears all SCPI event status registers" href="#CLS">CLS</a> the events are all reset to 0. Read <a href="#statusRegisterSetDesc" title="General SCPI Status register description">SCPI register set</a> section to get closer infos about the SCPI register system in general.</div>
        <div class="example">&gt;&gt; STAT:QUES?<br />&lt;&lt; 253</div>
        <div class="note">Some events might get lost if same event occurs multiple times without the event register being read in between.</div>
        <div class="info">Reading the event register automatically resets register to 0. To get the events bit positions read <a class="linkSCPI" title="Queries the SCPI Questionable Condition register" href="#STAT:QUES:COND?">STAT:QUES:COND?</a>.</div>
      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="STAT">
        <span class="scpi" id="STAT:QUES:COND?">STAT:QUES:COND?</span><span class="briefScpi">Queries the SCPI Questionable Condition register</span>
      </div>
      <div class="content">
        <div class="scpiLong">STATus:QUEStionable:CONDition?</div>
        <div class="resultDocu">Response<ul>
          <li><span class="param">bitmask</span><span class="paramType">uint</span> <span class="paramDesc">16 bit wide condition register bit mask.</span></li>
        </ul></div>

        <div class="details">Use this command to query the SCPI 16 bit wide Questionable Condition register bit mask. The Questionable register set monitors the validity of the measurement results.<br/> The register content is a bit mask with a 1 bit for every condition being active at the moment. Be aware some conditions might only last for a very short time period like a small peak causing overflow of measure range for example. In this case using the event register <a class="linkSCPI" title="Queries the SCPI Questionable Event register" href="#STAT:QUES?">STAT:QUES?</a> makes more sense. Configure the positive <a class="linkSCPI" title="Sets the SCPI Questionable Positive Transition register" href="#STAT:QUES:PTR">STAT:QUES:PTR</a> or negative <a class="linkSCPI" title="Sets the SCPI Questionable Negative Transition register" href="#STAT:QUES:NTR">STAT:QUES:NTR</a> transition register to map changes of the condition register to the event register. These are the masked events:<br/> <svg width="200px" viewBox="1560 1067 282 328" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">  <g>    <rect style="fill: #ffffff" x="1700.63" y="1070.68" width="140" height="324"/>    <rect style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x="1700.63" y="1070.68" width="140" height="324"/>    <text font-size="14.1111" style="fill: #000000;text-anchor:end;font-family:sans-serif;font-style:normal;font-weight:700" x="1831.63" y="1229.14">      <tspan x="1831.63" y="1229.14">STAT:</tspan>      <tspan x="1831.63" y="1246.78">QUEST</tspan>    </text>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1560.63" y="1080.68">    <tspan x="1560.63" y="1080.68">Questionable Voltage</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1560.63" y="1100.68">    <tspan x="1560.63" y="1100.68">Questionable Current</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1560.63" y="1120.68">    <tspan x="1560.63" y="1120.68">reserved</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="1580.63" y1="1082.68" x2="1696.16" y2="1082.68"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1688.39,1087.68 1698.39,1082.68 1688.39,1077.68 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1704.63" y="1086.68">    <tspan x="1704.63" y="1086.68">bit 0(1)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1560.63" y="1140.68">    <tspan x="1560.63" y="1140.68">Questionable Power</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="1580.63" y1="1102.68" x2="1696.16" y2="1102.68"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1688.39,1107.68 1698.39,1102.68 1688.39,1097.68 "/>  </g>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="1580.63" y1="1122.68" x2="1696.16" y2="1122.68"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1688.39,1127.68 1698.39,1122.68 1688.39,1117.68 "/>  </g>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="1580.63" y1="1142.68" x2="1696.16" y2="1142.68"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1688.39,1147.68 1698.39,1142.68 1688.39,1137.68 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1560.63" y="1160.68">    <tspan x="1560.63" y="1160.68">reserved</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="1580.63" y1="1162.68" x2="1696.16" y2="1162.68"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1688.39,1167.68 1698.39,1162.68 1688.39,1157.68 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1560.63" y="1180.68">    <tspan x="1560.63" y="1180.68">reserved</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="1580.63" y1="1182.68" x2="1696.16" y2="1182.68"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1688.39,1187.68 1698.39,1182.68 1688.39,1177.68 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1560.63" y="1200.68">    <tspan x="1560.63" y="1200.68">reserved</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="1580.63" y1="1202.68" x2="1696.16" y2="1202.68"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1688.39,1207.68 1698.39,1202.68 1688.39,1197.68 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1560.63" y="1220.68">    <tspan x="1560.63" y="1220.68">reserved</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="1580.63" y1="1222.68" x2="1696.16" y2="1222.68"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1688.39,1227.68 1698.39,1222.68 1688.39,1217.68 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1560.63" y="1240.68">    <tspan x="1560.63" y="1240.68">Questionable Calibrat</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="1580.63" y1="1242.68" x2="1696.16" y2="1242.68"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1688.39,1247.68 1698.39,1242.68 1688.39,1237.68 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1560.63" y="1260.68">    <tspan x="1560.63" y="1260.68">reserved</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="1580.63" y1="1262.68" x2="1696.16" y2="1262.68"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1688.39,1267.68 1698.39,1262.68 1688.39,1257.68 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1560.63" y="1280.68">    <tspan x="1560.63" y="1280.68">reserved</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="1580.63" y1="1282.68" x2="1696.16" y2="1282.68"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1688.39,1287.68 1698.39,1282.68 1688.39,1277.68 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1560.63" y="1300.68">    <tspan x="1560.63" y="1300.68">reserved</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="1580.63" y1="1302.68" x2="1696.16" y2="1302.68"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1688.39,1307.68 1698.39,1302.68 1688.39,1297.68 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1560.63" y="1320.68">    <tspan x="1560.63" y="1320.68">reserved</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="1580.63" y1="1322.68" x2="1696.16" y2="1322.68"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1688.39,1327.68 1698.39,1322.68 1688.39,1317.68 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1560.63" y="1380.68">    <tspan x="1560.63" y="1380.68">reserved</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="1580.63" y1="1382.68" x2="1696.16" y2="1382.68"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1688.39,1387.68 1698.39,1382.68 1688.39,1377.68 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1704.63" y="1106.68">    <tspan x="1704.63" y="1106.68">bit 1(2)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1704.63" y="1126.68">    <tspan x="1704.63" y="1126.68">bit 2(4)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1704.63" y="1146.68">    <tspan x="1704.63" y="1146.68">bit 3(8)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1704.63" y="1166.68">    <tspan x="1704.63" y="1166.68">bit 4(16)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1704.63" y="1186.68">    <tspan x="1704.63" y="1186.68">bit 5(32)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1704.63" y="1206.68">    <tspan x="1704.63" y="1206.68">bit 6(64)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1704.63" y="1226.68">    <tspan x="1704.63" y="1226.68">bit 7(128)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1704.63" y="1246.68">    <tspan x="1704.63" y="1246.68">bit 8(256)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1704.63" y="1266.68">    <tspan x="1704.63" y="1266.68">bit 9(512)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1704.63" y="1286.68">    <tspan x="1704.63" y="1286.68">bit 10(1024)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1704.63" y="1306.68">    <tspan x="1704.63" y="1306.68">bit 11(2048)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1704.63" y="1326.68">    <tspan x="1704.63" y="1326.68">bit 12(4096)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1704.63" y="1346.68">    <tspan x="1704.63" y="1346.68">bit 13(8192)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1704.63" y="1366.68">    <tspan x="1704.63" y="1366.68">bit 14(16384)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1704.63" y="1386.68">    <tspan x="1704.63" y="1386.68">bit 15(32768)</tspan>  </text>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1560.63" y="1340.68">    <tspan x="1560.63" y="1340.68">reserved</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="1580.63" y1="1342.68" x2="1696.16" y2="1342.68"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1688.39,1347.68 1698.39,1342.68 1688.39,1337.68 "/>  </g>  <text font-size="12.8" style="fill: #000000;text-anchor:start;font-family:sans-serif;font-style:normal;font-weight:normal" x="1560.63" y="1360.68">    <tspan x="1560.63" y="1360.68">reserved</tspan>  </text>  <g>    <line style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" x1="1580.63" y1="1362.68" x2="1696.16" y2="1362.68"/>    <polyline style="fill: none; fill-opacity:0; stroke-width: 2; stroke: #000000" points="1688.39,1367.68 1698.39,1362.68 1688.39,1357.68 "/>  </g></svg> <ul class="detailListing"> <li><b>bit 0:</b> Questionable Voltage measurement. <a class="linkSCPI" title="Measures the sensor averaged voltage" href="#MEAS:VOLT?">MEAS:VOLT?</a> will return infinity as signal out of measure range.</li> <li><b>bit 1:</b> Questionable Current measurement. <a class="linkSCPI" title="Measures the sensor averaged current" href="#MEAS:CURR?">MEAS:CURR?</a> will return infinity as signal out of measure range.</li> <li><b>bit 3:</b> Questionable Power measurement. <a class="linkSCPI" title="Measures the sensor averaged power in Watt or dBm" href="#MEAS?">MEAS?</a> will return infinity as signal out of measure range.</li> <li><b>bit 8:</b> Questionable Calibration. Device not calibrated or calibration due.</li> </ul> All other condition bits are reserved. Read <a href="#statusRegisterSetDesc" title="General SCPI Status register description">SCPI register set</a> section to get closer infos about the SCPI register system in general.</div>
        <div class="example">&gt;&gt; STAT:QUES:COND?<br />&lt;&lt; 2182</div>
        <div class="info">To test for short lasting conditions better use the event register <a class="linkSCPI" title="Queries the SCPI Questionable Event register" href="#STAT:QUES?">STAT:QUES?</a>.</div>
      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="STAT">
        <span class="scpi" id="STAT:QUES:ENAB">STAT:QUES:ENAB</span><span class="briefScpi">Sets the SCPI Questionable Enable register</span>
      </div>
      <div class="content">
        <div class="scpiLong">STATus:QUEStionable:ENABle <div class="tooltip" data-id="173">&lt;bitmask&gt;</div></div>
        <div class="paramDocu">Parameters<ul>
              <li id="173"><span class="param">bitmask</span><span class="paramType">uint</span> <span class="paramDesc">16 bit wide bit mask for enable register.</span></li>
        </ul></div>

        <div class="details">Use this command to set the SCPI 16 bit wide Questionable Enable register bit mask. The register content is a bit mask with a 1 bit for every enabled event that should be forwarded to the 8 bit wide SCPI status register bit 3. See <a class="linkSCPI" title="Status byte query" href="#STB?">STB?</a>. To get the meaning of the single bits read the <a class="linkSCPI" title="Queries the SCPI Questionable Condition register" href="#STAT:QUES:COND?">STAT:QUES:COND?</a> description. This enable register acts like a filter mask for all operation events (see <a class="linkSCPI" title="Queries the SCPI Questionable Event register" href="#STAT:QUES?">STAT:QUES?</a>) that should be forwarded to the SCPI status register. If the enable mask is set with all bits 1, the SCPI status register bit 3 is set to 1 whenever one or multiple events are set in <a class="linkSCPI" title="Queries the SCPI Questionable Event register" href="#STAT:QUES?">STAT:QUES?</a> register.<br /> The idea behind this abstraction concept is to read the SCPI status register and check for bit 3 to change. Whenever the bit changed the user then may query this Questionable Event register to get the details. Changing the enable register with active events might directly update the summarized SCPI status register bit.<br/> Read <a href="#statusRegisterSetDesc" title="General SCPI Status register description">SCPI register set</a> section to get closer infos about the SCPI register system in general.</div>
        <div class="example">&gt;&gt; STAT:QUES:ENAB 65280</div>
        <div class="note">Ensure that the positive and negative transition registers are set accordingly to change the event register. Forwarding only works if event register gets updated.</div>
        <div class="info">The default value after reboot or <a class="linkSCPI" title="Clears all SCPI event status registers" href="#CLS">CLS</a> is 0. So no events are forwarded.</div>
      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="STAT">
        <span class="scpi" id="STAT:QUES:ENAB?">STAT:QUES:ENAB?</span><span class="briefScpi">Queries the SCPI Questionable Enable register</span>
      </div>
      <div class="content">
        <div class="scpiLong">STATus:QUEStionable:ENABle?</div>
        <div class="resultDocu">Response<ul>
          <li><span class="param">bitmask</span><span class="paramType">uint</span> <span class="paramDesc">16 bit wide enable register bit mask.</span></li>
        </ul></div>

        <div class="details">Use this command to query the SCPI 16 bit wide Questionable Enable register bit mask. The register content is a bit mask with a 1 bit for every enabled event that should be forwarded to the 8 bit wide SCPI status register bit 7. To get the meaning of the single bits read the <a class="linkSCPI" title="Queries the SCPI Questionable Condition register" href="#STAT:QUES:COND?">STAT:QUES:COND?</a> description. For closer details about the Questionable Enable register read <a class="linkSCPI" title="Sets the SCPI Questionable Enable register" href="#STAT:QUES:ENAB">STAT:QUES:ENAB</a>.  Read <a href="#statusRegisterSetDesc" title="General SCPI Status register description">SCPI register set</a> section to get closer infos about the SCPI register system in general.</div>
        <div class="example">&gt;&gt; STAT:QUES:ENAB<br />&lt;&lt; 65280</div>
        <div class="note">Ensure that the positive or negative transition registers are set accordingly to change the event register. Forwarding only works if event register gets updated.</div>
        <div class="info">The default value after reboot or <a class="linkSCPI" title="Clears all SCPI event status registers" href="#CLS">CLS</a> is 0. So no events are forwarded.</div>
      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="STAT">
        <span class="scpi" id="STAT:QUES:NTR">STAT:QUES:NTR</span><span class="briefScpi">Sets the SCPI Questionable Negative Transition register</span>
      </div>
      <div class="content">
        <div class="scpiLong">STATus:QUEStionable:NTRansition <div class="tooltip" data-id="175">&lt;bitmask&gt;</div></div>
        <div class="paramDocu">Parameters<ul>
              <li id="175"><span class="param">bitmask</span><span class="paramType">uint</span> <span class="paramDesc">16 bit wide bit mask for positive transition register.</span></li>
        </ul></div>

        <div class="details">Use this command to set the SCPI 16 bit wide Questionable Negative Transition register bit mask. The register content is a bit mask with a 1 bit for every condition change that should be mapped to the operation event register <a class="linkSCPI" title="Queries the SCPI Questionable Event register" href="#STAT:QUES?">STAT:QUES?</a>. As it is the negative transition register, only condition register bit changes from 1 back to 0 update the bits in the event register. To get the meaning of the single bits read the <a class="linkSCPI" title="Queries the SCPI Questionable Condition register" href="#STAT:QUES:COND?">STAT:QUES:COND?</a> description.<br/> Read <a href="#statusRegisterSetDesc" title="General SCPI Status register description">SCPI register set</a> section to get closer infos about the SCPI register system in general.</div>
        <div class="example">&gt;&gt; STAT:QUES:NTR 260</div>
        <div class="info">The default value after reboot or <a class="linkSCPI" title="Clears all SCPI event status registers" href="#CLS">CLS</a> is 0. So no condition changes from bit 1 to 0 update the event register.</div>
      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="STAT">
        <span class="scpi" id="STAT:QUES:NTR?">STAT:QUES:NTR?</span><span class="briefScpi">Queries the SCPI Questionable Negative Transition register</span>
      </div>
      <div class="content">
        <div class="scpiLong">STATus:QUEStionable:NTRansition?</div>
        <div class="resultDocu">Response<ul>
          <li><span class="param">bitmask</span><span class="paramType">uint</span> <span class="paramDesc">16 bit wide bit mask for positive transition register.</span></li>
        </ul></div>

        <div class="details">Use this command to query the SCPI 16 bit wide Questionable Negative Transition register bit mask. The register content is a bit mask with a 1 bit for every condition change that should be mapped to the operation event register <a class="linkSCPI" title="Queries the SCPI Questionable Event register" href="#STAT:QUES?">STAT:QUES?</a>. For closer details about Negative Transition register read description <a class="linkSCPI" title="Sets the SCPI Questionable Negative Transition register" href="#STAT:QUES:NTR">STAT:QUES:NTR</a>.<br /> Read <a href="#statusRegisterSetDesc" title="General SCPI Status register description">SCPI register set</a> section to get closer infos about the SCPI register system in general.</div>
        <div class="example">&gt;&gt; STAT:QUES:NTR?<br />&lt;&lt; 260</div>
        <div class="info">The default value after reboot or <a class="linkSCPI" title="Clears all SCPI event status registers" href="#CLS">CLS</a> is 0. So no condition changes from bit 1 to 0 update the event register.</div>
      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="STAT">
        <span class="scpi" id="STAT:QUES:PTR">STAT:QUES:PTR</span><span class="briefScpi">Sets the SCPI Questionable Positive Transition register</span>
      </div>
      <div class="content">
        <div class="scpiLong">STATus:QUEStionable:PTRansition <div class="tooltip" data-id="174">&lt;bitmask&gt;</div></div>
        <div class="paramDocu">Parameters<ul>
              <li id="174"><span class="param">bitmask</span><span class="paramType">uint</span> <span class="paramDesc">16 bit wide bit mask for positive transition register.</span></li>
        </ul></div>

        <div class="details">Use this command to set the SCPI 16 bit wide Questionable Positive Transition register bit mask. The register content is a bit mask with a 1 bit for every condition change that should be mapped to the Questionable Event register <a class="linkSCPI" title="Queries the SCPI Questionable Event register" href="#STAT:QUES?">STAT:QUES?</a>. As it is the positive transition register, only condition register bit changes from 0 to 1 update the bits in the event register. To get the meaning of the single bits read the <a class="linkSCPI" title="Queries the SCPI Questionable Condition register" href="#STAT:QUES:COND?">STAT:QUES:COND?</a> description. For example if you set this register value to 8 (binary: 1000 bit at position 3) and the Powermeter measures a short peak causing a overflow of measure range the Questionable Power Measurement flag will become true for a short moment it will also set the event register value bit at position 3 to 1. <br/> Read <a href="#statusRegisterSetDesc" title="General SCPI Status register description">SCPI register set</a> section to get closer infos about the SCPI register system in general.</div>
        <div class="example">&gt;&gt; STAT:QUES:PTR 8</div>
        <div class="info">The default value after reboot or <a class="linkSCPI" title="Clears all SCPI event status registers" href="#CLS">CLS</a> is 65535 (all bits 1). So all condition changes from bit 0 to 1 update the event register.</div>
      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="STAT">
        <span class="scpi" id="STAT:QUES:PTR?">STAT:QUES:PTR?</span><span class="briefScpi">Queries the SCPI Questionable Positive Transition register</span>
      </div>
      <div class="content">
        <div class="scpiLong">STATus:QUEStionable:PTRansition?</div>
        <div class="resultDocu">Response<ul>
          <li><span class="param">bitmask</span><span class="paramType">uint</span> <span class="paramDesc">16 bit wide bit mask for positive transition register.</span></li>
        </ul></div>

        <div class="details">Use this command to query the SCPI 16 bit wide Questionable Positive Transition register bit mask. The register content is a bit mask with a 1 bit for every condition change that should be mapped to the operation event register <a class="linkSCPI" title="Queries the SCPI Questionable Event register" href="#STAT:QUES?">STAT:QUES?</a>. For closer details about Positive Transition register read description <a class="linkSCPI" title="Sets the SCPI Questionable Positive Transition register" href="#STAT:QUES:PTR">STAT:QUES:PTR</a>.<br/> Read <a href="#statusRegisterSetDesc" title="General SCPI Status register description">SCPI register set</a> section to get closer infos about the SCPI register system in general.</div>
        <div class="example">&gt;&gt; STAT:QUES:PTR?<br />&lt;&lt; 65280</div>
        <div class="info">The default value after reboot or <a class="linkSCPI" title="Clears all SCPI event status registers" href="#CLS">CLS</a> is 65535 (all bits 1). So all condition changes from bit 0 to 1 update the event register.</div>
      </div>
    </div>
    <hr>
    <p id="SYST"><a href="#SYST:ERR?" data-subsystem="SYST" class="unfoldSection">Unfold all SYST Commands</a></p>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="SYST">
        <span class="scpi" id="SYST:ERR?">SYST:ERR?</span><span class="briefScpi">Reads and removes oldest element of SCPI error queue</span>
      </div>
      <div class="content">
        <div class="scpiLong">SYSTem:ERRor[:NEXT]?</div>
        <div class="resultDocu">Response<ul>
          <li><span class="param">error</span><span class="paramType">int</span> <span class="paramDesc">SPCI error code. If no error occurred result will be 0.</span></li>
          <li><span class="param">errDesc</span><span class="paramType">string</span> <span class="paramDesc">error code English description text.</span></li>
        </ul></div>

        <div class="details">Call to test if there where errors during command execution or asynchronous device errors. The device internally uses a error queue to store SCPI errors. This command returns and removes the oldest element of the queue. Call multiple times until queue is empty and function returns 0, "No error". The internal queue length is limited. So if you do not keep the queue empty you might loose errors when queue is full.</div>
        <div class="example">&gt;&gt; SYST:ERR?<br />&lt;&lt; -113,"Undefined header"</div>

      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="SYST">
        <span class="scpi" id="SYST:ERR:COUN?">SYST:ERR:COUN?</span><span class="briefScpi">Tests how many errors are stored in SCPI error queue</span>
      </div>
      <div class="content">
        <div class="scpiLong">SYSTem:ERRor:COUNt?</div>
        <div class="resultDocu">Response<ul>
          <li><span class="param">errDesc</span><span class="paramType">uint</span> <span class="paramDesc">Amount of errors stored in SCPI error queue. 0 when empty.</span></li>
        </ul></div>

        <div class="details">For closer details about SCPI error queue read <a class="linkSCPI" title="Reads and removes oldest element of SCPI error queue" href="#SYST:ERR?">SYST:ERR?</a>.</div>
        <div class="example">&gt;&gt; SYST:ERR:COUN?<br />&lt;&lt; 1</div>

      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="SYST">
        <span class="scpi" id="SYST:SENS:IDN?">SYST:SENS:IDN?</span><span class="briefScpi">Sensor head identification query</span>
      </div>
      <div class="content">
        <div class="scpiLong">SYSTem:SENSor:IDN?</div>
        <div class="resultDocu">Response<ul>
          <li><span class="param">name</span><span class="paramType">string</span> <span class="paramDesc">name of sensor</span></li>
          <li><span class="param">serial</span><span class="paramType">string</span> <span class="paramDesc">serial number of sensor</span></li>
          <li><span class="param">calibrationDate</span><span class="paramType">string</span> <span class="paramDesc">calibration date of sensor. Format dd-MonthStr-YYYY.</span></li>
          <li><span class="param">type</span><span class="paramType">uint</span> <span class="paramDesc">type of sensor. See list in detail description.</span></li>
          <li><span class="param">subtype</span><span class="paramType">uint</span> <span class="paramDesc">subtype of sensor head. See list in detail description.</span></li>
          <li><span class="param">flags</span><span class="paramType">uint</span> <span class="paramDesc">bitmask with sensor capabilities. See lis tin detail description.</span></li>
        </ul></div>

        <div class="details">Use this command to query the light sensor head identification. Next to the name, serial and calibration date you get 3 numbers. The following listing defines the sensor types: <ul class="detailListing"> <li><b>1:</b> Single photodiode sensor</li> <li><b>2:</b> Thermopile sensor</li> <li><b>5:</b> Four Quadrant Thermopile sensor</li> </ul> These are the sensor subtypes: <ul class="detailListing"> <li><b>1:</b> Adapter sensor without head correction.</li> <li><b>2:</b> Sensor without head temperature sensor</li> <li><b>3:</b> Sensor filter slider. (Photodiode only)</li> <li><b>18:</b> Sensor with temperature sensor</li> </ul> The sensor capability flags are a bitmask with a bit set to 1 if the sensor supports the following function <ul class="detailListing"> <li><b>bit 0(1 dec):</b> Sensor is a power sensor</li> <li><b>bit 1(2 dec):</b> Sensor is a energy sensor</li> <li><b>bit 4(16 dec):</b> Sensor supports setting responsivity parameter (Adapter only)</li> <li><b>bit 5(32 dec):</b> Sensor supports setting wavelength parameter (Non Adapter only)</li> <li><b>bit 8(256 dec):</b> Sensor head supports measurement of head temperature</li> </ul></div>
        <div class="example">&gt;&gt; SYST:SENS:IDN?<br />&lt;&lt; "S120C","16060232","2-JUN-2021",1,18,33</div>

      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="SYST">
        <span class="scpi" id="SYST:SER:TRAN:BAUD">SYST:SER:TRAN:BAUD</span><span class="briefScpi">Changes baudrate of SCPI serial interface</span>
      </div>
      <div class="content">
        <div class="scpiLong">SYSTem:SERial:TRANsmit:BAUD <div class="tooltip" data-id="168">&lt;baudrate&gt;</div></div>
        <div class="paramDocu">Parameters<ul>
              <li id="168"><span class="param">baudrate</span><span class="paramType">uint</span> <span class="paramDesc">Query minimal, default or maximal used baudrate.</span></li>
        </ul></div>

        <div class="details">Use this command to change the currently configured baudrate of SCPI serial interface. The baudrate gets changed immediately and is stored persistently. The Powermeter supports the following baudrates: <ul class="detailListing"> <li>1200</li> <li>1800</li> <li>2400</li> <li>4800</li> <li>7200</li> <li>9600</li> <li>14400</li> <li>19200</li> <li>28800</li> <li>33600</li> <li>38400</li> <li>57600</li> <li>115200 (default)</li> <li>128000</li> <li>230400</li> <li>460800</li></div>
        <div class="example">&gt;&gt; SYST:SER:TRAN:BAUD 9600</div>

      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="SYST">
        <span class="scpi" id="SYST:SER:TRAN:BAUD?">SYST:SER:TRAN:BAUD?</span><span class="briefScpi">Queries baudrate of SCPI serial interface</span>
      </div>
      <div class="content">
        <div class="scpiLong">SYSTem:SERial:TRANsmit:BAUD? <div class="tooltip" data-id="169">&lt;special&gt;</div></div>
        <div class="paramDocu">Parameters<ul>
           <li id="169"><span class="optParam">special</span><span class="paramType">MIN,MAX,DEF</span> <span class="paramDesc">Optional! Query minimal, default or maximal used baudrate.</span></li>
        </ul></div>

        <div class="resultDocu">Response<ul>
          <li><span class="param">baudrate</span><span class="paramType">uint</span> <span class="paramDesc">currently configured baudrate of serial SCPI interface</span></li>
        </ul></div>

        <div class="details">Use this command to query the currently configured baudrate of SCPI serial interface. For closer details about supported baudrates read <a class="linkSCPI" title="Changes baudrate of SCPI serial interface" href="#SYST:SER:TRAN:BAUD">SYST:SER:TRAN:BAUD</a>.</div>
        <div class="example">&gt;&gt; SYST:SER:TRAN:BAUD?<br />&lt;&lt; 115200</div>

      </div>
    </div>
    <div class="noSplit">
      <div class="collapsible" data-subsystem="SYST">
        <span class="scpi" id="SYST:VERS?">SYST:VERS?</span><span class="briefScpi">Returns SCPI version string</span>
      </div>
      <div class="content">
        <div class="scpiLong">SYSTem:VERSion?</div>
        <div class="resultDocu">Response<ul>
          <li><span class="param">vers</span><span class="paramType">string</span> <span class="paramDesc">SCPI version string without quotes. See details for format.</span></li>
        </ul></div>

        <div class="details">Response format YYYY.V. YYYY is year of SCPI standard. V is the revision of standard.</div>
        <div class="example">&gt;&gt; SYST:VER?<br />&lt;&lt; 1999.0</div>

      </div>
    </div>


  </div>
  <script>
    //Add event handler to all SCPI command descriptions to hide or show all commands
    var coll = document.getElementsByClassName("collapsible");
    for (let i = 0; i < coll.length; i++) {
      coll[i].addEventListener("click", (e) => {
        let event = e.target || e.currentTarget;
        //Navigate DOM to collapsible div
        let content;
        if(event.nodeName == "SPAN") {
          content = event.parentElement.nextElementSibling;
        } else {
          content = event.nextElementSibling;
        }
          
        //Toggle fold/unfold by maxHeight style attribute
        if (content.style.maxHeight){
          content.style.maxHeight = null;
        } else {
          content.style.maxHeight = content.scrollHeight + "px";
        } 
      });
    }

    //Add event handler to all SCPI cross reference links to ensure link destination is visible
    var scpiLinks = document.getElementsByClassName("linkSCPI");
    for(let i = 0; i < scpiLinks.length; i++) {
      scpiLinks[i].addEventListener("click", (e) => {
        let event = e.target || e.currentTarget;
        if(event.hash) {
          //Get element of id stored in anchor element
          let destSpan = document.getElementById(event.hash.substring(1));
          if(destSpan) {
            //Navigate DOM to collapsible div
            let destEl = destSpan.parentElement.nextElementSibling;
            //Ensure to show div if not unfolded already
            if(destEl && !destEl.style.maxHeight) {
              destEl.style.maxHeight = destEl.scrollHeight + "px";
            }
          }
        }
      });
    }
    
    //Add event handlers to show and hide tooltips of SCPI commands
    var tooltips = document.getElementsByClassName("tooltip");    
    for (let i = 0; i < tooltips.length; i++) 
    {
      tooltips[i].addEventListener("mouseenter", (e) => {
        let event = e.target || e.currentTarget;
        document.getElementById(event.getAttribute('data-id')).classList.add("highlightParam");
      });
      tooltips[i].addEventListener("mouseleave", (e) => {
        let event = e.target || e.currentTarget;
        document.getElementById(event.getAttribute('data-id')).classList.remove("highlightParam");
      });
    }

    //Add event handler for SCPI subsystem fold or unfold operation 
    var unfolder = document.getElementsByClassName("unfoldSection");
    for (let i = 0; i < unfolder.length; i++) {
      unfolder[i].addEventListener("click", (e) => {
        let event = e.target || e.currentTarget;
        
        //Should we hide or show all entries?
        let isFold = event.text.startsWith('Unfold')
        //Get Subsystem for this event handler
        let subsystem = event.getAttribute('data-subsystem')
    
        //List of all SCPI command containers for given SCPI subsystem
        var affected = document.querySelectorAll('div[data-subsystem="'+subsystem+'"]');
    
        for(let j = 0; j < affected.length; j++) {
          //Navigate DOM to collapsible div
          let content = affected[j].nextElementSibling;
          //Test if we should hide or show all entries.
          if(isFold) {
            content.style.maxHeight = null;
          }
          else {
            content.style.maxHeight = content.scrollHeight + "px";
          }
    
          affected[j].click()
        }
    
        //Replace SCPI subsystem unfold/fold link text label
        if(isFold)
          event.text = event.text.replace("Unfold", "Fold");
        else
          event.text = event.text.replace("Fold", "Unfold");
      });
    }

    //Add event handler for global SCPI fold or unfold operation 
    document.getElementById("foldAll").addEventListener("click", (e) => {
      let event = e.target || e.currentTarget;
      //Should we hide or show all entries?
      let isFold = event.text.startsWith('Unfold');
        
      //List of all SCPI command container
      var affected = document.querySelectorAll('div[data-subsystem]');
        
      for(let j = 0; j < affected.length; j++) {
        //Navigate DOM to collapsible div
        let content = affected[j].nextElementSibling;
        //Test if we should hide or show all entries.
        if(isFold) {
          content.style.maxHeight = null;
        }
        else {
          content.style.maxHeight = content.scrollHeight + "px";
        }

        affected[j].click()
      }
        
      //Replace global unfold/fold link text label
      if(isFold)
        event.text = event.text.replace("Unfold", "Fold");
      else
        event.text = event.text.replace("Fold", "Unfold");
      
      //Replace all SCPI subsystem fold/unfold text labes
      otherFolders = document.querySelectorAll('a[data-subsystem]');
      for(let j = 0; j < otherFolders.length; j++) {
        if(isFold)
          otherFolders[j].text = otherFolders[j].text.replace("Unfold", "Fold");
        else
          otherFolders[j].text = otherFolders[j].text.replace("Fold", "Unfold");
      }
    });
    </script>
  </body>
</html>
