[uuid(7cdf0079-9593-466d-a848-0f3013a603cd), helpfile("TLPM.hlp"), helpcontext(215), helpstring("Thorlabs Power Meter Instrument Driver")]
library TLPM{
typedef [public] long           ViAttr;
typedef [public] long           ViStatus;
typedef [public] long*          ViAStatus;
typedef [public] long*          ViPStatus;
typedef [public] short        ViBoolean;
typedef [public] short*       ViABoolean;
typedef [public] short*       ViPBoolean;
typedef [public] char           ViChar;
typedef [public] LPSTR          ViAChar;
typedef [public] LPSTR          ViPChar;
typedef [public] unsigned char  ViByte;
typedef [public] unsigned char* ViAByte;
typedef [public] unsigned char* ViPByte;
typedef [public] unsigned char           ViInt8;
typedef [public] unsigned char*         ViAInt8;
typedef [public] unsigned char*         ViPInt8;
typedef [public] unsigned char  ViUInt8;
typedef [public] unsigned char* ViAUInt8;
typedef [public] unsigned char* ViPUInt8;
typedef [public] short          ViInt16;
typedef [public] short*         ViAInt16;
typedef [public] short*         ViPInt16;
typedef [public] short          ViUInt16;
typedef [public] short*         ViAUInt16;
typedef [public] short*         ViPUInt16;
typedef [public] long           ViInt32;
typedef [public] long*          ViAInt32;
typedef [public] long*          ViPInt32;
typedef [public] long           ViUInt32;
typedef [public] long*          ViAUInt32;
typedef [public] long*          ViPUInt32;
typedef [public] float          ViReal32;
typedef [public] float*         ViAReal32;
typedef [public] float*         ViPReal32;
typedef [public] double         ViReal64;
typedef [public] double*        ViAReal64;
typedef [public] double*        ViPReal64;
typedef [public] LPSTR ViBuf;
typedef [public] LPSTR ViPBuf;
typedef [public] LPSTR           ViString;
typedef [public] LPSTR*          ViAString;
typedef [public] LPSTR          ViPString;
typedef [public] LPSTR           ViRsrc;
typedef [public] LPSTR*          ViARsrc;
typedef [public] LPSTR          ViPRsrc;
typedef [public] LPSTR           ViKeyId;
typedef [public] LPSTR          ViPKeyId;
typedef [public] LPSTR          ViClass;
typedef [public] LPSTR          ViPClass;
typedef [public] long           ViAddr;
typedef [public] long*           ViAAddr;
typedef [public] long*           ViPAddr;
typedef [public] long           ViVersion;
typedef [public] long*          ViAVersion;
typedef [public] long*          ViPVersion;
typedef [public] long           ViAccessMode;
typedef [public] long           ViJobId;
typedef [public] long*          ViPJobId;
typedef [public] long           ViJobStatus;
typedef [public] long*          ViPJobStatus;
typedef [public] long           ViEventType;
typedef [public] long*          ViPEventType;
typedef [public] long           ViFilterType;
typedef [public] long*          ViPFilterType;
typedef [public] long           ViBusAddress;
typedef [public] long*          ViPBusAddress;
typedef [public] long           ViBusSize;
typedef [public] long           ViHndlr;
typedef [public] long           ViObject;
typedef [public] long           ViSession;
typedef [public] long*           ViPSession;
typedef [public] long           ViEvent;
typedef [public] long*           ViPEvent;
typedef [public] long           ViEventFilter;
typedef [public] long           ViFindList;
typedef [public] long*          ViPFindList;
typedef [public] long           ViAttrState;
typedef [public] long*          ViPAttrState;
typedef [public] LPSTR          ViConstString;
typedef [public] __int64        ViBusAddress64;
typedef [public] __int64*       ViPBusAddress64;
typedef [public] __int64        ViInt64;
typedef [public] __int64*       ViAInt64;
typedef [public] __int64*       ViPInt64;
typedef [public] __int64        ViUInt64;
typedef [public] __int64*       ViAUInt64;
typedef [public] __int64*       ViPUInt64;
	[dllname("TLPM.dll"), helpstring("VISA Attributes")]
	module VISAAttributes {
		const long VI_ATTR_RSRC_NAME                   = 0xBFFF0002;
		const long VI_ATTR_RSRC_IMPL_VERSION           = 0x3FFF0003;
		const long VI_ATTR_RSRC_LOCK_STATE             = 0x3FFF0004;
		const long VI_ATTR_MAX_QUEUE_LENGTH            = 0x3FFF0005;
		const long VI_ATTR_USER_DATA                   = 0x3FFF0007;
		const long VI_ATTR_FDC_CHNL                    = 0x3FFF000D;
		const long VI_ATTR_FDC_MODE                    = 0x3FFF000F;
		const long VI_ATTR_FDC_GEN_SIGNAL_EN           = 0x3FFF0011;
		const long VI_ATTR_FDC_USE_PAIR                = 0x3FFF0013;
		const long VI_ATTR_SEND_END_EN                 = 0x3FFF0016;
		const long VI_ATTR_TERMCHAR                    = 0x3FFF0018;
		const long VI_ATTR_TMO_VALUE                   = 0x3FFF001A;
		const long VI_ATTR_GPIB_READDR_EN             = 0x3FFF001B;
		const long VI_ATTR_IO_PROT                     = 0x3FFF001C;
		const long VI_ATTR_ASRL_BAUD                   = 0x3FFF0021; 
		const long VI_ATTR_ASRL_DATA_BITS              = 0x3FFF0022; 
		const long VI_ATTR_ASRL_PARITY                 = 0x3FFF0023; 
		const long VI_ATTR_ASRL_STOP_BITS              = 0x3FFF0024; 
		const long VI_ATTR_ASRL_FLOW_CNTRL             = 0x3FFF0025; 
		const long VI_ATTR_RD_BUF_OPER_MODE            = 0x3FFF002A;
		const long VI_ATTR_WR_BUF_OPER_MODE            = 0x3FFF002D;
		const long VI_ATTR_SUPPRESS_END_EN             = 0x3FFF0036;
		const long VI_ATTR_TERMCHAR_EN                 = 0x3FFF0038;
		const long VI_ATTR_DEST_ACCESS_PRIV           = 0x3FFF0039;
		const long VI_ATTR_DEST_BYTE_ORDER            = 0x3FFF003A;
		const long VI_ATTR_SRC_ACCESS_PRIV            = 0x3FFF003C;
		const long VI_ATTR_SRC_BYTE_ORDER             = 0x3FFF003D;
		const long VI_ATTR_SRC_INCREMENT               = 0x3FFF0040;
		const long VI_ATTR_DEST_INCREMENT              = 0x3FFF0041;
		const long VI_ATTR_WIN_ACCESS_PRIV            = 0x3FFF0045;
		const long VI_ATTR_WIN_BYTE_ORDER             = 0x3FFF0047;
		const long VI_ATTR_CMDR_LA                     = 0x3FFF006B;
		const long VI_ATTR_MAINFRAME_LA                = 0x3FFF0070;
		const long VI_ATTR_WIN_BASE_ADDR               = 0x3FFF0098;
		const long VI_ATTR_WIN_SIZE                    = 0x3FFF009A;
		const long VI_ATTR_ASRL_AVAIL_NUM              = 0x3FFF00AC;
		const long VI_ATTR_MEM_BASE                    = 0x3FFF00AD;
		const long VI_ATTR_ASRL_CTS_STATE             = 0x3FFF00AE;
		const long VI_ATTR_ASRL_DCD_STATE             = 0x3FFF00AF;
		const long VI_ATTR_ASRL_DSR_STATE             = 0x3FFF00B1;
		const long VI_ATTR_ASRL_DTR_STATE             = 0x3FFF00B2;
		const long VI_ATTR_ASRL_END_IN                 = 0x3FFF00B3; 
		const long VI_ATTR_ASRL_END_OUT                = 0x3FFF00B4; 
		const long VI_ATTR_ASRL_RI_STATE              = 0x3FFF00BF;
		const long VI_ATTR_ASRL_RTS_STATE             = 0x3FFF00C0;
		const long VI_ATTR_WIN_ACCESS                  = 0x3FFF00C3;
		const long VI_ATTR_RM_SESSION                  = 0x3FFF00C4;
		const long VI_ATTR_VXI_LA                      = 0x3FFF00D5;
		const long VI_ATTR_MANF_ID                     = 0x3FFF00D9;
		const long VI_ATTR_MEM_SIZE                    = 0x3FFF00DD;
		const long VI_ATTR_MEM_SPACE                   = 0x3FFF00DE;
		const long VI_ATTR_MODEL_CODE                  = 0x3FFF00DF;
		const long VI_ATTR_SLOT                        = 0x3FFF00E8;
		const long VI_ATTR_INTF_INST_NAME             = 0xBFFF00E9;
		const long VI_ATTR_IMMEDIATE_SERV              = 0x3FFF0100; 
		const long VI_ATTR_INTF_PARENT_NUM             = 0x3FFF0101;
		const long VI_ATTR_RSRC_SPEC_VERSION           = 0x3FFF0170;
		const long VI_ATTR_INTF_TYPE                   = 0x3FFF0171;
		const long VI_ATTR_GPIB_PRIMARY_ADDR           = 0x3FFF0172;
		const long VI_ATTR_GPIB_SECONDARY_ADDR         = 0x3FFF0173;
		const long VI_ATTR_RSRC_MANF_NAME              = 0xBFFF0174;
		const long VI_ATTR_RSRC_MANF_ID                = 0x3FFF0175;
		const long VI_ATTR_INTF_NUM                    = 0x3FFF0176;
		const long VI_ATTR_TRIG_ID                     = 0x3FFF0177;
		const long VI_ATTR_GPIB_UNADDR_EN             = 0x3FFF0184;
		const long VI_ATTR_JOB_ID                      = 0x3FFF4006;
		const long VI_ATTR_EVENT_TYPE                  = 0x3FFF4010;
		const long VI_ATTR_SIGP_STATUS_ID              = 0x3FFF4011;
		const long VI_ATTR_RECV_TRIG_ID                = 0x3FFF4012;
		const long VI_ATTR_INTR_STATUS_ID             = 0x3FFF4023;
		const long VI_ATTR_STATUS                      = 0x3FFF4025;
		const long VI_ATTR_RET_COUNT                   = 0x3FFF4026;
		const long VI_ATTR_BUFFER                      = 0x3FFF4027;
		const long VI_ATTR_RECV_INTR_LEVEL            = 0x3FFF4041;
		const long VI_ATTR_RSRC_CLASS                  = 0xBFFF0001;
		const long VI_ATTR_GPIB_ATN_STATE              = 0x3FFF0057;
		const long VI_ATTR_GPIB_ADDR_STATE             = 0x3FFF005C;
		const long VI_ATTR_GPIB_CIC_STATE              = 0x3FFF005E;
		const long VI_ATTR_GPIB_NDAC_STATE             = 0x3FFF0062;
		const long VI_ATTR_GPIB_SRQ_STATE              = 0x3FFF0067;
		const long VI_ATTR_GPIB_SYS_CNTRL_STATE        = 0x3FFF0068;
		const long VI_ATTR_GPIB_HS488_CBL_LEN          = 0x3FFF0069;
		const long VI_ATTR_VXI_DEV_CLASS               = 0x3FFF006C;
		const long VI_ATTR_MANF_NAME                   = 0xBFFF0072;
		const long VI_ATTR_MODEL_NAME                  = 0xBFFF0077;
		const long VI_ATTR_VXI_VME_INTR_STATUS         = 0x3FFF008B;
		const long VI_ATTR_VXI_TRIG_STATUS             = 0x3FFF008D;
		const long VI_ATTR_VXI_VME_SYSFAIL_STATE       = 0x3FFF0094;
		const long VI_ATTR_ASRL_XON_CHAR               = 0x3FFF00C1;
		const long VI_ATTR_ASRL_XOFF_CHAR              = 0x3FFF00C2;
		const long VI_ATTR_GPIB_REN_STATE              = 0x3FFF0181;
		const long VI_ATTR_DEV_STATUS_BYTE             = 0x3FFF0189;
		const long VI_ATTR_FILE_APPEND_EN              = 0x3FFF0192;
		const long VI_ATTR_VXI_TRIG_SUPPORT            = 0x3FFF0194;
		const long VI_ATTR_TCPIP_ADDR                  = 0xBFFF0195;
		const long VI_ATTR_TCPIP_HOSTNAME              = 0xBFFF0196;
		const long VI_ATTR_TCPIP_PORT                  = 0x3FFF0197;
		const long VI_ATTR_TCPIP_DEVICE_NAME           = 0xBFFF0199;
		const long VI_ATTR_TCPIP_NODELAY               = 0x3FFF019A;
		const long VI_ATTR_TCPIP_KEEPALIVE             = 0x3FFF019B;
		const long VI_ATTR_4882_COMPLIANT              = 0x3FFF019F;
		const long VI_ATTR_USB_SERIAL_NUM              = 0xBFFF01A0;
		const long VI_ATTR_USB_INTFC_NUM               = 0x3FFF01A1;
		const long VI_ATTR_USB_PROTOCOL                = 0x3FFF01A7;
		const long VI_ATTR_USB_MAX_INTR_SIZE           = 0x3FFF01AF;
		const long VI_ATTR_OPER_NAME                   = 0xBFFF4042;
		const long VI_ATTR_GPIB_RECV_CIC_STATE         = 0x3FFF4193;
		const long VI_ATTR_RECV_TCPIP_ADDR             = 0xBFFF4198;
		const long VI_ATTR_USB_RECV_INTR_SIZE          = 0x3FFF41B0;
		const long VI_ATTR_USB_RECV_INTR_DATA          = 0xBFFF41B1;
		const long VI_ATTR_RD_BUF_SIZE                 = 0x3FFF002B;
		const long VI_ATTR_WR_BUF_SIZE                 = 0x3FFF002E;
	};
	[dllname("TLPM.dll"), helpstring("VISA Event Types")]
	module VISAEventTypes {
		const long VI_EVENT_IO_COMPLETION              = 0x3FFF2009;
		const long VI_EVENT_TRIG                       = 0xBFFF200A;
		const long VI_EVENT_SERVICE_REQ                = 0x3FFF200B;
		const long VI_EVENT_VXI_SIGP                   = 0x3FFF2020;
		const long VI_EVENT_VXI_VME_INTR               = 0xBFFF2021;
		const long VI_ALL_ENABLED_EVENTS               = 0x3FFF7FFF;
		const long VI_EVENT_CLEAR                      = 0x3FFF200D;
		const long VI_EVENT_EXCEPTION                  = 0xBFFF200E;
		const long VI_EVENT_GPIB_CIC                   = 0x3FFF2012;
		const long VI_EVENT_GPIB_TALK                  = 0x3FFF2013;
		const long VI_EVENT_GPIB_LISTEN                = 0x3FFF2014;
		const long VI_EVENT_VXI_VME_SYSFAIL            = 0x3FFF201D;
		const long VI_EVENT_VXI_VME_SYSRESET           = 0x3FFF201E;
		const long VI_EVENT_TCPIP_CONNECT              = 0x3FFF2036;
		const long VI_EVENT_USB_INTR                   = 0x3FFF2037;
	};
	[dllname("TLPM.dll"), helpstring("VISA Completion and Error Codes")]
	module VISACompletionAndErrorCodes {
		const long VI_SUCCESS                    = 0x0;
		const long VI_SUCCESS_EVENT_EN           = 0x3FFF0002;
		const long VI_SUCCESS_EVENT_DIS          = 0x3FFF0003;
		const long VI_SUCCESS_QUEUE_EMPTY        = 0x3FFF0004;
		const long VI_SUCCESS_TERM_CHAR          = 0x3FFF0005;
		const long VI_SUCCESS_MAX_CNT            = 0x3FFF0006;
		const long VI_SUCCESS_DEV_NPRESENT       = 0x3FFF007D;
		const long VI_SUCCESS_QUEUE_NEMPTY       = 0x3FFF0080;
		const long VI_SUCCESS_NESTED_SHARED      = 0x3FFF0099;
		const long VI_SUCCESS_NESTED_EXCLUSIVE   = 0x3FFF009A;
		const long VI_SUCCESS_SYNC               = 0x3FFF009B;
		const long VI_WARN_CONFIG_NLOADED        = 0x3FFF0077;
		const long VI_WARN_NULL_OBJECT           = 0x3FFF0082;
		const long VI_WARN_NSUP_ATTR_STATE       = 0x3FFF0084;
		const long VI_WARN_UNKNOWN_STATUS        = 0x3FFF0085;
		const long VI_WARN_NSUP_BUF              = 0x3FFF0088;
		const long VI_WARN_NSUP_ID_QUERY         = 0x3FFC0101;
		const long VI_WARN_NSUP_RESET            = 0x3FFC0102;
		const long VI_WARN_NSUP_SELF_TEST        = 0x3FFC0103;
		const long VI_WARN_NSUP_ERROR_QUERY      = 0x3FFC0104;
		const long VI_WARN_NSUP_REV_QUERY        = 0x3FFC0105;
		const long VI_ERROR                      = 0x80000000;
		const long VI_ERROR_SYSTEM_ERROR         = 0xBFFF0000;
		const long VI_ERROR_INV_OBJECT           = 0xBFFF000E;
		const long VI_ERROR_RSRC_LOCKED          = 0xBFFF000F;
		const long VI_ERROR_INV_EXPR             = 0xBFFF0010;
		const long VI_ERROR_RSRC_NFOUND          = 0xBFFF0011;
		const long VI_ERROR_INV_RSRC_NAME        = 0xBFFF0012;
		const long VI_ERROR_INV_ACC_MODE         = 0xBFFF0013;
		const long VI_ERROR_TMO                  = 0xBFFF0015;
		const long VI_ERROR_CLOSING_FAILED       = 0xBFFF0016;
		const long VI_ERROR_INV_DEGREE           = 0xBFFF001B;
		const long VI_ERROR_INV_JOB_ID           = 0xBFFF001C;
		const long VI_ERROR_NSUP_ATTR            = 0xBFFF001D;
		const long VI_ERROR_NSUP_ATTR_STATE      = 0xBFFF001E;
		const long VI_ERROR_ATTR_READONLY        = 0xBFFF001F;
		const long VI_ERROR_INV_LOCK_TYPE        = 0xBFFF0020;
		const long VI_ERROR_INV_ACCESS_KEY       = 0xBFFF0021;
		const long VI_ERROR_INV_EVENT            = 0xBFFF0026;
		const long VI_ERROR_INV_MECH             = 0xBFFF0027;
		const long VI_ERROR_HNDLR_NINSTALLED     = 0xBFFF0028;
		const long VI_ERROR_INV_HNDLR_REF        = 0xBFFF0029;
		const long VI_ERROR_INV_CONTEXT          = 0xBFFF002A;
		const long VI_ERROR_ABORT                = 0xBFFF0030;
		const long VI_ERROR_RAW_WR_PROT_VIOL     = 0xBFFF0034;
		const long VI_ERROR_RAW_RD_PROT_VIOL     = 0xBFFF0035;
		const long VI_ERROR_OUTP_PROT_VIOL       = 0xBFFF0036;
		const long VI_ERROR_INP_PROT_VIOL        = 0xBFFF0037;
		const long VI_ERROR_BERR                 = 0xBFFF0038;
		const long VI_ERROR_INV_SETUP            = 0xBFFF003A;
		const long VI_ERROR_QUEUE_ERROR          = 0xBFFF003B;
		const long VI_ERROR_ALLOC                = 0xBFFF003C;
		const long VI_ERROR_INV_MASK             = 0xBFFF003D;
		const long VI_ERROR_IO                   = 0xBFFF003E;
		const long VI_ERROR_INV_FMT              = 0xBFFF003F;
		const long VI_ERROR_NSUP_FMT             = 0xBFFF0041;
		const long VI_ERROR_LINE_IN_USE          = 0xBFFF0042;
		const long VI_ERROR_SRQ_NOCCURRED        = 0xBFFF004A;
		const long VI_ERROR_INV_SPACE            = 0xBFFF004E;
		const long VI_ERROR_INV_OFFSET           = 0xBFFF0051;
		const long VI_ERROR_INV_WIDTH            = 0xBFFF0052;
		const long VI_ERROR_NSUP_OFFSET          = 0xBFFF0054;
		const long VI_ERROR_NSUP_VAR_WIDTH       = 0xBFFF0055;
		const long VI_ERROR_WINDOW_NMAPPED       = 0xBFFF0057;
		const long VI_ERROR_NLISTENERS           = 0xBFFF005F;
		const long VI_ERROR_NCIC                 = 0xBFFF0060;
		const long VI_ERROR_NSUP_OPER            = 0xBFFF0067;
		const long VI_ERROR_ASRL_PARITY          = 0xBFFF006A;
		const long VI_ERROR_ASRL_FRAMING         = 0xBFFF006B;
		const long VI_ERROR_ASRL_OVERRUN         = 0xBFFF006C;
		const long VI_ERROR_NSUP_ALIGN_OFFSET    = 0xBFFF0070;
		const long VI_ERROR_USER_BUF             = 0xBFFF0071;
		const long VI_ERROR_RSRC_BUSY            = 0xBFFF0072;
		const long VI_ERROR_NSUP_WIDTH           = 0xBFFF0076;
		const long VI_ERROR_INV_PARAMETER        = 0xBFFF0078;
		const long VI_ERROR_INV_PROT             = 0xBFFF0079;
		const long VI_ERROR_INV_SIZE             = 0xBFFF007B;
		const long VI_ERROR_WINDOW_MAPPED        = 0xBFFF0080;
		const long VI_ERROR_NIMPL_OPER           = 0xBFFF0081;
		const long VI_ERROR_INV_LENGTH           = 0xBFFF0083;
		const long VI_ERROR_SESN_NLOCKED         = 0xBFFF009C;
		const long VI_ERROR_MEM_NSHARED          = 0xBFFF009D;
		const long VI_ERROR_INV_SESSION          = 0xBFFF000E;
		const long VI_ERROR_PARAMETER1           = 0xBFFC0001;
		const long VI_ERROR_PARAMETER2           = 0xBFFC0002;
		const long VI_ERROR_PARAMETER3           = 0xBFFC0003;
		const long VI_ERROR_PARAMETER4           = 0xBFFC0004;
		const long VI_ERROR_PARAMETER5           = 0xBFFC0005;
		const long VI_ERROR_PARAMETER6           = 0xBFFC0006;
		const long VI_ERROR_PARAMETER7           = 0xBFFC0007;
		const long VI_ERROR_PARAMETER8           = 0xBFFC0008;
		const long VI_ERROR_FAIL_ID_QUERY        = 0xBFFC0011;
		const long VI_ERROR_INV_RESPONSE         = 0xBFFC0012;
		const long VI_SUCCESS_TRIG_MAPPED        = 0x3FFF007E;
		const long VI_SUCCESS_NCHAIN             = 0x3FFF0098;
		const long VI_ERROR_IN_PROGRESS          = 0xBFFF0039;
		const long VI_ERROR_NSUP_MODE            = 0xBFFF0046;
		const long VI_ERROR_RESP_PENDING         = 0xBFFF0059;
		const long VI_ERROR_NSYS_CNTLR           = 0xBFFF0061;
		const long VI_ERROR_INTR_PENDING         = 0xBFFF0068;
		const long VI_ERROR_TRIG_NMAPPED         = 0xBFFF006E;
		const long VI_ERROR_INV_MODE             = 0xBFFF0091;
		const long VI_ERROR_LIBRARY_NFOUND       = 0xBFFF009E;
		const long VI_ERROR_NSUP_INTR            = 0xBFFF009F;
		const long VI_ERROR_INV_LINE             = 0xBFFF00A0;
		const long VI_ERROR_FILE_ACCESS          = 0xBFFF00A1;
		const long VI_ERROR_FILE_IO              = 0xBFFF00A2;
		const long VI_ERROR_NSUP_LINE            = 0xBFFF00A3;
		const long VI_ERROR_NSUP_MECH            = 0xBFFF00A4;
		const long VI_ERROR_INTF_NUM_NCONFIG     = 0xBFFF00A5;
		const long VI_ERROR_CONN_LOST            = 0xBFFF00A6;
		const long VI_ERROR_MACHINE_NAVAIL       = 0xBFFF00A7;
		const long VI_ERROR_NPERMISSION          = 0xBFFF00A8;
		const long VI_WARN_QUEUE_OVERFLOW        = 0x3FFF000C;
		const long VI_WARN_EXT_FUNC_NIMPL        = 0x3FFF00A9;
	};
	[dllname("TLPM.dll"), helpstring("VISA National Instruments Extensions")]
	module VISANIExtensions {
		const long VI_ATTR_UNSAFE_CALLBACK_EN          = 0x3FFF0008;
		const long VI_ATTR_KRNL_VIIN8_ADDR             = 0x3FFF810A;
		const long VI_ATTR_KRNL_VIIN16_ADDR            = 0x3FFF810B;
		const long VI_ATTR_KRNL_VIIN32_ADDR            = 0x3FFF810C;
		const long VI_ATTR_KRNL_VIOUT8_ADDR            = 0x3FFF810D;
		const long VI_ATTR_KRNL_VIOUT16_ADDR           = 0x3FFF810E;
		const long VI_ATTR_KRNL_VIOUT32_ADDR           = 0x3FFF810F;
		const long VI_ATTR_RESMAN_STATUS               = 0x3FFF8110;
		const long VI_ATTR_FIND_NEEDS_REFRESH          = 0x3FFF018F;
		const long VI_ATTR_FIND_RSRC_MODE              = 0x3FFF0190;
		const int  VI_FIND_SEARCH_NOALIAS              = 0;
		const int  VI_FIND_SEARCH_MATCHALIASES         = 2;
		const int  VI_FIND_NOSEARCH_ALLALIASES         = 3;
		const int  VI_FIND_SEARCH_ALLALIASES           = 4;
		const long VI_EVENT_VXI_DEV_CMD                = 0xBFFF200F;
		const long VI_ATTR_VXI_DEV_CMD_TYPE            = 0x3FFF4037;
		const long VI_ATTR_VXI_DEV_CMD_VALUE           = 0x3FFF4038;
		const int  VI_VXI_DEV_CMD_TYPE_16              = 16;
		const int  VI_VXI_DEV_CMD_TYPE_32              = 32;
		const int  VI_VXI_RESP_NONE                    = 0;
		const int  VI_VXI_RESP_PROT_ERROR              = -1;
		const long VI_ATTR_ASRL_DISCARD_NULL           = 0x3FFF00B0;
		const long VI_ATTR_ASRL_BREAK_STATE            = 0x3FFF01BC;
		const long VI_ATTR_ASRL_BREAK_LEN              = 0x3FFF01BD;
		const long VI_ATTR_ASRL_ALLOW_TRANSMIT         = 0x3FFF01BE;
		const long VI_ATTR_ASRL_WIRE_MODE              = 0x3FFF01BF;
		const int  VI_ASRL_WIRE_485_4                  = 0;
		const int  VI_ASRL_WIRE_485_2_DTR_ECHO         = 1;
		const int  VI_ASRL_WIRE_485_2_DTR_CTRL         = 2;
		const int  VI_ASRL_WIRE_485_2_AUTO             = 3;
		const long VI_EVENT_ASRL_BREAK                 = 0x3FFF2023;
		const long VI_EVENT_ASRL_CTS                   = 0x3FFF2029;
		const long VI_EVENT_ASRL_DSR                   = 0x3FFF202A;
		const long VI_EVENT_ASRL_DCD                   = 0x3FFF202C;
		const long VI_EVENT_ASRL_RI                    = 0x3FFF202E;
		const long VI_EVENT_ASRL_CHAR                  = 0x3FFF2035;
		const long VI_EVENT_ASRL_TERMCHAR              = 0x3FFF2024;
		const long VI_ATTR_PXI_DEV_NUM                 = 0x3FFF0201;
		const long VI_ATTR_PXI_FUNC_NUM                = 0x3FFF0202;
		const long VI_ATTR_PXI_BUS_NUM                 = 0x3FFF0205;
		const long VI_ATTR_PXI_CHASSIS                 = 0x3FFF0206;
		const long VI_ATTR_PXI_SLOTPATH                = 0xBFFF0207;
		const long VI_ATTR_PXI_SLOT_LBUS_LEFT          = 0x3FFF0208;
		const long VI_ATTR_PXI_SLOT_LBUS_RIGHT         = 0x3FFF0209;
		const long VI_ATTR_PXI_TRIG_BUS                = 0x3FFF020A;
		const long VI_ATTR_PXI_STAR_TRIG_BUS           = 0x3FFF020B;
		const long VI_ATTR_PXI_STAR_TRIG_LINE          = 0x3FFF020C;
		const long VI_ATTR_PXI_MEM_TYPE_BAR0           = 0x3FFF0211;
		const long VI_ATTR_PXI_MEM_TYPE_BAR1           = 0x3FFF0212;
		const long VI_ATTR_PXI_MEM_TYPE_BAR2           = 0x3FFF0213;
		const long VI_ATTR_PXI_MEM_TYPE_BAR3           = 0x3FFF0214;
		const long VI_ATTR_PXI_MEM_TYPE_BAR4           = 0x3FFF0215;
		const long VI_ATTR_PXI_MEM_TYPE_BAR5           = 0x3FFF0216;
		const long VI_ATTR_PXI_MEM_BASE_BAR0           = 0x3FFF0221;
		const long VI_ATTR_PXI_MEM_BASE_BAR1           = 0x3FFF0222;
		const long VI_ATTR_PXI_MEM_BASE_BAR2           = 0x3FFF0223;
		const long VI_ATTR_PXI_MEM_BASE_BAR3           = 0x3FFF0224;
		const long VI_ATTR_PXI_MEM_BASE_BAR4           = 0x3FFF0225;
		const long VI_ATTR_PXI_MEM_BASE_BAR5           = 0x3FFF0226;
		const long VI_ATTR_PXI_MEM_SIZE_BAR0           = 0x3FFF0231;
		const long VI_ATTR_PXI_MEM_SIZE_BAR1           = 0x3FFF0232;
		const long VI_ATTR_PXI_MEM_SIZE_BAR2           = 0x3FFF0233;
		const long VI_ATTR_PXI_MEM_SIZE_BAR3           = 0x3FFF0234;
		const long VI_ATTR_PXI_MEM_SIZE_BAR4           = 0x3FFF0235;
		const long VI_ATTR_PXI_MEM_SIZE_BAR5           = 0x3FFF0236;
		const long VI_EVENT_PXI_INTR                   = 0x3FFF2022;
		const int  VI_INTF_PXI                         = 5;
		const int  VI_PXI_ALLOC_SPACE                  = 9;
		const int  VI_PXI_CFG_SPACE                    = 10;
		const int  VI_PXI_BAR0_SPACE                   = 11;
		const int  VI_PXI_BAR1_SPACE                   = 12;
		const int  VI_PXI_BAR2_SPACE                   = 13;
		const int  VI_PXI_BAR3_SPACE                   = 14;
		const int  VI_PXI_BAR4_SPACE                   = 15;
		const int  VI_PXI_BAR5_SPACE                   = 16;
		const int  VI_PXI_ADDR_NONE                    = 0;
		const int  VI_PXI_ADDR_MEM                     = 1;
		const int  VI_PXI_ADDR_IO                      = 2;
		const int  VI_PXI_ADDR_CFG                     = 3;
		const long VI_ATTR_USB_BULK_OUT_PIPE           = 0x3FFF01A2;
		const long VI_ATTR_USB_BULK_IN_PIPE            = 0x3FFF01A3;
		const long VI_ATTR_USB_INTR_IN_PIPE            = 0x3FFF01A4;
		const long VI_ATTR_USB_CLASS                   = 0x3FFF01A5;
		const long VI_ATTR_USB_SUBCLASS                = 0x3FFF01A6;
		const long VI_ATTR_USB_ALT_SETTING             = 0x3FFF01A8;
		const long VI_ATTR_USB_NUM_INTFCS              = 0x3FFF01AA;
		const long VI_ATTR_USB_NUM_PIPES               = 0x3FFF01AB;
		const long VI_ATTR_USB_BULK_OUT_STATUS         = 0x3FFF01AC;
		const long VI_ATTR_USB_BULK_IN_STATUS          = 0x3FFF01AD;
		const long VI_ATTR_USB_INTR_IN_STATUS          = 0x3FFF01AE;
		const int  VI_USB_PIPE_STATE_UNKNOWN           = -1;
		const int  VI_USB_PIPE_READY                   = 0;
		const int  VI_USB_PIPE_STALLED                 = 1;
		const long VI_ATTR_PXI_RECV_INTR_SEQ           = 0x3FFF4240;
		const long VI_ATTR_PXI_RECV_INTR_DATA          = 0x3FFF4241;
		const long VI_ATTR_ASRL_CONNECTED              = 0x3FFF01BB;
		const long VI_ATTR_PXI_SRC_TRIG_BUS            = 0x3FFF020D;
		const long VI_ATTR_PXI_DEST_TRIG_BUS           = 0x3FFF020E;
		const int  VI_ASRL_WIRE_232_DTE                = 128;
		const int  VI_ASRL_WIRE_232_DCE                = 129;
		const int  VI_ASRL_WIRE_232_AUTO               = 130;
		const int  VI_TRIG_PROT_RESERVE                = 6;
		const int  VI_TRIG_PROT_UNRESERVE              = 7;
		const int  VI_INTF_RIO                         = 8;
		const long VI_ATTR_USB_END_IN                  = 0x3FFF01A9;
		const int  VI_USB_END_NONE                     = 0;
		const int  VI_USB_END_SHORT                    = 4;
		const int  VI_USB_END_SHORT_OR_COUNT           = 5;
		const int  VI_INTF_FIREWIRE                    = 9;
		const long VI_ATTR_FIREWIRE_DEST_UPPER_OFFSET  = 0x3FFF01F0;
		const long VI_ATTR_FIREWIRE_SRC_UPPER_OFFSET   = 0x3FFF01F1;
		const long VI_ATTR_FIREWIRE_WIN_UPPER_OFFSET   = 0x3FFF01F2;
		const long VI_ATTR_FIREWIRE_VENDOR_ID          = 0x3FFF01F3;
		const long VI_ATTR_FIREWIRE_LOWER_CHIP_ID      = 0x3FFF01F4;
		const long VI_ATTR_FIREWIRE_UPPER_CHIP_ID      = 0x3FFF01F5;
		const long VI_ATTR_USB_CTRL_PIPE               = 0x3FFF01B0;
		const long VI_ATTR_PXI_IS_EXPRESS              = 0x3FFF0240;
		const long VI_ATTR_PXI_SLOT_LWIDTH             = 0x3FFF0241;
		const long VI_ATTR_PXI_MAX_LWIDTH              = 0x3FFF0242;
		const long VI_ATTR_PXI_ACTUAL_LWIDTH           = 0x3FFF0243;
		const long VI_ATTR_PXI_DSTAR_BUS               = 0x3FFF0244;
		const long VI_ATTR_PXI_DSTAR_SET               = 0x3FFF0245;
		const long VI_ATTR_VXI_TRIG_LINES_EN           = 0x3FFF4043;
		const long VI_ATTR_VXI_TRIG_DIR                = 0x3FFF4044;
	};
	[dllname("TLPM.dll"), helpstring("Other VISA Definitions")]
	module VISAOtherDefinitions {
		const int VI_FIND_BUFLEN                      = 256;
		const int VI_NULL                             = 0;
		const int VI_TRUE                             = 1;
		const int VI_FALSE                            = 0;
		const int VI_INTF_GPIB                        = 1;
		const int VI_INTF_VXI                         = 2;
		const int VI_INTF_GPIB_VXI                    = 3;
		const int VI_INTF_ASRL                        = 4;
		const int VI_NORMAL                           = 1;
		const int VI_FDC                              = 2;
		const int VI_HS488                            = 3;
		const int VI_ASRL488                          = 4;
		const int VI_FDC_NORMAL                       = 1;
		const int VI_FDC_STREAM                       = 2;
		const int VI_LOCAL_SPACE                      = 0;
		const int VI_A16_SPACE                        = 1;
		const int VI_A24_SPACE                        = 2;
		const int VI_A32_SPACE                        = 3;
		const int VI_UNKNOWN_LA                       = -1;
		const int VI_UNKNOWN_SLOT                     = -1;
		const int VI_UNKNOWN_LEVEL                    = -1;
		const int VI_QUEUE                            = 1;
		const int VI_HNDLR                            = 2;
		const int VI_SUSPEND_HNDLR                    = 4;
		const int VI_ALL_MECH                         = 0xFFFF;
		const int VI_ANY_HNDLR                        = 0;
		const int VI_TRIG_SW                          = -1;
		const int VI_TRIG_TTL0                        = 0;
		const int VI_TRIG_TTL1                        = 1;
		const int VI_TRIG_TTL2                        = 2;
		const int VI_TRIG_TTL3                        = 3;
		const int VI_TRIG_TTL4                        = 4;
		const int VI_TRIG_TTL5                        = 5;
		const int VI_TRIG_TTL6                        = 6;
		const int VI_TRIG_TTL7                        = 7;
		const int VI_TRIG_ECL0                        = 8;
		const int VI_TRIG_ECL1                        = 9;
		const int VI_TRIG_PROT_DEFAULT                = 0;
		const int VI_TRIG_PROT_ON                     = 1;
		const int VI_TRIG_PROT_OFF                    = 2;
		const int VI_TRIG_PROT_SYNC                   = 5;
		const int VI_READ_BUF                         = 1;
		const int VI_WRITE_BUF                        = 2;
		const int VI_READ_BUF_DISCARD                 = 4;
		const int VI_WRITE_BUF_DISCARD                = 8;
		const int VI_ASRL_IN_BUF                      = 16;
		const int VI_ASRL_OUT_BUF                     = 32;
		const int VI_ASRL_IN_BUF_DISCARD              = 64;
		const int VI_ASRL_OUT_BUF_DISCARD             = 128;
		const int VI_FLUSH_ON_ACCESS                  = 1;
		const int VI_FLUSH_WHEN_FULL                  = 2;
		const int VI_FLUSH_DISABLE                    = 3;
		const int VI_NMAPPED                          = 1;
		const int VI_USE_OPERS                        = 2;
		const int VI_DEREF_ADDR                       = 3;
		const int VI_TMO_IMMEDIATE                    = 0x0;
		const int VI_TMO_INFINITE                     = 0xFFFFFFFF;
		const int VI_INFINITE                         = 0xFFFFFFFF;
		const int VI_NO_LOCK                          = 0;
		const int VI_EXCLUSIVE_LOCK                   = 1;
		const int VI_SHARED_LOCK                      = 2;
		const int VI_LOAD_CONFIG                      = 4;
		const int VI_NO_SEC_ADDR                      = 0xFFFF;
		const int VI_ASRL_PAR_NONE                    = 0;
		const int VI_ASRL_PAR_ODD                     = 1;
		const int VI_ASRL_PAR_EVEN                    = 2;
		const int VI_ASRL_PAR_MARK                    = 3;
		const int VI_ASRL_PAR_SPACE                   = 4;
		const int VI_ASRL_STOP_ONE                    = 10;
		const int VI_ASRL_STOP_TWO                    = 20;
		const int VI_ASRL_FLOW_NONE                   = 0;
		const int VI_ASRL_FLOW_XON_XOFF               = 1;
		const int VI_ASRL_FLOW_RTS_CTS                = 2;
		const int VI_ASRL_END_NONE                    = 0;
		const int VI_ASRL_END_LAST_BIT                = 1;
		const int VI_ASRL_END_TERMCHAR                = 2;
		const int VI_ASRL_END_BREAK                   = 3;
		const int VI_STATE_ASSERTED                   = 1;
		const int VI_STATE_UNASSERTED                 = 0;
		const int VI_STATE_UNKNOWN                    = -1;
		const int VI_BIG_ENDIAN                       = 0;
		const int VI_LITTLE_ENDIAN                    = 1;
		const int VI_DATA_PRIV                        = 0;
		const int VI_DATA_NPRIV                       = 1;
		const int VI_PROG_PRIV                        = 2;
		const int VI_PROG_NPRIV                       = 3;
		const int VI_BLCK_PRIV                        = 4;
		const int VI_BLCK_NPRIV                       = 5;
		const int VI_D64_PRIV                         = 6;
		const int VI_D64_NPRIV                        = 7;
		const int VI_WIDTH_8                          = 1;
		const int VI_WIDTH_16                         = 2;
		const int VI_WIDTH_32                         = 4;
		const int VI_ON                              =  1;
		const int VI_OFF                             =  0;
		const int VI_INTF_TCPIP                       = 6;
		const int VI_INTF_USB                         = 7;
		const int VI_PROT_4882_STRS                   = 4;
		const int VI_TRIG_ALL                         = -2;
		const int VI_TRIG_PANEL_IN                    = 27;
		const int VI_TRIG_PANEL_OUT                   = 28;
		const int VI_IO_IN_BUF                        = 16;
		const int VI_IO_OUT_BUF                       = 32;
		const int VI_IO_IN_BUF_DISCARD                = 64;
		const int VI_IO_OUT_BUF_DISCARD               = 128;
		const int VI_ASRL_STOP_ONE5                   = 15;
		const int VI_ASRL_FLOW_DTR_DSR                = 4;
		const int VI_GPIB_REN_DEASSERT                = 0;
		const int VI_GPIB_REN_ASSERT                  = 1;
		const int VI_GPIB_REN_DEASSERT_GTL            = 2;
		const int VI_GPIB_REN_ASSERT_ADDRESS          = 3;
		const int VI_GPIB_REN_ASSERT_LLO              = 4;
		const int VI_GPIB_REN_ASSERT_ADDRESS_LLO      = 5;
		const int VI_GPIB_REN_ADDRESS_GTL             = 6;
		const int VI_GPIB_ATN_DEASSERT                = 0;
		const int VI_GPIB_ATN_ASSERT                  = 1;
		const int VI_GPIB_ATN_DEASSERT_HANDSHAKE      = 2;
		const int VI_GPIB_ATN_ASSERT_IMMEDIATE        = 3;
		const int VI_GPIB_HS488_DISABLED              = 0;
		const int VI_GPIB_HS488_NIMPL                 = -1;
		const int VI_GPIB_UNADDRESSED                 = 0;
		const int VI_GPIB_TALKER                      = 1;
		const int VI_GPIB_LISTENER                    = 2;
		const int VI_VXI_CMD16                        = 0x0200;
		const int VI_VXI_CMD16_RESP16                 = 0x0202;
		const int VI_VXI_RESP16                       = 0x0002;
		const int VI_VXI_CMD32                        = 0x0400;
		const int VI_VXI_CMD32_RESP16                 = 0x0402;
		const int VI_VXI_CMD32_RESP32                 = 0x0404;
		const int VI_VXI_RESP32                       = 0x0004;
		const int VI_ASSERT_SIGNAL                    = -1;
		const int VI_ASSERT_USE_ASSIGNED              = 0;
		const int VI_ASSERT_IRQ1                      = 1;
		const int VI_ASSERT_IRQ2                      = 2;
		const int VI_ASSERT_IRQ3                      = 3;
		const int VI_ASSERT_IRQ4                      = 4;
		const int VI_ASSERT_IRQ5                      = 5;
		const int VI_ASSERT_IRQ6                      = 6;
		const int VI_ASSERT_IRQ7                      = 7;
		const int VI_UTIL_ASSERT_SYSRESET             = 1;
		const int VI_UTIL_ASSERT_SYSFAIL              = 2;
		const int VI_UTIL_DEASSERT_SYSFAIL            = 3;
		const int VI_VXI_CLASS_MEMORY                 = 0;
		const int VI_VXI_CLASS_EXTENDED               = 1;
		const int VI_VXI_CLASS_MESSAGE                = 2;
		const int VI_VXI_CLASS_REGISTER               = 3;
		const int VI_VXI_CLASS_OTHER                  = 4;
		const int VI_PROT_NORMAL                      = 1;
		const int VI_PROT_FDC                         = 2;
		const int VI_PROT_HS488                       = 3;
		const int VI_PROT_USBTMC_VENDOR               = 5;
		const int VI_DEREF_ADDR_BYTE_SWAP             = 4;
	};
	[dllname("TLPM.dll"), helpcontext(215), helpstring("Initialize")]
	module Initialize{
		[helpcontext(1), entry("TLPM_init"), helpstring("Initialize")]
		ViStatus __stdcall TLPM_init(
			[in] ViRsrc resourceName, 
			[in] ViBoolean IDQuery, 
			[in] ViBoolean resetDevice, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViSession *instrumentHandle);
	};
	[dllname("TLPM.dll"), helpcontext(215), helpstring("Resource Functions")]
	module ResourceFunctions{
		[helpcontext(2), entry("TLPM_findRsrc"), helpstring("Find Resources")]
		ViStatus __stdcall TLPM_findRsrc(
			[in] ViSession instrumentHandle, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViUInt32 *resourceCount);
		[helpcontext(3), entry("TLPM_getRsrcName"), helpstring("Get Resource Name")]
		ViStatus __stdcall TLPM_getRsrcName(
			[in] ViSession instrumentHandle, 
			[in] ViUInt32 index, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 1), custom(746B27E2-FBD7-11d1-B311-0060970535CB, "[]")] LPSTR resourceName);
		[helpcontext(4), entry("TLPM_getRsrcInfo"), helpstring("Get Resource Information")]
		ViStatus __stdcall TLPM_getRsrcInfo(
			[in] ViSession instrumentHandle, 
			[in] ViUInt32 index, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 1), custom(746B27E2-FBD7-11d1-B311-0060970535CB, "[]")] LPSTR modelName, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 1), custom(746B27E2-FBD7-11d1-B311-0060970535CB, "[]")] LPSTR serialNumber, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 1), custom(746B27E2-FBD7-11d1-B311-0060970535CB, "[]")] LPSTR manufacturer, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViBoolean *deviceAvailable);
	};
	[dllname("TLPM.dll"), helpcontext(215), helpstring("Status Register")]
	module StatusRegister{
		[helpcontext(5), entry("TLPM_writeRegister"), helpstring("Write Register")]
		ViStatus __stdcall TLPM_writeRegister(
			[in] ViSession instrumentHandle, 
			[in] ViInt16 registerID, 
			[in] ViInt16 value);
		[helpcontext(6), entry("TLPM_readRegister"), helpstring("Read Register")]
		ViStatus __stdcall TLPM_readRegister(
			[in] ViSession instrumentHandle, 
			[in] ViInt16 registerID, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViInt16 *value);
		[helpcontext(7), entry("TLPM_presetRegister"), helpstring("Preset Registers")]
		ViStatus __stdcall TLPM_presetRegister(
			[in] ViSession instrumentHandle);
	};
	[dllname("TLPM.dll"), helpcontext(215), helpstring("System")]
	module System{
		[helpcontext(8), entry("TLPM_setTime"), helpstring("Set Date and Time")]
		ViStatus __stdcall TLPM_setTime(
			[in] ViSession instrumentHandle, 
			[in] ViInt16 year, 
			[in] ViInt16 month, 
			[in] ViInt16 day, 
			[in] ViInt16 hour, 
			[in] ViInt16 minute, 
			[in] ViInt16 second);
		[helpcontext(9), entry("TLPM_getTime"), helpstring("Get Date and Time")]
		ViStatus __stdcall TLPM_getTime(
			[in] ViSession instrumentHandle, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViInt16 *year, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViInt16 *month, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViInt16 *day, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViInt16 *hour, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViInt16 *minute, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViInt16 *second);
		[helpcontext(10), entry("TLPM_setLineFrequency"), helpstring("Set Line Frequency")]
		ViStatus __stdcall TLPM_setLineFrequency(
			[in] ViSession instrumentHandle, 
			[in] ViInt16 lineFrequency);
		[helpcontext(11), entry("TLPM_getLineFrequency"), helpstring("Get Line Frequency")]
		ViStatus __stdcall TLPM_getLineFrequency(
			[in] ViSession instrumentHandle, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViInt16 *lineFrequency);
		[helpcontext(12), entry("TLPM_getBatteryVoltage"), helpstring("Get Battery Voltage")]
		ViStatus __stdcall TLPM_getBatteryVoltage(
			[in] ViSession instrumentHandle, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViReal64 *voltage);
		[helpcontext(13), entry("TLPM_setDispBrightness"), helpstring("Set Display Brightness")]
		ViStatus __stdcall TLPM_setDispBrightness(
			[in] ViSession instrumentHandle, 
			[in] ViReal64 brightness);
		[helpcontext(14), entry("TLPM_getDispBrightness"), helpstring("Get Display Brightness")]
		ViStatus __stdcall TLPM_getDispBrightness(
			[in] ViSession instrumentHandle, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViReal64 *brightness);
		[helpcontext(15), entry("TLPM_setDispContrast"), helpstring("Set Display Contrast")]
		ViStatus __stdcall TLPM_setDispContrast(
			[in] ViSession instrumentHandle, 
			[in] ViReal64 contrast);
		[helpcontext(16), entry("TLPM_getDispContrast"), helpstring("Get Display Contrast")]
		ViStatus __stdcall TLPM_getDispContrast(
			[in] ViSession instrumentHandle, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViReal64 *contrast);
	};
	[dllname("TLPM.dll"), helpcontext(215), helpstring("Ethernet Interface")]
	module EthernetInterface{
		[helpcontext(17), entry("TLPM_setIPAddress"), helpstring("Set IP Address")]
		ViStatus __stdcall TLPM_setIPAddress(
			[in] ViSession instrumentHandle, 
			[in, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 1), custom(746B27E2-FBD7-11d1-B311-0060970535CB, "[]")] LPSTR IPAddress);
		[helpcontext(18), entry("TLPM_getIPAddress"), helpstring("Get IP Address")]
		ViStatus __stdcall TLPM_getIPAddress(
			[in] ViSession instrumentHandle, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 1), custom(746B27E2-FBD7-11d1-B311-0060970535CB, "[]")] LPSTR IPAddress);
		[helpcontext(19), entry("TLPM_setIPMask"), helpstring("Set IP Mask")]
		ViStatus __stdcall TLPM_setIPMask(
			[in] ViSession instrumentHandle, 
			[in, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 1), custom(746B27E2-FBD7-11d1-B311-0060970535CB, "[]")] LPSTR IPMask);
		[helpcontext(20), entry("TLPM_getIPMask"), helpstring("Get IP Mask")]
		ViStatus __stdcall TLPM_getIPMask(
			[in] ViSession instrumentHandle, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 1), custom(746B27E2-FBD7-11d1-B311-0060970535CB, "[]")] LPSTR IPMask);
		[helpcontext(21), entry("TLPM_getMACAddress"), helpstring("Get MAC Address")]
		ViStatus __stdcall TLPM_getMACAddress(
			[in] ViSession instrumentHandle, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 1), custom(746B27E2-FBD7-11d1-B311-0060970535CB, "[]")] LPSTR MACAddress);
		[helpcontext(22), entry("TLPM_setDHCP"), helpstring("Set DHCP")]
		ViStatus __stdcall TLPM_setDHCP(
			[in] ViSession instrumentHandle, 
			[in, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 1), custom(746B27E2-FBD7-11d1-B311-0060970535CB, "[]")] LPSTR DHCP);
		[helpcontext(23), entry("TLPM_getDHCP"), helpstring("Get DHCP")]
		ViStatus __stdcall TLPM_getDHCP(
			[in] ViSession instrumentHandle, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 1), custom(746B27E2-FBD7-11d1-B311-0060970535CB, "[]")] LPSTR DHCP);
		[helpcontext(24), entry("TLPM_setHostname"), helpstring("Set Hostname")]
		ViStatus __stdcall TLPM_setHostname(
			[in] ViSession instrumentHandle, 
			[in, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 1), custom(746B27E2-FBD7-11d1-B311-0060970535CB, "[]")] LPSTR hostname);
		[helpcontext(25), entry("TLPM_getHostname"), helpstring("Get Hostname")]
		ViStatus __stdcall TLPM_getHostname(
			[in] ViSession instrumentHandle, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 1), custom(746B27E2-FBD7-11d1-B311-0060970535CB, "[]")] LPSTR hostname);
		[helpcontext(26), entry("TLPM_setWebPort"), helpstring("Set Web Port")]
		ViStatus __stdcall TLPM_setWebPort(
			[in] ViSession instrumentHandle, 
			[in] ViUInt32 port);
		[helpcontext(27), entry("TLPM_getWebPort"), helpstring("Get Web Port")]
		ViStatus __stdcall TLPM_getWebPort(
			[in] ViSession instrumentHandle, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViUInt32 *port);
		[helpcontext(28), entry("TLPM_setEncryption"), helpstring("Set Encryption")]
		ViStatus __stdcall TLPM_setEncryption(
			[in] ViSession instrumentHandle, 
			[in] ViString oldPassword, 
			[in] ViString newPassword, 
			[in] ViBoolean encryption);
		[helpcontext(29), entry("TLPM_getEncryption"), helpstring("Get Encryption")]
		ViStatus __stdcall TLPM_getEncryption(
			[in] ViSession instrumentHandle, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 1), custom(746B27E2-FBD7-11d1-B311-0060970535CB, "[]")] LPSTR password, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViBoolean *encryption);
		[helpcontext(30), entry("TLPM_setSCPIPort"), helpstring("Set SCPI Port")]
		ViStatus __stdcall TLPM_setSCPIPort(
			[in] ViSession instrumentHandle, 
			[in] ViUInt32 port);
		[helpcontext(31), entry("TLPM_getSCPIPort"), helpstring("Get SCPI Port")]
		ViStatus __stdcall TLPM_getSCPIPort(
			[in] ViSession instrumentHandle, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViUInt32 *port);
		[helpcontext(32), entry("TLPM_setDFUPort"), helpstring("Set DFU Port")]
		ViStatus __stdcall TLPM_setDFUPort(
			[in] ViSession instrumentHandle, 
			[in] ViUInt32 port);
		[helpcontext(33), entry("TLPM_getDFUPort"), helpstring("Get DFU Port")]
		ViStatus __stdcall TLPM_getDFUPort(
			[in] ViSession instrumentHandle, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViUInt32 *port);
		[helpcontext(34), entry("TLPM_setLANPropagation"), helpstring("Set LAN Propagation")]
		ViStatus __stdcall TLPM_setLANPropagation(
			[in] ViSession instrumentHandle, 
			[in] ViBoolean enable);
		[helpcontext(35), entry("TLPM_getLANPropagation"), helpstring("Get LAN Propagation")]
		ViStatus __stdcall TLPM_getLANPropagation(
			[in] ViSession instrumentHandle, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViBoolean *enable);
		[helpcontext(36), entry("TLPM_setEnableNetSearch"), helpstring("Set Enable Net Search")]
		ViStatus __stdcall TLPM_setEnableNetSearch(
			[in] ViSession instrumentHandle, 
			[in] ViBoolean enable);
		[helpcontext(37), entry("TLPM_getEnableNetSearch"), helpstring("Get Enable Net Search")]
		ViStatus __stdcall TLPM_getEnableNetSearch(
			[in] ViSession instrumentHandle, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViBoolean *enable);
		[helpcontext(38), entry("TLPM_setNetSearchMask"), helpstring("Set Net Search Mask")]
		ViStatus __stdcall TLPM_setNetSearchMask(
			[in] ViSession instrumentHandle, 
			[in, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 1), custom(746B27E2-FBD7-11d1-B311-0060970535CB, "[]")] LPSTR netMask);
	};
	[dllname("TLPM.dll"), helpcontext(215), helpstring("Measure")]
	module Measure{
		[helpcontext(39), entry("TLPM_setInputFilterState"), helpstring("Set Photodiode Input Filter State")]
		ViStatus __stdcall TLPM_setInputFilterState(
			[in] ViSession instrumentHandle, 
			[in] ViBoolean photodiodeInputFilterState);
		[helpcontext(40), entry("TLPM_getInputFilterState"), helpstring("Get Photodiode Input Filter State")]
		ViStatus __stdcall TLPM_getInputFilterState(
			[in] ViSession instrumentHandle, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViBoolean *photodiodeInputFilterState);
		[helpcontext(41), entry("TLPM_setAccelState"), helpstring("Set Thermopile Accelerator State")]
		ViStatus __stdcall TLPM_setAccelState(
			[in] ViSession instrumentHandle, 
			[in] ViBoolean thermopileAcceleratorState);
		[helpcontext(42), entry("TLPM_getAccelState"), helpstring("Get Thermopile Accelerator State")]
		ViStatus __stdcall TLPM_getAccelState(
			[in] ViSession instrumentHandle, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViBoolean *thermopileAcceleratorState);
		[helpcontext(43), entry("TLPM_setAccelMode"), helpstring("Set Thermopile Accelerator Mode")]
		ViStatus __stdcall TLPM_setAccelMode(
			[in] ViSession instrumentHandle, 
			[in] ViBoolean thermopileAcceleratorMode);
		[helpcontext(44), entry("TLPM_getAccelMode"), helpstring("Get Thermopile Accelerator Mode")]
		ViStatus __stdcall TLPM_getAccelMode(
			[in] ViSession instrumentHandle, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViBoolean *thermopileAcceleratorMode);
		[helpcontext(45), entry("TLPM_setAccelTau"), helpstring("Set Thermopile Accelerator Tau")]
		ViStatus __stdcall TLPM_setAccelTau(
			[in] ViSession instrumentHandle, 
			[in] ViReal64 thermopileAcceleratorTau);
		[helpcontext(46), entry("TLPM_getAccelTau"), helpstring("Get Thermopile Accelerator Tau")]
		ViStatus __stdcall TLPM_getAccelTau(
			[in] ViSession instrumentHandle, 
			[in] ViInt16 attribute, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViReal64 *thermopileAcceleratorTau);
		[helpcontext(47), entry("TLPM_setInputAdapterType"), helpstring("Set Custom Sensor Input Adapter Type")]
		ViStatus __stdcall TLPM_setInputAdapterType(
			[in] ViSession instrumentHandle, 
			[in] ViInt16 customSensorType);
		[helpcontext(48), entry("TLPM_getInputAdapterType"), helpstring("Get Custom Sensor Input Adapter Type")]
		ViStatus __stdcall TLPM_getInputAdapterType(
			[in] ViSession instrumentHandle, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViInt16 *customSensorType);
		[helpcontext(49), entry("TLPM_setAvgTime"), helpstring("Set Average Time")]
		ViStatus __stdcall TLPM_setAvgTime(
			[in] ViSession instrumentHandle, 
			[in] ViReal64 averageTime);
		[helpcontext(50), entry("TLPM_getAvgTime"), helpstring("Get Average Time")]
		ViStatus __stdcall TLPM_getAvgTime(
			[in] ViSession instrumentHandle, 
			[in] ViInt16 attribute, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViReal64 *averageTime);
		[helpcontext(51), entry("TLPM_setAvgCnt"), helpstring("Set Average Count")]
		ViStatus __stdcall TLPM_setAvgCnt(
			[in] ViSession instrumentHandle, 
			[in] ViInt16 averageCount);
		[helpcontext(52), entry("TLPM_getAvgCnt"), helpstring("Get Average Count")]
		ViStatus __stdcall TLPM_getAvgCnt(
			[in] ViSession instrumentHandle, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViInt16 *averageCount);
		[helpcontext(53), entry("TLPM_setAttenuation"), helpstring("Set Attenuation")]
		ViStatus __stdcall TLPM_setAttenuation(
			[in] ViSession instrumentHandle, 
			[in] ViReal64 attenuation);
		[helpcontext(54), entry("TLPM_getAttenuation"), helpstring("Get Attenuation")]
		ViStatus __stdcall TLPM_getAttenuation(
			[in] ViSession instrumentHandle, 
			[in] ViInt16 attribute, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViReal64 *attenuation);
		[helpcontext(55), entry("TLPM_startDarkAdjust"), helpstring("Start Dark Offset Adjustment")]
		ViStatus __stdcall TLPM_startDarkAdjust(
			[in] ViSession instrumentHandle);
		[helpcontext(56), entry("TLPM_cancelDarkAdjust"), helpstring("Cancel Dark Adjustment")]
		ViStatus __stdcall TLPM_cancelDarkAdjust(
			[in] ViSession instrumentHandle);
		[helpcontext(57), entry("TLPM_getDarkAdjustState"), helpstring("Get Dark Adjustment State")]
		ViStatus __stdcall TLPM_getDarkAdjustState(
			[in] ViSession instrumentHandle, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViInt16 *state);
		[helpcontext(58), entry("TLPM_getDarkOffset"), helpstring("Get Dark Offset")]
		ViStatus __stdcall TLPM_getDarkOffset(
			[in] ViSession instrumentHandle, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViReal64 *darkOffset);
		[helpcontext(59), entry("TLPM_startZeroPos"), helpstring("Start Zeroing Position")]
		ViStatus __stdcall TLPM_startZeroPos(
			[in] ViSession instrumentHandle, 
			[in] ViUInt16 channel);
		[helpcontext(60), entry("TLPM_cancelZeroPos"), helpstring("Cancel Zeroing Position")]
		ViStatus __stdcall TLPM_cancelZeroPos(
			[in] ViSession instrumentHandle, 
			[in] ViUInt16 channel);
		[helpcontext(61), entry("TLPM_setZeroPos"), helpstring("Set Zeroing Positions")]
		ViStatus __stdcall TLPM_setZeroPos(
			[in] ViSession instrumentHandle, 
			[in] ViReal64 zeroX, 
			[in] ViReal64 zeroY, 
			[in] ViUInt16 channel);
		[helpcontext(62), entry("TLPM_getZeroPos"), helpstring("Get Zeroing Positions")]
		ViStatus __stdcall TLPM_getZeroPos(
			[in] ViSession instrumentHandle, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViReal64 *zeroX, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViReal64 *zeroY, 
			[in] ViUInt16 channel);
		[helpcontext(63), entry("TLPM_setBeamDia"), helpstring("Set Beam Diameter")]
		ViStatus __stdcall TLPM_setBeamDia(
			[in] ViSession instrumentHandle, 
			[in] ViReal64 beamDiameter);
		[helpcontext(64), entry("TLPM_getBeamDia"), helpstring("Get Beam Diameter")]
		ViStatus __stdcall TLPM_getBeamDia(
			[in] ViSession instrumentHandle, 
			[in] ViInt16 attribute, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViReal64 *beamDiameter);
		[helpcontext(65), entry("TLPM_setWavelength"), helpstring("Set Wavelength")]
		ViStatus __stdcall TLPM_setWavelength(
			[in] ViSession instrumentHandle, 
			[in] ViReal64 wavelength);
		[helpcontext(66), entry("TLPM_getWavelength"), helpstring("Get Wavelength")]
		ViStatus __stdcall TLPM_getWavelength(
			[in] ViSession instrumentHandle, 
			[in] ViInt16 attribute, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViReal64 *wavelength);
		[helpcontext(67), entry("TLPM_setPhotodiodeResponsivity"), helpstring("Set Photodiode Responsivity")]
		ViStatus __stdcall TLPM_setPhotodiodeResponsivity(
			[in] ViSession instrumentHandle, 
			[in] ViReal64 response);
		[helpcontext(68), entry("TLPM_getPhotodiodeResponsivity"), helpstring("Get Photodiode Responsivity")]
		ViStatus __stdcall TLPM_getPhotodiodeResponsivity(
			[in] ViSession instrumentHandle, 
			[in] ViInt16 attribute, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViReal64 *responsivity);
		[helpcontext(69), entry("TLPM_setThermopileResponsivity"), helpstring("Set Thermopile Responsivity")]
		ViStatus __stdcall TLPM_setThermopileResponsivity(
			[in] ViSession instrumentHandle, 
			[in] ViReal64 response);
		[helpcontext(70), entry("TLPM_getThermopileResponsivity"), helpstring("Get Thermopile Responsivity")]
		ViStatus __stdcall TLPM_getThermopileResponsivity(
			[in] ViSession instrumentHandle, 
			[in] ViInt16 attribute, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViReal64 *responsivity);
		[helpcontext(71), entry("TLPM_setPyrosensorResponsivity"), helpstring("Set Pyrosensor Responsivity")]
		ViStatus __stdcall TLPM_setPyrosensorResponsivity(
			[in] ViSession instrumentHandle, 
			[in] ViReal64 response);
		[helpcontext(72), entry("TLPM_getPyrosensorResponsivity"), helpstring("Get Pyrosensor Responsivity")]
		ViStatus __stdcall TLPM_getPyrosensorResponsivity(
			[in] ViSession instrumentHandle, 
			[in] ViInt16 attribute, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViReal64 *responsivity);
		[helpcontext(73), entry("TLPM_setCurrentAutoRange"), helpstring("Set Current Autorange Mode")]
		ViStatus __stdcall TLPM_setCurrentAutoRange(
			[in] ViSession instrumentHandle, 
			[in] ViBoolean currentAutorangeMode);
		[helpcontext(74), entry("TLPM_getCurrentAutorange"), helpstring("Get Current Autorange Mode")]
		ViStatus __stdcall TLPM_getCurrentAutorange(
			[in] ViSession instrumentHandle, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViBoolean *currentAutorangeMode);
		[helpcontext(75), entry("TLPM_setCurrentRange"), helpstring("Set Current Range")]
		ViStatus __stdcall TLPM_setCurrentRange(
			[in] ViSession instrumentHandle, 
			[in] ViReal64 currentToMeasure);
		[helpcontext(76), entry("TLPM_getCurrentRange"), helpstring("Get Current Range")]
		ViStatus __stdcall TLPM_getCurrentRange(
			[in] ViSession instrumentHandle, 
			[in] ViInt16 attribute, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViReal64 *currentValue);
		[helpcontext(77), entry("TLPM_getCurrentRanges"), helpstring("Get Current Ranges")]
		ViStatus __stdcall TLPM_getCurrentRanges(
			[in] ViSession instrumentHandle, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 1), custom(746B27E2-FBD7-11d1-B311-0060970535CB, "[]")] ViReal64 *currentValues, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViUInt16 *rangeCount);
		[helpcontext(78), entry("TLPM_setCurrentRef"), helpstring("Set Current Reference")]
		ViStatus __stdcall TLPM_setCurrentRef(
			[in] ViSession instrumentHandle, 
			[in] ViReal64 currentReferenceValue);
		[helpcontext(79), entry("TLPM_getCurrentRef"), helpstring("Get Current Reference")]
		ViStatus __stdcall TLPM_getCurrentRef(
			[in] ViSession instrumentHandle, 
			[in] ViInt16 attribute, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViReal64 *currentReferenceValue);
		[helpcontext(80), entry("TLPM_setCurrentRefState"), helpstring("Set Current Reference State")]
		ViStatus __stdcall TLPM_setCurrentRefState(
			[in] ViSession instrumentHandle, 
			[in] ViBoolean currentReferenceState);
		[helpcontext(81), entry("TLPM_getCurrentRefState"), helpstring("Get Current Reference State")]
		ViStatus __stdcall TLPM_getCurrentRefState(
			[in] ViSession instrumentHandle, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViBoolean *currentReferenceState);
		[helpcontext(82), entry("TLPM_setEnergyRange"), helpstring("Set Energy Range")]
		ViStatus __stdcall TLPM_setEnergyRange(
			[in] ViSession instrumentHandle, 
			[in] ViReal64 energyToMeasure);
		[helpcontext(83), entry("TLPM_getEnergyRange"), helpstring("Get Energy Range")]
		ViStatus __stdcall TLPM_getEnergyRange(
			[in] ViSession instrumentHandle, 
			[in] ViInt16 attribute, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViReal64 *energyValue);
		[helpcontext(84), entry("TLPM_setEnergyRef"), helpstring("Set Energy Reference")]
		ViStatus __stdcall TLPM_setEnergyRef(
			[in] ViSession instrumentHandle, 
			[in] ViReal64 energyReferenceValue);
		[helpcontext(85), entry("TLPM_getEnergyRef"), helpstring("Get Energy Reference")]
		ViStatus __stdcall TLPM_getEnergyRef(
			[in] ViSession instrumentHandle, 
			[in] ViInt16 attribute, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViReal64 *energyReferenceValue);
		[helpcontext(86), entry("TLPM_setEnergyRefState"), helpstring("Set Energy Reference State")]
		ViStatus __stdcall TLPM_setEnergyRefState(
			[in] ViSession instrumentHandle, 
			[in] ViBoolean energyReferenceState);
		[helpcontext(87), entry("TLPM_getEnergyRefState"), helpstring("Get Energy Reference State")]
		ViStatus __stdcall TLPM_getEnergyRefState(
			[in] ViSession instrumentHandle, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViBoolean *energyReferenceState);
		[helpcontext(88), entry("TLPM_getFreqRange"), helpstring("Get Frequency Range")]
		ViStatus __stdcall TLPM_getFreqRange(
			[in] ViSession instrumentHandle, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViReal64 *lowerFrequency, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViReal64 *upperFrequency);
		[helpcontext(89), entry("TLPM_setFreqMode"), helpstring("Set Frequency Mode")]
		ViStatus __stdcall TLPM_setFreqMode(
			[in] ViSession instrumentHandle, 
			[in] ViUInt16 frequencyMode);
		[helpcontext(90), entry("TLPM_getFreqMode"), helpstring("Get Frequency Mode")]
		ViStatus __stdcall TLPM_getFreqMode(
			[in] ViSession instrumentHandle, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViUInt16 *frequencyMode);
		[helpcontext(91), entry("TLPM_setPowerAutoRange"), helpstring("Set Power Autorange Mode")]
		ViStatus __stdcall TLPM_setPowerAutoRange(
			[in] ViSession instrumentHandle, 
			[in] ViBoolean powerAutorangeMode);
		[helpcontext(92), entry("TLPM_getPowerAutorange"), helpstring("Get Power Autorange Mode")]
		ViStatus __stdcall TLPM_getPowerAutorange(
			[in] ViSession instrumentHandle, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViBoolean *powerAutorangeMode);
		[helpcontext(93), entry("TLPM_setPowerRange"), helpstring("Set Power Range")]
		ViStatus __stdcall TLPM_setPowerRange(
			[in] ViSession instrumentHandle, 
			[in] ViReal64 powerToMeasure);
		[helpcontext(94), entry("TLPM_getPowerRange"), helpstring("Get Power Range")]
		ViStatus __stdcall TLPM_getPowerRange(
			[in] ViSession instrumentHandle, 
			[in] ViInt16 attribute, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViReal64 *powerValue);
		[helpcontext(95), entry("TLPM_setPowerRef"), helpstring("Set Power Reference")]
		ViStatus __stdcall TLPM_setPowerRef(
			[in] ViSession instrumentHandle, 
			[in] ViReal64 powerReferenceValue);
		[helpcontext(96), entry("TLPM_getPowerRef"), helpstring("Get Power Reference")]
		ViStatus __stdcall TLPM_getPowerRef(
			[in] ViSession instrumentHandle, 
			[in] ViInt16 attribute, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViReal64 *powerReferenceValue);
		[helpcontext(97), entry("TLPM_setPowerRefState"), helpstring("Set Power Reference State")]
		ViStatus __stdcall TLPM_setPowerRefState(
			[in] ViSession instrumentHandle, 
			[in] ViBoolean powerReferenceState);
		[helpcontext(98), entry("TLPM_getPowerRefState"), helpstring("Get Power Reference State")]
		ViStatus __stdcall TLPM_getPowerRefState(
			[in] ViSession instrumentHandle, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViBoolean *powerReferenceState);
		[helpcontext(99), entry("TLPM_setPowerUnit"), helpstring("Set Power Unit")]
		ViStatus __stdcall TLPM_setPowerUnit(
			[in] ViSession instrumentHandle, 
			[in] ViInt16 powerUnit);
		[helpcontext(100), entry("TLPM_getPowerUnit"), helpstring("Get Power Unit")]
		ViStatus __stdcall TLPM_getPowerUnit(
			[in] ViSession instrumentHandle, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViInt16 *powerUnit);
		[helpcontext(101), entry("TLPM_getPowerCalibrationPointsInformation"), helpstring("Get Power Calibration Points Information")]
		ViStatus __stdcall TLPM_getPowerCalibrationPointsInformation(
			[in] ViSession instrumentHandle, 
			[in] ViUInt16 index, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 1), custom(746B27E2-FBD7-11d1-B311-0060970535CB, "[]")] LPSTR serialNumber, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 1), custom(746B27E2-FBD7-11d1-B311-0060970535CB, "[]")] LPSTR calibrationDate, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViUInt16 *calibrationPointsCount, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 1), custom(746B27E2-FBD7-11d1-B311-0060970535CB, "[]")] LPSTR author, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViUInt16 *sensorPosition);
		[helpcontext(102), entry("TLPM_getPowerCalibrationPointsState"), helpstring("Get Power Calibration Points State")]
		ViStatus __stdcall TLPM_getPowerCalibrationPointsState(
			[in] ViSession instrumentHandle, 
			[in] ViUInt16 index, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViBoolean *state);
		[helpcontext(103), entry("TLPM_setPowerCalibrationPointsState"), helpstring("Set Power Calibration Points State")]
		ViStatus __stdcall TLPM_setPowerCalibrationPointsState(
			[in] ViSession instrumentHandle, 
			[in] ViUInt16 index, 
			[in] ViBoolean state);
		[helpcontext(104), entry("TLPM_getPowerCalibrationPoints"), helpstring("Get Power Calibration Points")]
		ViStatus __stdcall TLPM_getPowerCalibrationPoints(
			[in] ViSession instrumentHandle, 
			[in] ViUInt16 index, 
			[in] ViUInt16 pointCounts, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 1), custom(746B27E2-FBD7-11d1-B311-0060970535CB, "[]")] ViReal64 *wavelengths, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 1), custom(746B27E2-FBD7-11d1-B311-0060970535CB, "[]")] ViReal64 *powerCorrectionFactors);
		[helpcontext(105), entry("TLPM_setPowerCalibrationPoints"), helpstring("Set Power Calibration Points")]
		ViStatus __stdcall TLPM_setPowerCalibrationPoints(
			[in] ViSession instrumentHandle, 
			[in] ViUInt16 index, 
			[in] ViUInt16 pointCounts, 
			[in, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 1), custom(746B27E2-FBD7-11d1-B311-0060970535CB, "[]")] ViReal64 *wavelengths, 
			[in, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 1), custom(746B27E2-FBD7-11d1-B311-0060970535CB, "[]")] ViReal64 *powersCorrectionFactors, 
			[in, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 1), custom(746B27E2-FBD7-11d1-B311-0060970535CB, "[]")] LPSTR author, 
			[in] ViUInt16 sensorPosition);
		[helpcontext(106), entry("TLPM_reinitSensor"), helpstring("Reinit Sensor")]
		ViStatus __stdcall TLPM_reinitSensor(
			[in] ViSession instrumentHandle);
		[helpcontext(107), entry("TLPM_setVoltageAutoRange"), helpstring("Set Voltage Autorange Mode")]
		ViStatus __stdcall TLPM_setVoltageAutoRange(
			[in] ViSession instrumentHandle, 
			[in] ViBoolean voltageAutorangeMode);
		[helpcontext(108), entry("TLPM_getVoltageAutorange"), helpstring("Get Voltage Autorange Mode")]
		ViStatus __stdcall TLPM_getVoltageAutorange(
			[in] ViSession instrumentHandle, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViBoolean *voltageAutorangeMode);
		[helpcontext(109), entry("TLPM_setVoltageRange"), helpstring("Set Voltage Range")]
		ViStatus __stdcall TLPM_setVoltageRange(
			[in] ViSession instrumentHandle, 
			[in] ViReal64 voltageToMeasure);
		[helpcontext(110), entry("TLPM_getVoltageRange"), helpstring("Get Voltage Range")]
		ViStatus __stdcall TLPM_getVoltageRange(
			[in] ViSession instrumentHandle, 
			[in] ViInt16 attribute, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViReal64 *voltageValue);
		[helpcontext(111), entry("TLPM_getVoltageRanges"), helpstring("Get Voltage Ranges")]
		ViStatus __stdcall TLPM_getVoltageRanges(
			[in] ViSession instrumentHandle, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 1), custom(746B27E2-FBD7-11d1-B311-0060970535CB, "[]")] ViReal64 *voltageValues, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViUInt16 *rangeCount);
		[helpcontext(112), entry("TLPM_setVoltageRef"), helpstring("Set Voltage Reference")]
		ViStatus __stdcall TLPM_setVoltageRef(
			[in] ViSession instrumentHandle, 
			[in] ViReal64 voltageReferenceValue);
		[helpcontext(113), entry("TLPM_getVoltageRef"), helpstring("Get Voltage Reference")]
		ViStatus __stdcall TLPM_getVoltageRef(
			[in] ViSession instrumentHandle, 
			[in] ViInt16 attribute, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViReal64 *voltageReferenceValue);
		[helpcontext(114), entry("TLPM_setVoltageRefState"), helpstring("Set Voltage Reference State")]
		ViStatus __stdcall TLPM_setVoltageRefState(
			[in] ViSession instrumentHandle, 
			[in] ViBoolean voltageReferenceState);
		[helpcontext(115), entry("TLPM_getVoltageRefState"), helpstring("Get Voltage Reference State")]
		ViStatus __stdcall TLPM_getVoltageRefState(
			[in] ViSession instrumentHandle, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViBoolean *voltageReferenceState);
		[helpcontext(116), entry("TLPM_setPeakThreshold"), helpstring("Set Peak Detector Threshold")]
		ViStatus __stdcall TLPM_setPeakThreshold(
			[in] ViSession instrumentHandle, 
			[in] ViReal64 peakDetectorThreshold);
		[helpcontext(117), entry("TLPM_getPeakThreshold"), helpstring("Get Peak Detector Threshold")]
		ViStatus __stdcall TLPM_getPeakThreshold(
			[in] ViSession instrumentHandle, 
			[in] ViInt16 attribute, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViReal64 *peakDetectorThreshold);
		[helpcontext(118), entry("TLPM_startPeakDetector"), helpstring("Start Peak Detector")]
		ViStatus __stdcall TLPM_startPeakDetector(
			[in] ViSession instrumentHandle);
		[helpcontext(119), entry("TLPM_isPeakDetectorRunning"), helpstring("Is Peak Detector Running")]
		ViStatus __stdcall TLPM_isPeakDetectorRunning(
			[in] ViSession instrumentHandle, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViBoolean *isRunning);
		[helpcontext(120), entry("TLPM_setPeakFilter"), helpstring("Set Peak Filter")]
		ViStatus __stdcall TLPM_setPeakFilter(
			[in] ViSession instrumentHandle, 
			[in] ViInt16 filter);
		[helpcontext(121), entry("TLPM_getPeakFilter"), helpstring("Get Peak Filter")]
		ViStatus __stdcall TLPM_getPeakFilter(
			[in] ViSession instrumentHandle, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViInt16 *filter);
		[helpcontext(122), entry("TLPM_setExtNtcParameter"), helpstring("Set External NTC Parameter")]
		ViStatus __stdcall TLPM_setExtNtcParameter(
			[in] ViSession instrumentHandle, 
			[in] ViReal64 r0Coefficient, 
			[in] ViReal64 betaCoefficient);
		[helpcontext(123), entry("TLPM_getExtNtcParameter"), helpstring("Get External NTC Parameter")]
		ViStatus __stdcall TLPM_getExtNtcParameter(
			[in] ViSession instrumentHandle, 
			[in] ViInt16 attribute, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViReal64 *r0Coefficient, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViReal64 *betaCoefficient);
		[helpcontext(124), entry("TLPM_setFilterPosition"), helpstring("Set Filter Position")]
		ViStatus __stdcall TLPM_setFilterPosition(
			[in] ViSession instrumentHandle, 
			[in] ViBoolean filterPosition);
		[helpcontext(125), entry("TLPM_getFilterPosition"), helpstring("Get Filter Position")]
		ViStatus __stdcall TLPM_getFilterPosition(
			[in] ViSession instrumentHandle, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViBoolean *filterPosition);
		[helpcontext(126), entry("TLPM_setFilterAutoMode"), helpstring("Set Filter Auto Mode")]
		ViStatus __stdcall TLPM_setFilterAutoMode(
			[in] ViSession instrumentHandle, 
			[in] ViBoolean filterAutoPositionDetection);
		[helpcontext(127), entry("TLPM_getFilterAutoMode"), helpstring("Get Filter Auto Mode")]
		ViStatus __stdcall TLPM_getFilterAutoMode(
			[in] ViSession instrumentHandle, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViBoolean *filterAutoPositionDetection);
		[helpcontext(128), entry("TLPM_getAnalogOutputSlopeRange"), helpstring("Get Analog Output Slope Range")]
		ViStatus __stdcall TLPM_getAnalogOutputSlopeRange(
			[in] ViSession instrumentHandle, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViReal64 *minSlope, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViReal64 *maxSlope);
		[helpcontext(129), entry("TLPM_setAnalogOutputSlope"), helpstring("Set Analog Output Slope")]
		ViStatus __stdcall TLPM_setAnalogOutputSlope(
			[in] ViSession instrumentHandle, 
			[in] ViReal64 slope);
		[helpcontext(130), entry("TLPM_getAnalogOutputSlope"), helpstring("Get Analog Output Slope")]
		ViStatus __stdcall TLPM_getAnalogOutputSlope(
			[in] ViSession instrumentHandle, 
			[in] ViInt16 attribute, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViReal64 *slope);
		[helpcontext(131), entry("TLPM_getAnalogOutputVoltageRange"), helpstring("Get Analog Output Voltage Range")]
		ViStatus __stdcall TLPM_getAnalogOutputVoltageRange(
			[in] ViSession instrumentHandle, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViReal64 *minVoltage, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViReal64 *maxVoltage);
		[helpcontext(132), entry("TLPM_getAnalogOutputVoltage"), helpstring("Get Analog Output Voltage")]
		ViStatus __stdcall TLPM_getAnalogOutputVoltage(
			[in] ViSession instrumentHandle, 
			[in] ViInt16 attribute, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViReal64 *voltage);
		[helpcontext(133), entry("TLPM_getAnalogOutputHub"), helpstring("Get Analog Output Hub")]
		ViStatus __stdcall TLPM_getAnalogOutputHub(
			[in] ViSession instrumentHandle, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViReal64 *voltage);
		[helpcontext(134), entry("TLPM_setAnalogOutputHub"), helpstring("Set Analog Output Hub")]
		ViStatus __stdcall TLPM_setAnalogOutputHub(
			[in] ViSession instrumentHandle, 
			[in] ViReal64 voltage);
		[helpcontext(135), entry("TLPM_getPositionAnalogOutputSlopeRange"), helpstring("Get Position Analog Output Slope Range")]
		ViStatus __stdcall TLPM_getPositionAnalogOutputSlopeRange(
			[in] ViSession instrumentHandle, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViReal64 *minSlope, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViReal64 *maxSlope);
		[helpcontext(136), entry("TLPM_setPositionAnalogOutputSlope"), helpstring("Set Position Analog Output Slope")]
		ViStatus __stdcall TLPM_setPositionAnalogOutputSlope(
			[in] ViSession instrumentHandle, 
			[in] ViReal64 slope);
		[helpcontext(137), entry("TLPM_getPositionAnalogOutputSlope"), helpstring("Get Position Analog Output Slope")]
		ViStatus __stdcall TLPM_getPositionAnalogOutputSlope(
			[in] ViSession instrumentHandle, 
			[in] ViInt16 attribute, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViReal64 *slope);
		[helpcontext(138), entry("TLPM_getPositionAnalogOutputVoltageRange"), helpstring("Get Position Analog Output Voltage Range")]
		ViStatus __stdcall TLPM_getPositionAnalogOutputVoltageRange(
			[in] ViSession instrumentHandle, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViReal64 *minVoltage, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViReal64 *maxVoltage);
		[helpcontext(139), entry("TLPM_getPositionAnalogOutputVoltage"), helpstring("Get Position Analog Output Voltage")]
		ViStatus __stdcall TLPM_getPositionAnalogOutputVoltage(
			[in] ViSession instrumentHandle, 
			[in] ViInt16 attribute, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViReal64 *voltageX, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViReal64 *voltageY);
		[helpcontext(140), entry("TLPM_getMeasPinMode"), helpstring("Get Meas Pin Mode")]
		ViStatus __stdcall TLPM_getMeasPinMode(
			[in] ViSession instrumentHandle, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViBoolean *state);
		[helpcontext(141), entry("TLPM_getMeasPinPowerLevel"), helpstring("Get Meas Pin Power Level")]
		ViStatus __stdcall TLPM_getMeasPinPowerLevel(
			[in] ViSession instrumentHandle, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViReal64 *level);
		[helpcontext(142), entry("TLPM_setMeasPinPowerLevel"), helpstring("Set Meas Pin Power Level")]
		ViStatus __stdcall TLPM_setMeasPinPowerLevel(
			[in] ViSession instrumentHandle, 
			[in] ViReal64 level);
		[helpcontext(143), entry("TLPM_getMeasPinEnergyLevel"), helpstring("Get Meas Pin Energy Level")]
		ViStatus __stdcall TLPM_getMeasPinEnergyLevel(
			[in] ViSession instrumentHandle, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViReal64 *level);
		[helpcontext(144), entry("TLPM_setMeasPinEnergyLevel"), helpstring("Set Meas Pin Energy Level")]
		ViStatus __stdcall TLPM_setMeasPinEnergyLevel(
			[in] ViSession instrumentHandle, 
			[in] ViReal64 level);
		[helpcontext(145), entry("TLPM_setNegativePulseWidth"), helpstring("Set Negative Pulse Width")]
		ViStatus __stdcall TLPM_setNegativePulseWidth(
			[in] ViSession instrumentHandle, 
			[in] ViReal64 pulseDuration);
		[helpcontext(146), entry("TLPM_setPositivePulseWidth"), helpstring("Set Positive Pulse Width")]
		ViStatus __stdcall TLPM_setPositivePulseWidth(
			[in] ViSession instrumentHandle, 
			[in] ViReal64 pulseDuration);
		[helpcontext(147), entry("TLPM_setNegativeDutyCycle"), helpstring("Set Negative Duty Cycle")]
		ViStatus __stdcall TLPM_setNegativeDutyCycle(
			[in] ViSession instrumentHandle, 
			[in] ViReal64 dutyCycle);
		[helpcontext(148), entry("TLPM_setPositiveDutyCycle"), helpstring("Set Positive Duty Cycle")]
		ViStatus __stdcall TLPM_setPositiveDutyCycle(
			[in] ViSession instrumentHandle, 
			[in] ViReal64 dutyCycle);
		[helpcontext(149), entry("TLPM_measCurrent"), helpstring("Measure Current")]
		ViStatus __stdcall TLPM_measCurrent(
			[in] ViSession instrumentHandle, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViReal64 *current);
		[helpcontext(150), entry("TLPM_measVoltage"), helpstring("Measure Voltage")]
		ViStatus __stdcall TLPM_measVoltage(
			[in] ViSession instrumentHandle, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViReal64 *voltage);
		[helpcontext(151), entry("TLPM_measPower"), helpstring("Measure Power")]
		ViStatus __stdcall TLPM_measPower(
			[in] ViSession instrumentHandle, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViReal64 *power);
		[helpcontext(152), entry("TLPM_measEnergy"), helpstring("Measure Energy")]
		ViStatus __stdcall TLPM_measEnergy(
			[in] ViSession instrumentHandle, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViReal64 *energy);
		[helpcontext(153), entry("TLPM_measPowerDens"), helpstring("Measure Power Density")]
		ViStatus __stdcall TLPM_measPowerDens(
			[in] ViSession instrumentHandle, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViReal64 *powerDensity);
		[helpcontext(154), entry("TLPM_measEnergyDens"), helpstring("Measure Energy Density")]
		ViStatus __stdcall TLPM_measEnergyDens(
			[in] ViSession instrumentHandle, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViReal64 *energyDensity);
		[helpcontext(155), entry("TLPM_measFreq"), helpstring("Measure Frequency")]
		ViStatus __stdcall TLPM_measFreq(
			[in] ViSession instrumentHandle, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViReal64 *frequency);
		[helpcontext(156), entry("TLPM_measAuxAD0"), helpstring("Measure Auxiliary AD0 Voltage")]
		ViStatus __stdcall TLPM_measAuxAD0(
			[in] ViSession instrumentHandle, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViReal64 *voltage);
		[helpcontext(157), entry("TLPM_measAuxAD1"), helpstring("Measure Auxiliary AD1 Voltage")]
		ViStatus __stdcall TLPM_measAuxAD1(
			[in] ViSession instrumentHandle, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViReal64 *voltage);
		[helpcontext(158), entry("TLPM_measHeadTemperature"), helpstring("Measure Head Temperature")]
		ViStatus __stdcall TLPM_measHeadTemperature(
			[in] ViSession instrumentHandle, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViReal64 *temperature);
		[helpcontext(159), entry("TLPM_measEmmHumidity"), helpstring("Measure Environmental Humidity")]
		ViStatus __stdcall TLPM_measEmmHumidity(
			[in] ViSession instrumentHandle, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViReal64 *humidity);
		[helpcontext(160), entry("TLPM_measEmmTemperature"), helpstring("Measure Environmental Temperature")]
		ViStatus __stdcall TLPM_measEmmTemperature(
			[in] ViSession instrumentHandle, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViReal64 *temperature);
		[helpcontext(161), entry("TLPM_measExtNtcTemperature"), helpstring("Measure External NTC Temperature")]
		ViStatus __stdcall TLPM_measExtNtcTemperature(
			[in] ViSession instrumentHandle, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViReal64 *temperature);
		[helpcontext(162), entry("TLPM_measExtNtcResistance"), helpstring("Measure External NTC Resistance")]
		ViStatus __stdcall TLPM_measExtNtcResistance(
			[in] ViSession instrumentHandle, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViReal64 *resistance);
		[helpcontext(163), entry("TLPM_meas4QPositions"), helpstring("Measure 4Q Positions")]
		ViStatus __stdcall TLPM_meas4QPositions(
			[in] ViSession instrumentHandle, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViReal64 *xPosition, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViReal64 *yPosition);
		[helpcontext(164), entry("TLPM_meas4QVoltages"), helpstring("Measure 4Q Voltages")]
		ViStatus __stdcall TLPM_meas4QVoltages(
			[in] ViSession instrumentHandle, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViReal64 *voltage1, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViReal64 *voltage2, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViReal64 *voltage3, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViReal64 *voltage4);
		[helpcontext(165), entry("TLPM_measNegPulseWidth"), helpstring("Measure Negative Pulse Width")]
		ViStatus __stdcall TLPM_measNegPulseWidth(
			[in] ViSession instrumentHandle, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViReal64 *negativePulseWidth);
		[helpcontext(166), entry("TLPM_measPosPulseWidth"), helpstring("Measure Positive Pulse Width")]
		ViStatus __stdcall TLPM_measPosPulseWidth(
			[in] ViSession instrumentHandle, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViReal64 *positivePulseWidth);
		[helpcontext(167), entry("TLPM_measNegDutyCycle"), helpstring("Measure Negative Duty Cycle")]
		ViStatus __stdcall TLPM_measNegDutyCycle(
			[in] ViSession instrumentHandle, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViReal64 *negativeDutyCycle);
		[helpcontext(168), entry("TLPM_measPosDutyCycle"), helpstring("Measure Positive Duty Cycle")]
		ViStatus __stdcall TLPM_measPosDutyCycle(
			[in] ViSession instrumentHandle, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViReal64 *positiveDutyCycle);
		[helpcontext(169), entry("TLPM_resetFastArrayMeasurement"), helpstring("Reset Fast Array Measurement")]
		ViStatus __stdcall TLPM_resetFastArrayMeasurement(
			[in] ViSession instrumentHandle);
		[helpcontext(170), entry("TLPM_confPowerFastArrayMeasurement"), helpstring("Conf Power Fast Array Measurement")]
		ViStatus __stdcall TLPM_confPowerFastArrayMeasurement(
			[in] ViSession instrumentHandle);
		[helpcontext(171), entry("TLPM_confCurrentFastArrayMeasurement"), helpstring("Conf Current Fast Array Measurement")]
		ViStatus __stdcall TLPM_confCurrentFastArrayMeasurement(
			[in] ViSession instrumentHandle);
		[helpcontext(172), entry("TLPM_confVoltageFastArrayMeasurement"), helpstring("Conf Voltage Fast Array Measurement")]
		ViStatus __stdcall TLPM_confVoltageFastArrayMeasurement(
			[in] ViSession instrumentHandle);
		[helpcontext(173), entry("TLPM_confPDensityFastArrayMeasurement"), helpstring("Conf PDensity Fast Array Measurement")]
		ViStatus __stdcall TLPM_confPDensityFastArrayMeasurement(
			[in] ViSession instrumentHandle);
		[helpcontext(174), entry("TLPM_confEnergyFastArrayMeasurement"), helpstring("Conf Energy Fast Array Measurement")]
		ViStatus __stdcall TLPM_confEnergyFastArrayMeasurement(
			[in] ViSession instrumentHandle);
		[helpcontext(175), entry("TLPM_confEDensityFastArrayMeasurement"), helpstring("Conf EDensity Fast Array Measurement")]
		ViStatus __stdcall TLPM_confEDensityFastArrayMeasurement(
			[in] ViSession instrumentHandle);
		[helpcontext(176), entry("TLPM_getNextFastArrayMeasurement"), helpstring("Get Next Fast Array Measurement")]
		ViStatus __stdcall TLPM_getNextFastArrayMeasurement(
			[in] ViSession instrumentHandle, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViUInt16 *count, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 1), custom(746B27E2-FBD7-11d1-B311-0060970535CB, "[]")] ViUInt32 *timestamps, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 1), custom(746B27E2-FBD7-11d1-B311-0060970535CB, "[]")] ViReal64 *measData);
		[helpcontext(177), entry("TLPM_getFastMaxSamplerate"), helpstring("Get Fast Max Samplerate")]
		ViStatus __stdcall TLPM_getFastMaxSamplerate(
			[in] ViSession instrumentHandle, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViUInt32 *maxSamplerate);
		[helpcontext(178), entry("TLPM_confPowerMeasurementSequence"), helpstring("Conf Power Measurement Sequence")]
		ViStatus __stdcall TLPM_confPowerMeasurementSequence(
			[in] ViSession instrumentHandle, 
			[in] ViUInt32 averagingLimit);
		[helpcontext(179), entry("TLPM_confPowerMeasurementSequenceHWTrigger"), helpstring("Conf Power Measurement Sequence HW Trigger")]
		ViStatus __stdcall TLPM_confPowerMeasurementSequenceHWTrigger(
			[in] ViSession instrumentHandle, 
			[in] ViUInt32 averagingLimit, 
			[in] ViUInt32 triggerHorizontalPosition);
		[helpcontext(180), entry("TLPM_confCurrentMeasurementSequence"), helpstring("Conf Current Measurement Sequence")]
		ViStatus __stdcall TLPM_confCurrentMeasurementSequence(
			[in] ViSession instrumentHandle, 
			[in] ViUInt32 averagingLimit);
		[helpcontext(181), entry("TLPM_confCurrentMeasurementSequenceHWTrigger"), helpstring("Conf Current Measurement Sequence HW Trigger")]
		ViStatus __stdcall TLPM_confCurrentMeasurementSequenceHWTrigger(
			[in] ViSession instrumentHandle, 
			[in] ViUInt32 averagingLimit, 
			[in] ViUInt32 triggerHorizontalPosition);
		[helpcontext(182), entry("TLPM_startMeasurementSequence"), helpstring("Start Measurement Sequence")]
		ViStatus __stdcall TLPM_startMeasurementSequence(
			[in] ViSession instrumentHandle, 
			[in] ViUInt32 autoTriggerDelay, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViBoolean *triggerForced);
		[helpcontext(183), entry("TLPM_getMeasurementSequence"), helpstring("Get Measurement Sequence")]
		ViStatus __stdcall TLPM_getMeasurementSequence(
			[in] ViSession instrumentHandle, 
			[in] ViUInt32 baseTime, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 1), custom(746B27E2-FBD7-11d1-B311-0060970535CB, "[]")] float *timeStamps, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 1), custom(746B27E2-FBD7-11d1-B311-0060970535CB, "[]")] float *values);
		[helpcontext(184), entry("TLPM_getMeasurementSequenceHWTrigger"), helpstring("Get Measurement Sequence HW Trigger")]
		ViStatus __stdcall TLPM_getMeasurementSequenceHWTrigger(
			[in] ViSession instrumentHandle, 
			[in] ViUInt32 baseTime, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 1), custom(746B27E2-FBD7-11d1-B311-0060970535CB, "[]")] float *timeStamps, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 1), custom(746B27E2-FBD7-11d1-B311-0060970535CB, "[]")] float *values);
	};
	[dllname("TLPM.dll"), helpcontext(215), helpstring("Digital I/O")]
	module DigitalIO{
		[helpcontext(185), entry("TLPM_setDigIoDirection"), helpstring("Set Digital I/O Direction")]
		ViStatus __stdcall TLPM_setDigIoDirection(
			[in] ViSession instrumentHandle, 
			[in] ViBoolean IO0, 
			[in] ViBoolean IO1, 
			[in] ViBoolean IO2, 
			[in] ViBoolean IO3);
		[helpcontext(186), entry("TLPM_getDigIoDirection"), helpstring("Get Digital I/O Direction")]
		ViStatus __stdcall TLPM_getDigIoDirection(
			[in] ViSession instrumentHandle, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViBoolean *IO0, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViBoolean *IO1, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViBoolean *IO2, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViBoolean *IO3);
		[helpcontext(187), entry("TLPM_setDigIoOutput"), helpstring("Set Digital I/O Output")]
		ViStatus __stdcall TLPM_setDigIoOutput(
			[in] ViSession instrumentHandle, 
			[in] ViBoolean IO0, 
			[in] ViBoolean IO1, 
			[in] ViBoolean IO2, 
			[in] ViBoolean IO3);
		[helpcontext(188), entry("TLPM_getDigIoOutput"), helpstring("Get Digital I/O Output")]
		ViStatus __stdcall TLPM_getDigIoOutput(
			[in] ViSession instrumentHandle, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViBoolean *IO0, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViBoolean *IO1, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViBoolean *IO2, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViBoolean *IO3);
		[helpcontext(189), entry("TLPM_getDigIoPort"), helpstring("Get Digital I/O Port")]
		ViStatus __stdcall TLPM_getDigIoPort(
			[in] ViSession instrumentHandle, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViBoolean *IO0, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViBoolean *IO1, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViBoolean *IO2, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViBoolean *IO3);
	};
	[dllname("TLPM.dll"), helpcontext(215), helpstring("Digital I/O PM103")]
	module DigitalIOPM103{
		[helpcontext(190), entry("TLPM_setDigIoPinMode"), helpstring("Set Digital I/O Pin Mode")]
		ViStatus __stdcall TLPM_setDigIoPinMode(
			[in] ViSession instrumentHandle, 
			[in] ViInt16 pinNumber, 
			[in] ViUInt16 pinMode);
		[helpcontext(191), entry("TLPM_getDigIoPinMode"), helpstring("Get Digital I/O Pin Mode")]
		ViStatus __stdcall TLPM_getDigIoPinMode(
			[in] ViSession instrumentHandle, 
			[in] ViInt16 pinNumber, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViUInt16 *pinMode);
		[helpcontext(192), entry("TLPM_setDigIoPinOutput"), helpstring("Set Digital I/O Pin Output")]
		ViStatus __stdcall TLPM_setDigIoPinOutput(
			[in] ViSession instrumentHandle, 
			[in] ViBoolean IO0, 
			[in] ViBoolean IO1, 
			[in] ViBoolean IO2, 
			[in] ViBoolean IO3);
		[helpcontext(193), entry("TLPM_getDigIoPinOutput"), helpstring("Get Digital I/O Pin Output")]
		ViStatus __stdcall TLPM_getDigIoPinOutput(
			[in] ViSession instrumentHandle, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViBoolean *IO0, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViBoolean *IO1, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViBoolean *IO2, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViBoolean *IO3);
		[helpcontext(194), entry("TLPM_getDigIoPinInput"), helpstring("Get Digital I/O Pin Input")]
		ViStatus __stdcall TLPM_getDigIoPinInput(
			[in] ViSession instrumentHandle, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViBoolean *IO0, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViBoolean *IO1, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViBoolean *IO2, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViBoolean *IO3);
	};
	[dllname("TLPM.dll"), helpcontext(215), helpstring("Utility Functions")]
	module UtilityFunctions{
		[helpcontext(195), entry("TLPM_errorMessage"), helpstring("Error Message")]
		ViStatus __stdcall TLPM_errorMessage(
			[in] ViSession instrumentHandle, 
			[in] ViStatus statusCode, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 1), custom(746B27E2-FBD7-11d1-B311-0060970535CB, "[]")] LPSTR description);
		[helpcontext(196), entry("TLPM_errorQuery"), helpstring("Error Query")]
		ViStatus __stdcall TLPM_errorQuery(
			[in] ViSession instrumentHandle, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViInt32 *errorNumber, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 1), custom(746B27E2-FBD7-11d1-B311-0060970535CB, "[]")] LPSTR errorMessage);
		[helpcontext(197), entry("TLPM_errorQueryMode"), helpstring("Error Query Mode")]
		ViStatus __stdcall TLPM_errorQueryMode(
			[in] ViSession instrumentHandle, 
			[in] ViBoolean mode);
		[helpcontext(198), entry("TLPM_reset"), helpstring("Reset")]
		ViStatus __stdcall TLPM_reset(
			[in] ViSession instrumentHandle);
		[helpcontext(199), entry("TLPM_selfTest"), helpstring("Self-Test")]
		ViStatus __stdcall TLPM_selfTest(
			[in] ViSession instrumentHandle, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViInt16 *selfTestResult, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 1), custom(746B27E2-FBD7-11d1-B311-0060970535CB, "[]")] LPSTR description);
		[helpcontext(200), entry("TLPM_revisionQuery"), helpstring("Revision Query")]
		ViStatus __stdcall TLPM_revisionQuery(
			[in] ViSession instrumentHandle, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 1), custom(746B27E2-FBD7-11d1-B311-0060970535CB, "[]")] LPSTR instrumentDriverRevision, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 1), custom(746B27E2-FBD7-11d1-B311-0060970535CB, "[]")] LPSTR firmwareRevision);
		[helpcontext(201), entry("TLPM_identificationQuery"), helpstring("Identification Query")]
		ViStatus __stdcall TLPM_identificationQuery(
			[in] ViSession instrumentHandle, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 1), custom(746B27E2-FBD7-11d1-B311-0060970535CB, "[]")] LPSTR manufacturerName, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 1), custom(746B27E2-FBD7-11d1-B311-0060970535CB, "[]")] LPSTR deviceName, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 1), custom(746B27E2-FBD7-11d1-B311-0060970535CB, "[]")] LPSTR serialNumber, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 1), custom(746B27E2-FBD7-11d1-B311-0060970535CB, "[]")] LPSTR firmwareRevision);
		[helpcontext(202), entry("TLPM_getCalibrationMsg"), helpstring("Calibration Message")]
		ViStatus __stdcall TLPM_getCalibrationMsg(
			[in] ViSession instrumentHandle, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 1), custom(746B27E2-FBD7-11d1-B311-0060970535CB, "[]")] LPSTR message_);
		[helpcontext(203), entry("TLPM_getSensorInfo"), helpstring("Sensor Information")]
		ViStatus __stdcall TLPM_getSensorInfo(
			[in] ViSession instrumentHandle, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 1), custom(746B27E2-FBD7-11d1-B311-0060970535CB, "[]")] LPSTR sensorName, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 1), custom(746B27E2-FBD7-11d1-B311-0060970535CB, "[]")] LPSTR sensorSerialNumber, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 1), custom(746B27E2-FBD7-11d1-B311-0060970535CB, "[]")] LPSTR sensorCalibrationMessage, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViInt16 *sensorType, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViInt16 *sensorSubtype, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViInt16 *sensorFlags);
		[helpcontext(204), entry("TLPM_writeRaw"), helpstring("Raw Write to Instrument")]
		ViStatus __stdcall TLPM_writeRaw(
			[in] ViSession instrumentHandle, 
			[in] ViString command);
		[helpcontext(205), entry("TLPM_readRaw"), helpstring("Raw Read from Instrument")]
		ViStatus __stdcall TLPM_readRaw(
			[in] ViSession instrumentHandle, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 1), custom(746B27E2-FBD7-11d1-B311-0060970535CB, "[]")] LPSTR buffer, 
			[in] ViUInt32 size, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViUInt32 *returnCount);
		[helpcontext(206), entry("TLPM_setTimeoutValue"), helpstring("Set Communication Timeout")]
		ViStatus __stdcall TLPM_setTimeoutValue(
			[in] ViSession instrumentHandle, 
			[in] ViUInt32 value);
		[helpcontext(207), entry("TLPM_getTimeoutValue"), helpstring("Get Communication Timeout")]
		ViStatus __stdcall TLPM_getTimeoutValue(
			[in] ViSession instrumentHandle, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViUInt32 *value);
	};
	[dllname("TLPM.dll"), helpcontext(215), helpstring("Attributes")]
	module Attributes{
		[helpcontext(208), entry("TLPM_setDisplayName"), helpstring("Set Display Name")]
		ViStatus __stdcall TLPM_setDisplayName(
			[in] ViSession instrumentHandle, 
			[in, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 1), custom(746B27E2-FBD7-11d1-B311-0060970535CB, "[]")] LPSTR name);
		[helpcontext(209), entry("TLPM_getDisplayName"), helpstring("Get Display Name")]
		ViStatus __stdcall TLPM_getDisplayName(
			[in] ViSession instrumentHandle, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 1), custom(746B27E2-FBD7-11d1-B311-0060970535CB, "[]")] LPSTR name);
		[helpcontext(210), entry("TLPM_setDeviceBaudrate"), helpstring("Set Device Baudrate")]
		ViStatus __stdcall TLPM_setDeviceBaudrate(
			[in] ViSession instrumentHandle, 
			[in] ViUInt32 baudrate);
		[helpcontext(211), entry("TLPM_getDeviceBaudrate"), helpstring("Get Device Baudrate")]
		ViStatus __stdcall TLPM_getDeviceBaudrate(
			[in] ViSession instrumentHandle, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViUInt32 *baudrate);
		[helpcontext(212), entry("TLPM_setDriverBaudrate"), helpstring("Set Driver Baudrate")]
		ViStatus __stdcall TLPM_setDriverBaudrate(
			[in] ViSession instrumentHandle, 
			[in] ViUInt32 baudrate);
		[helpcontext(213), entry("TLPM_getDriverBaudrate"), helpstring("Get Driver Baudrate")]
		ViStatus __stdcall TLPM_getDriverBaudrate(
			[in] ViSession instrumentHandle, 
			[in, out, custom(53D57340-9A16-11d0-A62C-0020AF16F78E, 0)] ViUInt32 *baudrate);
	};
	[dllname("TLPM.dll"), helpcontext(215), helpstring("Close")]
	module Close{
		[helpcontext(214), entry("TLPM_close"), helpstring("Close")]
		ViStatus __stdcall TLPM_close(
			[in] ViSession instrumentHandle);
	};
};
