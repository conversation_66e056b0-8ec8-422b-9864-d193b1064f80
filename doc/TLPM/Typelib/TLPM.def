LIBRARY TLPM
DESCRIPTION "Thorlabs Power Meter Instrument Driver"

EXPORTS
TLPM_init
TLPM_findRsrc
TLPM_getRsrcName
TLPM_getRsrcInfo
TLPM_writeRegister
TLPM_readRegister
TLPM_presetRegister
TLPM_setTime
TLPM_getTime
TLPM_setLineFrequency
TLPM_getLineFrequency
TLPM_getBatteryVoltage
TLPM_setDispBrightness
TLPM_getDispBrightness
TLPM_setDispContrast
TLPM_getDispContrast
TLPM_setIPAddress
TLPM_getIPAddress
TLPM_setIPMask
TLPM_getIPMask
TLPM_getMACAddress
TLPM_setDHCP
TLPM_getDHCP
TLPM_setHostname
TLPM_getHostname
TLPM_setWebPort
TLPM_getWebPort
TLPM_setEncryption
TLPM_getEncryption
TLPM_setSCPIPort
TLPM_getSCPIPort
TLPM_setDFUPort
TLPM_getDFUPort
TLPM_setLANPropagation
TLPM_getLANPropagation
TLPM_setEnableNetSearch
TLPM_getEnableNetSearch
TLPM_setNetSearchMask
TLPM_setInputFilterState
TLPM_getInputFilterState
TLPM_setAccelState
TLPM_getAccelState
TLPM_setAccelMode
TLPM_getAccelMode
TLPM_setAccelTau
TLPM_getAccelTau
TLPM_setInputAdapterType
TLPM_getInputAdapterType
TLPM_setAvgTime
TLPM_getAvgTime
TLPM_setAvgCnt
TLPM_getAvgCnt
TLPM_setAttenuation
TLPM_getAttenuation
TLPM_startDarkAdjust
TLPM_cancelDarkAdjust
TLPM_getDarkAdjustState
TLPM_getDarkOffset
TLPM_startZeroPos
TLPM_cancelZeroPos
TLPM_setZeroPos
TLPM_getZeroPos
TLPM_setBeamDia
TLPM_getBeamDia
TLPM_setWavelength
TLPM_getWavelength
TLPM_setPhotodiodeResponsivity
TLPM_getPhotodiodeResponsivity
TLPM_setThermopileResponsivity
TLPM_getThermopileResponsivity
TLPM_setPyrosensorResponsivity
TLPM_getPyrosensorResponsivity
TLPM_setCurrentAutoRange
TLPM_getCurrentAutorange
TLPM_setCurrentRange
TLPM_getCurrentRange
TLPM_getCurrentRanges
TLPM_setCurrentRef
TLPM_getCurrentRef
TLPM_setCurrentRefState
TLPM_getCurrentRefState
TLPM_setEnergyRange
TLPM_getEnergyRange
TLPM_setEnergyRef
TLPM_getEnergyRef
TLPM_setEnergyRefState
TLPM_getEnergyRefState
TLPM_getFreqRange
TLPM_setFreqMode
TLPM_getFreqMode
TLPM_setPowerAutoRange
TLPM_getPowerAutorange
TLPM_setPowerRange
TLPM_getPowerRange
TLPM_setPowerRef
TLPM_getPowerRef
TLPM_setPowerRefState
TLPM_getPowerRefState
TLPM_setPowerUnit
TLPM_getPowerUnit
TLPM_getPowerCalibrationPointsInformation
TLPM_getPowerCalibrationPointsState
TLPM_setPowerCalibrationPointsState
TLPM_getPowerCalibrationPoints
TLPM_setPowerCalibrationPoints
TLPM_reinitSensor
TLPM_setVoltageAutoRange
TLPM_getVoltageAutorange
TLPM_setVoltageRange
TLPM_getVoltageRange
TLPM_getVoltageRanges
TLPM_setVoltageRef
TLPM_getVoltageRef
TLPM_setVoltageRefState
TLPM_getVoltageRefState
TLPM_setPeakThreshold
TLPM_getPeakThreshold
TLPM_startPeakDetector
TLPM_isPeakDetectorRunning
TLPM_setPeakFilter
TLPM_getPeakFilter
TLPM_setExtNtcParameter
TLPM_getExtNtcParameter
TLPM_setFilterPosition
TLPM_getFilterPosition
TLPM_setFilterAutoMode
TLPM_getFilterAutoMode
TLPM_getAnalogOutputSlopeRange
TLPM_setAnalogOutputSlope
TLPM_getAnalogOutputSlope
TLPM_getAnalogOutputVoltageRange
TLPM_getAnalogOutputVoltage
TLPM_getAnalogOutputHub
TLPM_setAnalogOutputHub
TLPM_getPositionAnalogOutputSlopeRange
TLPM_setPositionAnalogOutputSlope
TLPM_getPositionAnalogOutputSlope
TLPM_getPositionAnalogOutputVoltageRange
TLPM_getPositionAnalogOutputVoltage
TLPM_getMeasPinMode
TLPM_getMeasPinPowerLevel
TLPM_setMeasPinPowerLevel
TLPM_getMeasPinEnergyLevel
TLPM_setMeasPinEnergyLevel
TLPM_setNegativePulseWidth
TLPM_setPositivePulseWidth
TLPM_setNegativeDutyCycle
TLPM_setPositiveDutyCycle
TLPM_measCurrent
TLPM_measVoltage
TLPM_measPower
TLPM_measEnergy
TLPM_measPowerDens
TLPM_measEnergyDens
TLPM_measFreq
TLPM_measAuxAD0
TLPM_measAuxAD1
TLPM_measHeadTemperature
TLPM_measEmmHumidity
TLPM_measEmmTemperature
TLPM_measExtNtcTemperature
TLPM_measExtNtcResistance
TLPM_meas4QPositions
TLPM_meas4QVoltages
TLPM_measNegPulseWidth
TLPM_measPosPulseWidth
TLPM_measNegDutyCycle
TLPM_measPosDutyCycle
TLPM_resetFastArrayMeasurement
TLPM_confPowerFastArrayMeasurement
TLPM_confCurrentFastArrayMeasurement
TLPM_confVoltageFastArrayMeasurement
TLPM_confPDensityFastArrayMeasurement
TLPM_confEnergyFastArrayMeasurement
TLPM_confEDensityFastArrayMeasurement
TLPM_getNextFastArrayMeasurement
TLPM_getFastMaxSamplerate
TLPM_confPowerMeasurementSequence
TLPM_confPowerMeasurementSequenceHWTrigger
TLPM_confCurrentMeasurementSequence
TLPM_confCurrentMeasurementSequenceHWTrigger
TLPM_startMeasurementSequence
TLPM_getMeasurementSequence
TLPM_getMeasurementSequenceHWTrigger
TLPM_setDigIoDirection
TLPM_getDigIoDirection
TLPM_setDigIoOutput
TLPM_getDigIoOutput
TLPM_getDigIoPort
TLPM_setDigIoPinMode
TLPM_getDigIoPinMode
TLPM_setDigIoPinOutput
TLPM_getDigIoPinOutput
TLPM_getDigIoPinInput
TLPM_errorMessage
TLPM_errorQuery
TLPM_errorQueryMode
TLPM_reset
TLPM_selfTest
TLPM_revisionQuery
TLPM_identificationQuery
TLPM_getCalibrationMsg
TLPM_getSensorInfo
TLPM_writeRaw
TLPM_readRaw
TLPM_setTimeoutValue
TLPM_getTimeoutValue
TLPM_setDisplayName
TLPM_getDisplayName
TLPM_setDeviceBaudrate
TLPM_getDeviceBaudrate
TLPM_setDriverBaudrate
TLPM_getDriverBaudrate
TLPM_close
