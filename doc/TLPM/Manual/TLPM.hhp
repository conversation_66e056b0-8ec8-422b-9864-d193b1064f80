[ALIAS]
IDH_TLPM_init=TLPM_files\FunctTLPM_init.html
IDH_TLPM_findRsrc=TLPM_files\FunctTLPM_findRsrc.html
IDH_TLPM_getRsrcName=TLPM_files\FunctTLPM_getRsrcName.html
IDH_TLPM_getRsrcInfo=TLPM_files\FunctTLPM_getRsrcInfo.html
IDH_TLPM_writeRegister=TLPM_files\FunctTLPM_writeRegister.html
IDH_TLPM_readRegister=TLPM_files\FunctTLPM_readRegister.html
IDH_TLPM_presetRegister=TLPM_files\FunctTLPM_presetRegister.html
IDH_TLPM_setTime=TLPM_files\FunctTLPM_setTime.html
IDH_TLPM_getTime=TLPM_files\FunctTLPM_getTime.html
IDH_TLPM_setLineFrequency=TLPM_files\FunctTLPM_setLineFrequency.html
IDH_TLPM_getLineFrequency=TLPM_files\FunctTLPM_getLineFrequency.html
IDH_TLPM_getBatteryVoltage=TLPM_files\FunctTLPM_getBatteryVoltage.html
IDH_TLPM_setDispBrightness=TLPM_files\FunctTLPM_setDispBrightness.html
IDH_TLPM_getDispBrightness=TLPM_files\FunctTLPM_getDispBrightness.html
IDH_TLPM_setDispContrast=TLPM_files\FunctTLPM_setDispContrast.html
IDH_TLPM_getDispContrast=TLPM_files\FunctTLPM_getDispContrast.html
IDH_TLPM_setIPAddress=TLPM_files\FunctTLPM_setIPAddress.html
IDH_TLPM_getIPAddress=TLPM_files\FunctTLPM_getIPAddress.html
IDH_TLPM_setIPMask=TLPM_files\FunctTLPM_setIPMask.html
IDH_TLPM_getIPMask=TLPM_files\FunctTLPM_getIPMask.html
IDH_TLPM_getMACAddress=TLPM_files\FunctTLPM_getMACAddress.html
IDH_TLPM_setDHCP=TLPM_files\FunctTLPM_setDHCP.html
IDH_TLPM_getDHCP=TLPM_files\FunctTLPM_getDHCP.html
IDH_TLPM_setHostname=TLPM_files\FunctTLPM_setHostname.html
IDH_TLPM_getHostname=TLPM_files\FunctTLPM_getHostname.html
IDH_TLPM_setWebPort=TLPM_files\FunctTLPM_setWebPort.html
IDH_TLPM_getWebPort=TLPM_files\FunctTLPM_getWebPort.html
IDH_TLPM_setEncryption=TLPM_files\FunctTLPM_setEncryption.html
IDH_TLPM_getEncryption=TLPM_files\FunctTLPM_getEncryption.html
IDH_TLPM_setSCPIPort=TLPM_files\FunctTLPM_setSCPIPort.html
IDH_TLPM_getSCPIPort=TLPM_files\FunctTLPM_getSCPIPort.html
IDH_TLPM_setDFUPort=TLPM_files\FunctTLPM_setDFUPort.html
IDH_TLPM_getDFUPort=TLPM_files\FunctTLPM_getDFUPort.html
IDH_TLPM_setLANPropagation=TLPM_files\FunctTLPM_setLANPropagation.html
IDH_TLPM_getLANPropagation=TLPM_files\FunctTLPM_getLANPropagation.html
IDH_TLPM_setEnableNetSearch=TLPM_files\FunctTLPM_setEnableNetSearch.html
IDH_TLPM_getEnableNetSearch=TLPM_files\FunctTLPM_getEnableNetSearch.html
IDH_TLPM_setNetSearchMask=TLPM_files\FunctTLPM_setNetSearchMask.html
IDH_TLPM_setInputFilterState=TLPM_files\FunctTLPM_setInputFilterState.html
IDH_TLPM_getInputFilterState=TLPM_files\FunctTLPM_getInputFilterState.html
IDH_TLPM_setAccelState=TLPM_files\FunctTLPM_setAccelState.html
IDH_TLPM_getAccelState=TLPM_files\FunctTLPM_getAccelState.html
IDH_TLPM_setAccelMode=TLPM_files\FunctTLPM_setAccelMode.html
IDH_TLPM_getAccelMode=TLPM_files\FunctTLPM_getAccelMode.html
IDH_TLPM_setAccelTau=TLPM_files\FunctTLPM_setAccelTau.html
IDH_TLPM_getAccelTau=TLPM_files\FunctTLPM_getAccelTau.html
IDH_TLPM_setInputAdapterType=TLPM_files\FunctTLPM_setInputAdapterType.html
IDH_TLPM_getInputAdapterType=TLPM_files\FunctTLPM_getInputAdapterType.html
IDH_TLPM_setAvgTime=TLPM_files\FunctTLPM_setAvgTime.html
IDH_TLPM_getAvgTime=TLPM_files\FunctTLPM_getAvgTime.html
IDH_TLPM_setAvgCnt=TLPM_files\FunctTLPM_setAvgCnt.html
IDH_TLPM_getAvgCnt=TLPM_files\FunctTLPM_getAvgCnt.html
IDH_TLPM_setAttenuation=TLPM_files\FunctTLPM_setAttenuation.html
IDH_TLPM_getAttenuation=TLPM_files\FunctTLPM_getAttenuation.html
IDH_TLPM_startDarkAdjust=TLPM_files\FunctTLPM_startDarkAdjust.html
IDH_TLPM_cancelDarkAdjust=TLPM_files\FunctTLPM_cancelDarkAdjust.html
IDH_TLPM_getDarkAdjustState=TLPM_files\FunctTLPM_getDarkAdjustState.html
IDH_TLPM_getDarkOffset=TLPM_files\FunctTLPM_getDarkOffset.html
IDH_TLPM_startZeroPos=TLPM_files\FunctTLPM_startZeroPos.html
IDH_TLPM_cancelZeroPos=TLPM_files\FunctTLPM_cancelZeroPos.html
IDH_TLPM_setZeroPos=TLPM_files\FunctTLPM_setZeroPos.html
IDH_TLPM_getZeroPos=TLPM_files\FunctTLPM_getZeroPos.html
IDH_TLPM_setBeamDia=TLPM_files\FunctTLPM_setBeamDia.html
IDH_TLPM_getBeamDia=TLPM_files\FunctTLPM_getBeamDia.html
IDH_TLPM_setWavelength=TLPM_files\FunctTLPM_setWavelength.html
IDH_TLPM_getWavelength=TLPM_files\FunctTLPM_getWavelength.html
IDH_TLPM_setPhotodiodeResponsivity=TLPM_files\FunctTLPM_setPhotodiodeResponsivity.html
IDH_TLPM_getPhotodiodeResponsivity=TLPM_files\FunctTLPM_getPhotodiodeResponsivity.html
IDH_TLPM_setThermopileResponsivity=TLPM_files\FunctTLPM_setThermopileResponsivity.html
IDH_TLPM_getThermopileResponsivity=TLPM_files\FunctTLPM_getThermopileResponsivity.html
IDH_TLPM_setPyrosensorResponsivity=TLPM_files\FunctTLPM_setPyrosensorResponsivity.html
IDH_TLPM_getPyrosensorResponsivity=TLPM_files\FunctTLPM_getPyrosensorResponsivity.html
IDH_TLPM_setCurrentAutoRange=TLPM_files\FunctTLPM_setCurrentAutoRange.html
IDH_TLPM_getCurrentAutorange=TLPM_files\FunctTLPM_getCurrentAutorange.html
IDH_TLPM_setCurrentRange=TLPM_files\FunctTLPM_setCurrentRange.html
IDH_TLPM_getCurrentRange=TLPM_files\FunctTLPM_getCurrentRange.html
IDH_TLPM_getCurrentRanges=TLPM_files\FunctTLPM_getCurrentRanges.html
IDH_TLPM_setCurrentRef=TLPM_files\FunctTLPM_setCurrentRef.html
IDH_TLPM_getCurrentRef=TLPM_files\FunctTLPM_getCurrentRef.html
IDH_TLPM_setCurrentRefState=TLPM_files\FunctTLPM_setCurrentRefState.html
IDH_TLPM_getCurrentRefState=TLPM_files\FunctTLPM_getCurrentRefState.html
IDH_TLPM_setEnergyRange=TLPM_files\FunctTLPM_setEnergyRange.html
IDH_TLPM_getEnergyRange=TLPM_files\FunctTLPM_getEnergyRange.html
IDH_TLPM_setEnergyRef=TLPM_files\FunctTLPM_setEnergyRef.html
IDH_TLPM_getEnergyRef=TLPM_files\FunctTLPM_getEnergyRef.html
IDH_TLPM_setEnergyRefState=TLPM_files\FunctTLPM_setEnergyRefState.html
IDH_TLPM_getEnergyRefState=TLPM_files\FunctTLPM_getEnergyRefState.html
IDH_TLPM_getFreqRange=TLPM_files\FunctTLPM_getFreqRange.html
IDH_TLPM_setFreqMode=TLPM_files\FunctTLPM_setFreqMode.html
IDH_TLPM_getFreqMode=TLPM_files\FunctTLPM_getFreqMode.html
IDH_TLPM_setPowerAutoRange=TLPM_files\FunctTLPM_setPowerAutoRange.html
IDH_TLPM_getPowerAutorange=TLPM_files\FunctTLPM_getPowerAutorange.html
IDH_TLPM_setPowerRange=TLPM_files\FunctTLPM_setPowerRange.html
IDH_TLPM_getPowerRange=TLPM_files\FunctTLPM_getPowerRange.html
IDH_TLPM_setPowerRef=TLPM_files\FunctTLPM_setPowerRef.html
IDH_TLPM_getPowerRef=TLPM_files\FunctTLPM_getPowerRef.html
IDH_TLPM_setPowerRefState=TLPM_files\FunctTLPM_setPowerRefState.html
IDH_TLPM_getPowerRefState=TLPM_files\FunctTLPM_getPowerRefState.html
IDH_TLPM_setPowerUnit=TLPM_files\FunctTLPM_setPowerUnit.html
IDH_TLPM_getPowerUnit=TLPM_files\FunctTLPM_getPowerUnit.html
IDH_TLPM_getPowerCalibrationPointsInformation=TLPM_files\FunctTLPM_getPowerCalibrationPointsInformation.html
IDH_TLPM_getPowerCalibrationPointsState=TLPM_files\FunctTLPM_getPowerCalibrationPointsState.html
IDH_TLPM_setPowerCalibrationPointsState=TLPM_files\FunctTLPM_setPowerCalibrationPointsState.html
IDH_TLPM_getPowerCalibrationPoints=TLPM_files\FunctTLPM_getPowerCalibrationPoints.html
IDH_TLPM_setPowerCalibrationPoints=TLPM_files\FunctTLPM_setPowerCalibrationPoints.html
IDH_TLPM_reinitSensor=TLPM_files\FunctTLPM_reinitSensor.html
IDH_TLPM_setVoltageAutoRange=TLPM_files\FunctTLPM_setVoltageAutoRange.html
IDH_TLPM_getVoltageAutorange=TLPM_files\FunctTLPM_getVoltageAutorange.html
IDH_TLPM_setVoltageRange=TLPM_files\FunctTLPM_setVoltageRange.html
IDH_TLPM_getVoltageRange=TLPM_files\FunctTLPM_getVoltageRange.html
IDH_TLPM_getVoltageRanges=TLPM_files\FunctTLPM_getVoltageRanges.html
IDH_TLPM_setVoltageRef=TLPM_files\FunctTLPM_setVoltageRef.html
IDH_TLPM_getVoltageRef=TLPM_files\FunctTLPM_getVoltageRef.html
IDH_TLPM_setVoltageRefState=TLPM_files\FunctTLPM_setVoltageRefState.html
IDH_TLPM_getVoltageRefState=TLPM_files\FunctTLPM_getVoltageRefState.html
IDH_TLPM_setPeakThreshold=TLPM_files\FunctTLPM_setPeakThreshold.html
IDH_TLPM_getPeakThreshold=TLPM_files\FunctTLPM_getPeakThreshold.html
IDH_TLPM_startPeakDetector=TLPM_files\FunctTLPM_startPeakDetector.html
IDH_TLPM_isPeakDetectorRunning=TLPM_files\FunctTLPM_isPeakDetectorRunning.html
IDH_TLPM_setPeakFilter=TLPM_files\FunctTLPM_setPeakFilter.html
IDH_TLPM_getPeakFilter=TLPM_files\FunctTLPM_getPeakFilter.html
IDH_TLPM_setExtNtcParameter=TLPM_files\FunctTLPM_setExtNtcParameter.html
IDH_TLPM_getExtNtcParameter=TLPM_files\FunctTLPM_getExtNtcParameter.html
IDH_TLPM_setFilterPosition=TLPM_files\FunctTLPM_setFilterPosition.html
IDH_TLPM_getFilterPosition=TLPM_files\FunctTLPM_getFilterPosition.html
IDH_TLPM_setFilterAutoMode=TLPM_files\FunctTLPM_setFilterAutoMode.html
IDH_TLPM_getFilterAutoMode=TLPM_files\FunctTLPM_getFilterAutoMode.html
IDH_TLPM_getAnalogOutputSlopeRange=TLPM_files\FunctTLPM_getAnalogOutputSlopeRange.html
IDH_TLPM_setAnalogOutputSlope=TLPM_files\FunctTLPM_setAnalogOutputSlope.html
IDH_TLPM_getAnalogOutputSlope=TLPM_files\FunctTLPM_getAnalogOutputSlope.html
IDH_TLPM_getAnalogOutputVoltageRange=TLPM_files\FunctTLPM_getAnalogOutputVoltageRange.html
IDH_TLPM_getAnalogOutputVoltage=TLPM_files\FunctTLPM_getAnalogOutputVoltage.html
IDH_TLPM_getAnalogOutputHub=TLPM_files\FunctTLPM_getAnalogOutputHub.html
IDH_TLPM_setAnalogOutputHub=TLPM_files\FunctTLPM_setAnalogOutputHub.html
IDH_TLPM_getPositionAnalogOutputSlopeRange=TLPM_files\FunctTLPM_getPositionAnalogOutputSlopeRange.html
IDH_TLPM_setPositionAnalogOutputSlope=TLPM_files\FunctTLPM_setPositionAnalogOutputSlope.html
IDH_TLPM_getPositionAnalogOutputSlope=TLPM_files\FunctTLPM_getPositionAnalogOutputSlope.html
IDH_TLPM_getPositionAnalogOutputVoltageRange=TLPM_files\FunctTLPM_getPositionAnalogOutputVoltageRange.html
IDH_TLPM_getPositionAnalogOutputVoltage=TLPM_files\FunctTLPM_getPositionAnalogOutputVoltage.html
IDH_TLPM_getMeasPinMode=TLPM_files\FunctTLPM_getMeasPinMode.html
IDH_TLPM_getMeasPinPowerLevel=TLPM_files\FunctTLPM_getMeasPinPowerLevel.html
IDH_TLPM_setMeasPinPowerLevel=TLPM_files\FunctTLPM_setMeasPinPowerLevel.html
IDH_TLPM_getMeasPinEnergyLevel=TLPM_files\FunctTLPM_getMeasPinEnergyLevel.html
IDH_TLPM_setMeasPinEnergyLevel=TLPM_files\FunctTLPM_setMeasPinEnergyLevel.html
IDH_TLPM_setNegativePulseWidth=TLPM_files\FunctTLPM_setNegativePulseWidth.html
IDH_TLPM_setPositivePulseWidth=TLPM_files\FunctTLPM_setPositivePulseWidth.html
IDH_TLPM_setNegativeDutyCycle=TLPM_files\FunctTLPM_setNegativeDutyCycle.html
IDH_TLPM_setPositiveDutyCycle=TLPM_files\FunctTLPM_setPositiveDutyCycle.html
IDH_TLPM_measCurrent=TLPM_files\FunctTLPM_measCurrent.html
IDH_TLPM_measVoltage=TLPM_files\FunctTLPM_measVoltage.html
IDH_TLPM_measPower=TLPM_files\FunctTLPM_measPower.html
IDH_TLPM_measEnergy=TLPM_files\FunctTLPM_measEnergy.html
IDH_TLPM_measPowerDens=TLPM_files\FunctTLPM_measPowerDens.html
IDH_TLPM_measEnergyDens=TLPM_files\FunctTLPM_measEnergyDens.html
IDH_TLPM_measFreq=TLPM_files\FunctTLPM_measFreq.html
IDH_TLPM_measAuxAD0=TLPM_files\FunctTLPM_measAuxAD0.html
IDH_TLPM_measAuxAD1=TLPM_files\FunctTLPM_measAuxAD1.html
IDH_TLPM_measHeadTemperature=TLPM_files\FunctTLPM_measHeadTemperature.html
IDH_TLPM_measEmmHumidity=TLPM_files\FunctTLPM_measEmmHumidity.html
IDH_TLPM_measEmmTemperature=TLPM_files\FunctTLPM_measEmmTemperature.html
IDH_TLPM_measExtNtcTemperature=TLPM_files\FunctTLPM_measExtNtcTemperature.html
IDH_TLPM_measExtNtcResistance=TLPM_files\FunctTLPM_measExtNtcResistance.html
IDH_TLPM_meas4QPositions=TLPM_files\FunctTLPM_meas4QPositions.html
IDH_TLPM_meas4QVoltages=TLPM_files\FunctTLPM_meas4QVoltages.html
IDH_TLPM_measNegPulseWidth=TLPM_files\FunctTLPM_measNegPulseWidth.html
IDH_TLPM_measPosPulseWidth=TLPM_files\FunctTLPM_measPosPulseWidth.html
IDH_TLPM_measNegDutyCycle=TLPM_files\FunctTLPM_measNegDutyCycle.html
IDH_TLPM_measPosDutyCycle=TLPM_files\FunctTLPM_measPosDutyCycle.html
IDH_TLPM_resetFastArrayMeasurement=TLPM_files\FunctTLPM_resetFastArrayMeasurement.html
IDH_TLPM_confPowerFastArrayMeasurement=TLPM_files\FunctTLPM_confPowerFastArrayMeasurement.html
IDH_TLPM_confCurrentFastArrayMeasurement=TLPM_files\FunctTLPM_confCurrentFastArrayMeasurement.html
IDH_TLPM_confVoltageFastArrayMeasurement=TLPM_files\FunctTLPM_confVoltageFastArrayMeasurement.html
IDH_TLPM_confPDensityFastArrayMeasurement=TLPM_files\FunctTLPM_confPDensityFastArrayMeasurement.html
IDH_TLPM_confEnergyFastArrayMeasurement=TLPM_files\FunctTLPM_confEnergyFastArrayMeasurement.html
IDH_TLPM_confEDensityFastArrayMeasurement=TLPM_files\FunctTLPM_confEDensityFastArrayMeasurement.html
IDH_TLPM_getNextFastArrayMeasurement=TLPM_files\FunctTLPM_getNextFastArrayMeasurement.html
IDH_TLPM_getFastMaxSamplerate=TLPM_files\FunctTLPM_getFastMaxSamplerate.html
IDH_TLPM_confPowerMeasurementSequence=TLPM_files\FunctTLPM_confPowerMeasurementSequence.html
IDH_TLPM_confPowerMeasurementSequenceHWTrigger=TLPM_files\FunctTLPM_confPowerMeasurementSequenceHWTrigger.html
IDH_TLPM_confCurrentMeasurementSequence=TLPM_files\FunctTLPM_confCurrentMeasurementSequence.html
IDH_TLPM_confCurrentMeasurementSequenceHWTrigger=TLPM_files\FunctTLPM_confCurrentMeasurementSequenceHWTrigger.html
IDH_TLPM_startMeasurementSequence=TLPM_files\FunctTLPM_startMeasurementSequence.html
IDH_TLPM_getMeasurementSequence=TLPM_files\FunctTLPM_getMeasurementSequence.html
IDH_TLPM_getMeasurementSequenceHWTrigger=TLPM_files\FunctTLPM_getMeasurementSequenceHWTrigger.html
IDH_TLPM_setDigIoDirection=TLPM_files\FunctTLPM_setDigIoDirection.html
IDH_TLPM_getDigIoDirection=TLPM_files\FunctTLPM_getDigIoDirection.html
IDH_TLPM_setDigIoOutput=TLPM_files\FunctTLPM_setDigIoOutput.html
IDH_TLPM_getDigIoOutput=TLPM_files\FunctTLPM_getDigIoOutput.html
IDH_TLPM_getDigIoPort=TLPM_files\FunctTLPM_getDigIoPort.html
IDH_TLPM_setDigIoPinMode=TLPM_files\FunctTLPM_setDigIoPinMode.html
IDH_TLPM_getDigIoPinMode=TLPM_files\FunctTLPM_getDigIoPinMode.html
IDH_TLPM_setDigIoPinOutput=TLPM_files\FunctTLPM_setDigIoPinOutput.html
IDH_TLPM_getDigIoPinOutput=TLPM_files\FunctTLPM_getDigIoPinOutput.html
IDH_TLPM_getDigIoPinInput=TLPM_files\FunctTLPM_getDigIoPinInput.html
IDH_TLPM_errorMessage=TLPM_files\FunctTLPM_errorMessage.html
IDH_TLPM_errorQuery=TLPM_files\FunctTLPM_errorQuery.html
IDH_TLPM_errorQueryMode=TLPM_files\FunctTLPM_errorQueryMode.html
IDH_TLPM_reset=TLPM_files\FunctTLPM_reset.html
IDH_TLPM_selfTest=TLPM_files\FunctTLPM_selfTest.html
IDH_TLPM_revisionQuery=TLPM_files\FunctTLPM_revisionQuery.html
IDH_TLPM_identificationQuery=TLPM_files\FunctTLPM_identificationQuery.html
IDH_TLPM_getCalibrationMsg=TLPM_files\FunctTLPM_getCalibrationMsg.html
IDH_TLPM_getSensorInfo=TLPM_files\FunctTLPM_getSensorInfo.html
IDH_TLPM_writeRaw=TLPM_files\FunctTLPM_writeRaw.html
IDH_TLPM_readRaw=TLPM_files\FunctTLPM_readRaw.html
IDH_TLPM_setTimeoutValue=TLPM_files\FunctTLPM_setTimeoutValue.html
IDH_TLPM_getTimeoutValue=TLPM_files\FunctTLPM_getTimeoutValue.html
IDH_TLPM_setDisplayName=TLPM_files\FunctTLPM_setDisplayName.html
IDH_TLPM_getDisplayName=TLPM_files\FunctTLPM_getDisplayName.html
IDH_TLPM_setDeviceBaudrate=TLPM_files\FunctTLPM_setDeviceBaudrate.html
IDH_TLPM_getDeviceBaudrate=TLPM_files\FunctTLPM_getDeviceBaudrate.html
IDH_TLPM_setDriverBaudrate=TLPM_files\FunctTLPM_setDriverBaudrate.html
IDH_TLPM_getDriverBaudrate=TLPM_files\FunctTLPM_getDriverBaudrate.html
IDH_TLPM_close=TLPM_files\FunctTLPM_close.html
IDH_TLPM_html_main_file=TLPM.html

[MAP]
#define IDH_TLPM_init 1
#define IDH_TLPM_findRsrc 2
#define IDH_TLPM_getRsrcName 3
#define IDH_TLPM_getRsrcInfo 4
#define IDH_TLPM_writeRegister 5
#define IDH_TLPM_readRegister 6
#define IDH_TLPM_presetRegister 7
#define IDH_TLPM_setTime 8
#define IDH_TLPM_getTime 9
#define IDH_TLPM_setLineFrequency 10
#define IDH_TLPM_getLineFrequency 11
#define IDH_TLPM_getBatteryVoltage 12
#define IDH_TLPM_setDispBrightness 13
#define IDH_TLPM_getDispBrightness 14
#define IDH_TLPM_setDispContrast 15
#define IDH_TLPM_getDispContrast 16
#define IDH_TLPM_setIPAddress 17
#define IDH_TLPM_getIPAddress 18
#define IDH_TLPM_setIPMask 19
#define IDH_TLPM_getIPMask 20
#define IDH_TLPM_getMACAddress 21
#define IDH_TLPM_setDHCP 22
#define IDH_TLPM_getDHCP 23
#define IDH_TLPM_setHostname 24
#define IDH_TLPM_getHostname 25
#define IDH_TLPM_setWebPort 26
#define IDH_TLPM_getWebPort 27
#define IDH_TLPM_setEncryption 28
#define IDH_TLPM_getEncryption 29
#define IDH_TLPM_setSCPIPort 30
#define IDH_TLPM_getSCPIPort 31
#define IDH_TLPM_setDFUPort 32
#define IDH_TLPM_getDFUPort 33
#define IDH_TLPM_setLANPropagation 34
#define IDH_TLPM_getLANPropagation 35
#define IDH_TLPM_setEnableNetSearch 36
#define IDH_TLPM_getEnableNetSearch 37
#define IDH_TLPM_setNetSearchMask 38
#define IDH_TLPM_setInputFilterState 39
#define IDH_TLPM_getInputFilterState 40
#define IDH_TLPM_setAccelState 41
#define IDH_TLPM_getAccelState 42
#define IDH_TLPM_setAccelMode 43
#define IDH_TLPM_getAccelMode 44
#define IDH_TLPM_setAccelTau 45
#define IDH_TLPM_getAccelTau 46
#define IDH_TLPM_setInputAdapterType 47
#define IDH_TLPM_getInputAdapterType 48
#define IDH_TLPM_setAvgTime 49
#define IDH_TLPM_getAvgTime 50
#define IDH_TLPM_setAvgCnt 51
#define IDH_TLPM_getAvgCnt 52
#define IDH_TLPM_setAttenuation 53
#define IDH_TLPM_getAttenuation 54
#define IDH_TLPM_startDarkAdjust 55
#define IDH_TLPM_cancelDarkAdjust 56
#define IDH_TLPM_getDarkAdjustState 57
#define IDH_TLPM_getDarkOffset 58
#define IDH_TLPM_startZeroPos 59
#define IDH_TLPM_cancelZeroPos 60
#define IDH_TLPM_setZeroPos 61
#define IDH_TLPM_getZeroPos 62
#define IDH_TLPM_setBeamDia 63
#define IDH_TLPM_getBeamDia 64
#define IDH_TLPM_setWavelength 65
#define IDH_TLPM_getWavelength 66
#define IDH_TLPM_setPhotodiodeResponsivity 67
#define IDH_TLPM_getPhotodiodeResponsivity 68
#define IDH_TLPM_setThermopileResponsivity 69
#define IDH_TLPM_getThermopileResponsivity 70
#define IDH_TLPM_setPyrosensorResponsivity 71
#define IDH_TLPM_getPyrosensorResponsivity 72
#define IDH_TLPM_setCurrentAutoRange 73
#define IDH_TLPM_getCurrentAutorange 74
#define IDH_TLPM_setCurrentRange 75
#define IDH_TLPM_getCurrentRange 76
#define IDH_TLPM_getCurrentRanges 77
#define IDH_TLPM_setCurrentRef 78
#define IDH_TLPM_getCurrentRef 79
#define IDH_TLPM_setCurrentRefState 80
#define IDH_TLPM_getCurrentRefState 81
#define IDH_TLPM_setEnergyRange 82
#define IDH_TLPM_getEnergyRange 83
#define IDH_TLPM_setEnergyRef 84
#define IDH_TLPM_getEnergyRef 85
#define IDH_TLPM_setEnergyRefState 86
#define IDH_TLPM_getEnergyRefState 87
#define IDH_TLPM_getFreqRange 88
#define IDH_TLPM_setFreqMode 89
#define IDH_TLPM_getFreqMode 90
#define IDH_TLPM_setPowerAutoRange 91
#define IDH_TLPM_getPowerAutorange 92
#define IDH_TLPM_setPowerRange 93
#define IDH_TLPM_getPowerRange 94
#define IDH_TLPM_setPowerRef 95
#define IDH_TLPM_getPowerRef 96
#define IDH_TLPM_setPowerRefState 97
#define IDH_TLPM_getPowerRefState 98
#define IDH_TLPM_setPowerUnit 99
#define IDH_TLPM_getPowerUnit 100
#define IDH_TLPM_getPowerCalibrationPointsInformation 101
#define IDH_TLPM_getPowerCalibrationPointsState 102
#define IDH_TLPM_setPowerCalibrationPointsState 103
#define IDH_TLPM_getPowerCalibrationPoints 104
#define IDH_TLPM_setPowerCalibrationPoints 105
#define IDH_TLPM_reinitSensor 106
#define IDH_TLPM_setVoltageAutoRange 107
#define IDH_TLPM_getVoltageAutorange 108
#define IDH_TLPM_setVoltageRange 109
#define IDH_TLPM_getVoltageRange 110
#define IDH_TLPM_getVoltageRanges 111
#define IDH_TLPM_setVoltageRef 112
#define IDH_TLPM_getVoltageRef 113
#define IDH_TLPM_setVoltageRefState 114
#define IDH_TLPM_getVoltageRefState 115
#define IDH_TLPM_setPeakThreshold 116
#define IDH_TLPM_getPeakThreshold 117
#define IDH_TLPM_startPeakDetector 118
#define IDH_TLPM_isPeakDetectorRunning 119
#define IDH_TLPM_setPeakFilter 120
#define IDH_TLPM_getPeakFilter 121
#define IDH_TLPM_setExtNtcParameter 122
#define IDH_TLPM_getExtNtcParameter 123
#define IDH_TLPM_setFilterPosition 124
#define IDH_TLPM_getFilterPosition 125
#define IDH_TLPM_setFilterAutoMode 126
#define IDH_TLPM_getFilterAutoMode 127
#define IDH_TLPM_getAnalogOutputSlopeRange 128
#define IDH_TLPM_setAnalogOutputSlope 129
#define IDH_TLPM_getAnalogOutputSlope 130
#define IDH_TLPM_getAnalogOutputVoltageRange 131
#define IDH_TLPM_getAnalogOutputVoltage 132
#define IDH_TLPM_getAnalogOutputHub 133
#define IDH_TLPM_setAnalogOutputHub 134
#define IDH_TLPM_getPositionAnalogOutputSlopeRange 135
#define IDH_TLPM_setPositionAnalogOutputSlope 136
#define IDH_TLPM_getPositionAnalogOutputSlope 137
#define IDH_TLPM_getPositionAnalogOutputVoltageRange 138
#define IDH_TLPM_getPositionAnalogOutputVoltage 139
#define IDH_TLPM_getMeasPinMode 140
#define IDH_TLPM_getMeasPinPowerLevel 141
#define IDH_TLPM_setMeasPinPowerLevel 142
#define IDH_TLPM_getMeasPinEnergyLevel 143
#define IDH_TLPM_setMeasPinEnergyLevel 144
#define IDH_TLPM_setNegativePulseWidth 145
#define IDH_TLPM_setPositivePulseWidth 146
#define IDH_TLPM_setNegativeDutyCycle 147
#define IDH_TLPM_setPositiveDutyCycle 148
#define IDH_TLPM_measCurrent 149
#define IDH_TLPM_measVoltage 150
#define IDH_TLPM_measPower 151
#define IDH_TLPM_measEnergy 152
#define IDH_TLPM_measPowerDens 153
#define IDH_TLPM_measEnergyDens 154
#define IDH_TLPM_measFreq 155
#define IDH_TLPM_measAuxAD0 156
#define IDH_TLPM_measAuxAD1 157
#define IDH_TLPM_measHeadTemperature 158
#define IDH_TLPM_measEmmHumidity 159
#define IDH_TLPM_measEmmTemperature 160
#define IDH_TLPM_measExtNtcTemperature 161
#define IDH_TLPM_measExtNtcResistance 162
#define IDH_TLPM_meas4QPositions 163
#define IDH_TLPM_meas4QVoltages 164
#define IDH_TLPM_measNegPulseWidth 165
#define IDH_TLPM_measPosPulseWidth 166
#define IDH_TLPM_measNegDutyCycle 167
#define IDH_TLPM_measPosDutyCycle 168
#define IDH_TLPM_resetFastArrayMeasurement 169
#define IDH_TLPM_confPowerFastArrayMeasurement 170
#define IDH_TLPM_confCurrentFastArrayMeasurement 171
#define IDH_TLPM_confVoltageFastArrayMeasurement 172
#define IDH_TLPM_confPDensityFastArrayMeasurement 173
#define IDH_TLPM_confEnergyFastArrayMeasurement 174
#define IDH_TLPM_confEDensityFastArrayMeasurement 175
#define IDH_TLPM_getNextFastArrayMeasurement 176
#define IDH_TLPM_getFastMaxSamplerate 177
#define IDH_TLPM_confPowerMeasurementSequence 178
#define IDH_TLPM_confPowerMeasurementSequenceHWTrigger 179
#define IDH_TLPM_confCurrentMeasurementSequence 180
#define IDH_TLPM_confCurrentMeasurementSequenceHWTrigger 181
#define IDH_TLPM_startMeasurementSequence 182
#define IDH_TLPM_getMeasurementSequence 183
#define IDH_TLPM_getMeasurementSequenceHWTrigger 184
#define IDH_TLPM_setDigIoDirection 185
#define IDH_TLPM_getDigIoDirection 186
#define IDH_TLPM_setDigIoOutput 187
#define IDH_TLPM_getDigIoOutput 188
#define IDH_TLPM_getDigIoPort 189
#define IDH_TLPM_setDigIoPinMode 190
#define IDH_TLPM_getDigIoPinMode 191
#define IDH_TLPM_setDigIoPinOutput 192
#define IDH_TLPM_getDigIoPinOutput 193
#define IDH_TLPM_getDigIoPinInput 194
#define IDH_TLPM_errorMessage 195
#define IDH_TLPM_errorQuery 196
#define IDH_TLPM_errorQueryMode 197
#define IDH_TLPM_reset 198
#define IDH_TLPM_selfTest 199
#define IDH_TLPM_revisionQuery 200
#define IDH_TLPM_identificationQuery 201
#define IDH_TLPM_getCalibrationMsg 202
#define IDH_TLPM_getSensorInfo 203
#define IDH_TLPM_writeRaw 204
#define IDH_TLPM_readRaw 205
#define IDH_TLPM_setTimeoutValue 206
#define IDH_TLPM_getTimeoutValue 207
#define IDH_TLPM_setDisplayName 208
#define IDH_TLPM_getDisplayName 209
#define IDH_TLPM_setDeviceBaudrate 210
#define IDH_TLPM_getDeviceBaudrate 211
#define IDH_TLPM_setDriverBaudrate 212
#define IDH_TLPM_getDriverBaudrate 213
#define IDH_TLPM_close 214
#define IDH_TLPM_html_main_file 215

[FILES]
TLPM.html
TLPM_files\Alphabetical Function Index.html
TLPM_files\Hierarchical Function Index.html
TLPM_files\ClassResource Functions.html
TLPM_files\ClassStatus Register.html
TLPM_files\ClassSystem.html
TLPM_files\ClassDate Time.html
TLPM_files\ClassLine Frequency.html
TLPM_files\ClassBattery.html
TLPM_files\ClassDisplay.html
TLPM_files\ClassEthernet Interface.html
TLPM_files\ClassMeasure.html
TLPM_files\ClassConfigure.html
TLPM_files\ClassInput.html
TLPM_files\ClassAverage.html
TLPM_files\ClassCorrection.html
TLPM_files\ClassCurrent Measurement.html
TLPM_files\ClassEnergy Measurement.html
TLPM_files\ClassFrequency Measurement.html
TLPM_files\ClassPower Measurement.html
TLPM_files\ClassUser Power Calibration.html
TLPM_files\ClassVoltage Measurement.html
TLPM_files\ClassPeak Detector.html
TLPM_files\ClassExternal Temperature Sensor.html
TLPM_files\ClassFilter Position.html
TLPM_files\ClassAnalog Output.html
TLPM_files\ClassPosition Analog Output.html
TLPM_files\ClassMeasurement Pin.html
TLPM_files\ClassPulse Measurement.html
TLPM_files\ClassRead.html
TLPM_files\ClassArray Measurement.html
TLPM_files\ClassDigital I/O.html
TLPM_files\ClassDigital I/O PM103.html
TLPM_files\ClassUtility Functions.html
TLPM_files\ClassRaw I/O.html
TLPM_files\ClassTimeout.html
TLPM_files\ClassAttributes.html
TLPM_files\FunctTLPM_init.html
TLPM_files\FunctTLPM_findRsrc.html
TLPM_files\FunctTLPM_getRsrcName.html
TLPM_files\FunctTLPM_getRsrcInfo.html
TLPM_files\FunctTLPM_writeRegister.html
TLPM_files\FunctTLPM_readRegister.html
TLPM_files\FunctTLPM_presetRegister.html
TLPM_files\FunctTLPM_setTime.html
TLPM_files\FunctTLPM_getTime.html
TLPM_files\FunctTLPM_setLineFrequency.html
TLPM_files\FunctTLPM_getLineFrequency.html
TLPM_files\FunctTLPM_getBatteryVoltage.html
TLPM_files\FunctTLPM_setDispBrightness.html
TLPM_files\FunctTLPM_getDispBrightness.html
TLPM_files\FunctTLPM_setDispContrast.html
TLPM_files\FunctTLPM_getDispContrast.html
TLPM_files\FunctTLPM_setIPAddress.html
TLPM_files\FunctTLPM_getIPAddress.html
TLPM_files\FunctTLPM_setIPMask.html
TLPM_files\FunctTLPM_getIPMask.html
TLPM_files\FunctTLPM_getMACAddress.html
TLPM_files\FunctTLPM_setDHCP.html
TLPM_files\FunctTLPM_getDHCP.html
TLPM_files\FunctTLPM_setHostname.html
TLPM_files\FunctTLPM_getHostname.html
TLPM_files\FunctTLPM_setWebPort.html
TLPM_files\FunctTLPM_getWebPort.html
TLPM_files\FunctTLPM_setEncryption.html
TLPM_files\FunctTLPM_getEncryption.html
TLPM_files\FunctTLPM_setSCPIPort.html
TLPM_files\FunctTLPM_getSCPIPort.html
TLPM_files\FunctTLPM_setDFUPort.html
TLPM_files\FunctTLPM_getDFUPort.html
TLPM_files\FunctTLPM_setLANPropagation.html
TLPM_files\FunctTLPM_getLANPropagation.html
TLPM_files\FunctTLPM_setEnableNetSearch.html
TLPM_files\FunctTLPM_getEnableNetSearch.html
TLPM_files\FunctTLPM_setNetSearchMask.html
TLPM_files\FunctTLPM_setInputFilterState.html
TLPM_files\FunctTLPM_getInputFilterState.html
TLPM_files\FunctTLPM_setAccelState.html
TLPM_files\FunctTLPM_getAccelState.html
TLPM_files\FunctTLPM_setAccelMode.html
TLPM_files\FunctTLPM_getAccelMode.html
TLPM_files\FunctTLPM_setAccelTau.html
TLPM_files\FunctTLPM_getAccelTau.html
TLPM_files\FunctTLPM_setInputAdapterType.html
TLPM_files\FunctTLPM_getInputAdapterType.html
TLPM_files\FunctTLPM_setAvgTime.html
TLPM_files\FunctTLPM_getAvgTime.html
TLPM_files\FunctTLPM_setAvgCnt.html
TLPM_files\FunctTLPM_getAvgCnt.html
TLPM_files\FunctTLPM_setAttenuation.html
TLPM_files\FunctTLPM_getAttenuation.html
TLPM_files\FunctTLPM_startDarkAdjust.html
TLPM_files\FunctTLPM_cancelDarkAdjust.html
TLPM_files\FunctTLPM_getDarkAdjustState.html
TLPM_files\FunctTLPM_getDarkOffset.html
TLPM_files\FunctTLPM_startZeroPos.html
TLPM_files\FunctTLPM_cancelZeroPos.html
TLPM_files\FunctTLPM_setZeroPos.html
TLPM_files\FunctTLPM_getZeroPos.html
TLPM_files\FunctTLPM_setBeamDia.html
TLPM_files\FunctTLPM_getBeamDia.html
TLPM_files\FunctTLPM_setWavelength.html
TLPM_files\FunctTLPM_getWavelength.html
TLPM_files\FunctTLPM_setPhotodiodeResponsivity.html
TLPM_files\FunctTLPM_getPhotodiodeResponsivity.html
TLPM_files\FunctTLPM_setThermopileResponsivity.html
TLPM_files\FunctTLPM_getThermopileResponsivity.html
TLPM_files\FunctTLPM_setPyrosensorResponsivity.html
TLPM_files\FunctTLPM_getPyrosensorResponsivity.html
TLPM_files\FunctTLPM_setCurrentAutoRange.html
TLPM_files\FunctTLPM_getCurrentAutorange.html
TLPM_files\FunctTLPM_setCurrentRange.html
TLPM_files\FunctTLPM_getCurrentRange.html
TLPM_files\FunctTLPM_getCurrentRanges.html
TLPM_files\FunctTLPM_setCurrentRef.html
TLPM_files\FunctTLPM_getCurrentRef.html
TLPM_files\FunctTLPM_setCurrentRefState.html
TLPM_files\FunctTLPM_getCurrentRefState.html
TLPM_files\FunctTLPM_setEnergyRange.html
TLPM_files\FunctTLPM_getEnergyRange.html
TLPM_files\FunctTLPM_setEnergyRef.html
TLPM_files\FunctTLPM_getEnergyRef.html
TLPM_files\FunctTLPM_setEnergyRefState.html
TLPM_files\FunctTLPM_getEnergyRefState.html
TLPM_files\FunctTLPM_getFreqRange.html
TLPM_files\FunctTLPM_setFreqMode.html
TLPM_files\FunctTLPM_getFreqMode.html
TLPM_files\FunctTLPM_setPowerAutoRange.html
TLPM_files\FunctTLPM_getPowerAutorange.html
TLPM_files\FunctTLPM_setPowerRange.html
TLPM_files\FunctTLPM_getPowerRange.html
TLPM_files\FunctTLPM_setPowerRef.html
TLPM_files\FunctTLPM_getPowerRef.html
TLPM_files\FunctTLPM_setPowerRefState.html
TLPM_files\FunctTLPM_getPowerRefState.html
TLPM_files\FunctTLPM_setPowerUnit.html
TLPM_files\FunctTLPM_getPowerUnit.html
TLPM_files\FunctTLPM_getPowerCalibrationPointsInformation.html
TLPM_files\FunctTLPM_getPowerCalibrationPointsState.html
TLPM_files\FunctTLPM_setPowerCalibrationPointsState.html
TLPM_files\FunctTLPM_getPowerCalibrationPoints.html
TLPM_files\FunctTLPM_setPowerCalibrationPoints.html
TLPM_files\FunctTLPM_reinitSensor.html
TLPM_files\FunctTLPM_setVoltageAutoRange.html
TLPM_files\FunctTLPM_getVoltageAutorange.html
TLPM_files\FunctTLPM_setVoltageRange.html
TLPM_files\FunctTLPM_getVoltageRange.html
TLPM_files\FunctTLPM_getVoltageRanges.html
TLPM_files\FunctTLPM_setVoltageRef.html
TLPM_files\FunctTLPM_getVoltageRef.html
TLPM_files\FunctTLPM_setVoltageRefState.html
TLPM_files\FunctTLPM_getVoltageRefState.html
TLPM_files\FunctTLPM_setPeakThreshold.html
TLPM_files\FunctTLPM_getPeakThreshold.html
TLPM_files\FunctTLPM_startPeakDetector.html
TLPM_files\FunctTLPM_isPeakDetectorRunning.html
TLPM_files\FunctTLPM_setPeakFilter.html
TLPM_files\FunctTLPM_getPeakFilter.html
TLPM_files\FunctTLPM_setExtNtcParameter.html
TLPM_files\FunctTLPM_getExtNtcParameter.html
TLPM_files\FunctTLPM_setFilterPosition.html
TLPM_files\FunctTLPM_getFilterPosition.html
TLPM_files\FunctTLPM_setFilterAutoMode.html
TLPM_files\FunctTLPM_getFilterAutoMode.html
TLPM_files\FunctTLPM_getAnalogOutputSlopeRange.html
TLPM_files\FunctTLPM_setAnalogOutputSlope.html
TLPM_files\FunctTLPM_getAnalogOutputSlope.html
TLPM_files\FunctTLPM_getAnalogOutputVoltageRange.html
TLPM_files\FunctTLPM_getAnalogOutputVoltage.html
TLPM_files\FunctTLPM_getAnalogOutputHub.html
TLPM_files\FunctTLPM_setAnalogOutputHub.html
TLPM_files\FunctTLPM_getPositionAnalogOutputSlopeRange.html
TLPM_files\FunctTLPM_setPositionAnalogOutputSlope.html
TLPM_files\FunctTLPM_getPositionAnalogOutputSlope.html
TLPM_files\FunctTLPM_getPositionAnalogOutputVoltageRange.html
TLPM_files\FunctTLPM_getPositionAnalogOutputVoltage.html
TLPM_files\FunctTLPM_getMeasPinMode.html
TLPM_files\FunctTLPM_getMeasPinPowerLevel.html
TLPM_files\FunctTLPM_setMeasPinPowerLevel.html
TLPM_files\FunctTLPM_getMeasPinEnergyLevel.html
TLPM_files\FunctTLPM_setMeasPinEnergyLevel.html
TLPM_files\FunctTLPM_setNegativePulseWidth.html
TLPM_files\FunctTLPM_setPositivePulseWidth.html
TLPM_files\FunctTLPM_setNegativeDutyCycle.html
TLPM_files\FunctTLPM_setPositiveDutyCycle.html
TLPM_files\FunctTLPM_measCurrent.html
TLPM_files\FunctTLPM_measVoltage.html
TLPM_files\FunctTLPM_measPower.html
TLPM_files\FunctTLPM_measEnergy.html
TLPM_files\FunctTLPM_measPowerDens.html
TLPM_files\FunctTLPM_measEnergyDens.html
TLPM_files\FunctTLPM_measFreq.html
TLPM_files\FunctTLPM_measAuxAD0.html
TLPM_files\FunctTLPM_measAuxAD1.html
TLPM_files\FunctTLPM_measHeadTemperature.html
TLPM_files\FunctTLPM_measEmmHumidity.html
TLPM_files\FunctTLPM_measEmmTemperature.html
TLPM_files\FunctTLPM_measExtNtcTemperature.html
TLPM_files\FunctTLPM_measExtNtcResistance.html
TLPM_files\FunctTLPM_meas4QPositions.html
TLPM_files\FunctTLPM_meas4QVoltages.html
TLPM_files\FunctTLPM_measNegPulseWidth.html
TLPM_files\FunctTLPM_measPosPulseWidth.html
TLPM_files\FunctTLPM_measNegDutyCycle.html
TLPM_files\FunctTLPM_measPosDutyCycle.html
TLPM_files\FunctTLPM_resetFastArrayMeasurement.html
TLPM_files\FunctTLPM_confPowerFastArrayMeasurement.html
TLPM_files\FunctTLPM_confCurrentFastArrayMeasurement.html
TLPM_files\FunctTLPM_confVoltageFastArrayMeasurement.html
TLPM_files\FunctTLPM_confPDensityFastArrayMeasurement.html
TLPM_files\FunctTLPM_confEnergyFastArrayMeasurement.html
TLPM_files\FunctTLPM_confEDensityFastArrayMeasurement.html
TLPM_files\FunctTLPM_getNextFastArrayMeasurement.html
TLPM_files\FunctTLPM_getFastMaxSamplerate.html
TLPM_files\FunctTLPM_confPowerMeasurementSequence.html
TLPM_files\FunctTLPM_confPowerMeasurementSequenceHWTrigger.html
TLPM_files\FunctTLPM_confCurrentMeasurementSequence.html
TLPM_files\FunctTLPM_confCurrentMeasurementSequenceHWTrigger.html
TLPM_files\FunctTLPM_startMeasurementSequence.html
TLPM_files\FunctTLPM_getMeasurementSequence.html
TLPM_files\FunctTLPM_getMeasurementSequenceHWTrigger.html
TLPM_files\FunctTLPM_setDigIoDirection.html
TLPM_files\FunctTLPM_getDigIoDirection.html
TLPM_files\FunctTLPM_setDigIoOutput.html
TLPM_files\FunctTLPM_getDigIoOutput.html
TLPM_files\FunctTLPM_getDigIoPort.html
TLPM_files\FunctTLPM_setDigIoPinMode.html
TLPM_files\FunctTLPM_getDigIoPinMode.html
TLPM_files\FunctTLPM_setDigIoPinOutput.html
TLPM_files\FunctTLPM_getDigIoPinOutput.html
TLPM_files\FunctTLPM_getDigIoPinInput.html
TLPM_files\FunctTLPM_errorMessage.html
TLPM_files\FunctTLPM_errorQuery.html
TLPM_files\FunctTLPM_errorQueryMode.html
TLPM_files\FunctTLPM_reset.html
TLPM_files\FunctTLPM_selfTest.html
TLPM_files\FunctTLPM_revisionQuery.html
TLPM_files\FunctTLPM_identificationQuery.html
TLPM_files\FunctTLPM_getCalibrationMsg.html
TLPM_files\FunctTLPM_getSensorInfo.html
TLPM_files\FunctTLPM_writeRaw.html
TLPM_files\FunctTLPM_readRaw.html
TLPM_files\FunctTLPM_setTimeoutValue.html
TLPM_files\FunctTLPM_getTimeoutValue.html
TLPM_files\FunctTLPM_setDisplayName.html
TLPM_files\FunctTLPM_getDisplayName.html
TLPM_files\FunctTLPM_setDeviceBaudrate.html
TLPM_files\FunctTLPM_getDeviceBaudrate.html
TLPM_files\FunctTLPM_setDriverBaudrate.html
TLPM_files\FunctTLPM_getDriverBaudrate.html
TLPM_files\FunctTLPM_close.html
TLPM_files\htmlhelp.css
