<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=ISO-8859-1">
    <link rel="stylesheet" href="htmlhelp.css" type="text/css">
    <title>TLPM_getNextFastArrayMeasurement</title>
  </head>
  <body>
    <h1>TLPM_getNextFastArrayMeasurement</h1>
    <p class="syntax">
      ViStatus TLPM_getNextFastArrayMeasurement (ViSession instrumentHandle,
                                           ViPUInt16 count,
                                           ViUInt32 _VI_FAR timestamps[],
                                           ViReal64 _VI_FAR measData[]);
    </p>
    <h2 class="purpose">Purpose</h2>
    <p class="body">
    This function is used to obtain measurements from the instrument.
    <br/>
    The result are timestamp - value pairs.
    <br/>
    Remark:
    <br/>
    This function starts a new measurement cycle and after finishing measurement the result is received. Subject to the actual Average Count this may take up to seconds.
    </p>
    <h2>Parameters</h2>
    <table class="borderless" summary="Parameter help">
      <tr>
        <td valign="top"><em class="label">Input</em></td>
      </tr>
      <tr>
        <td class="tablehead">Name</td>
        <td class="tablehead">Type</td>
        <td class="tablehead">Description</td>
      </tr>
      <tr>
        <td class="paramName">instrumentHandle</td>
        <td class="paramDataType">ViSession</td>
        <td>
    This parameter accepts the instrument handle returned by &lt;Initialize&gt; to select the desired instrument driver session.
        </td>
      </tr>
      <tr>
        <td valign="top"><em class="label">Output</em></td>
      </tr>
      <tr>
        <td class="tablehead">Name</td>
        <td class="tablehead">Type</td>
        <td class="tablehead">Description</td>
      </tr>
      <tr>
        <td class="paramName">count</td>
        <td class="paramDataType">ViUInt16 (passed by reference)</td>
        <td>
    The count of timestamp - measurement value pairs
    <br/>
    The value will be 200
        </td>
      </tr>
      <tr>
        <td class="paramName">timestamps</td>
        <td class="paramDataType">ViUInt32[]</td>
        <td>
    Buffer containing up to 200 timestamps.
    <br/>
    This are raw timestamps and are NOT in ms.
        </td>
      </tr>
      <tr>
        <td class="paramName">measData</td>
        <td class="paramDataType">ViReal64[]</td>
        <td>
    Buffer containing up to 200 measurement values.
        </td>
      </tr>
    </table>
    <h2>Return Value</h2>
    <table class="borderless" summary="Return value help">
      <tr>
        <td class="tablehead">Name</td>
        <td class="tablehead">Type</td>
        <td class="tablehead">Description</td>
      </tr>
      <tr>
        <td class="paramName">status</td>
        <td class="paramDataType">ViStatus</td>
        <td>
    This is the error code returned by the function call. For error codes and descriptions see &lt;Error Message&gt;.
        </td>
      </tr>
    </table>
    <h2>References</h2>
    <p class="body">
      <a href="ClassArray%20Measurement.html">Array Measurement</a><br/>
      <a href="ClassMeasure.html">Measure</a><br/>
      <a href="Thorlabs%20Power%20Meter%20Instrument%20Driver.html">Thorlabs Power Meter Instrument Driver</a><br/>
      <a href="Alphabetical%20Function%20Index.html">Alphabetical Function Index</a><br/>
      <a href="Hierarchical%20Function%20Index.html">Hierarchical Function Index</a><br/>
    </p>
  </body>
</html>