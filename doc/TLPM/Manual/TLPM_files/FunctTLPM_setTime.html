<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=ISO-8859-1">
    <link rel="stylesheet" href="htmlhelp.css" type="text/css">
    <title>TLPM_setTime</title>
  </head>
  <body>
    <h1>TLPM_setTime</h1>
    <p class="syntax">
      ViStatus TLPM_setTime (ViSession instrumentHandle, ViInt16 year,
                       ViInt16 month, ViInt16 day, ViInt16 hour,
                       ViInt16 minute, ViInt16 second);
    </p>
    <h2 class="purpose">Purpose</h2>
    <p class="body">
    This function sets the system date and time of the powermeter.
    <br/>
    Notes:
    <br/>
    (1) Date and time are displayed on instruments screen and are used as timestamp for data saved to memory card.
    <br/>
    (2) The function is only available on PM100D, PM200, PM400.
    </p>
    <h2>Parameters</h2>
    <table class="borderless" summary="Parameter help">
      <tr>
        <td valign="top"><em class="label">Input</em></td>
      </tr>
      <tr>
        <td class="tablehead">Name</td>
        <td class="tablehead">Type</td>
        <td class="tablehead">Description</td>
      </tr>
      <tr>
        <td class="paramName">instrumentHandle</td>
        <td class="paramDataType">ViSession</td>
        <td>
    This parameter accepts the instrument handle returned by &lt;Initialize&gt; to select the desired instrument driver session.
        </td>
      </tr>
      <tr>
        <td class="paramName">year</td>
        <td class="paramDataType">ViInt16</td>
        <td>
    This parameter specifies the actual year in the format yyyy e.g. 2009.
        </td>
      </tr>
      <tr>
        <td class="paramName">month</td>
        <td class="paramDataType">ViInt16</td>
        <td>
    This parameter specifies the actual month in the format mm e.g. 01.
        </td>
      </tr>
      <tr>
        <td class="paramName">day</td>
        <td class="paramDataType">ViInt16</td>
        <td>
    This parameter specifies the actual day in the format dd e.g. 15.
        </td>
      </tr>
      <tr>
        <td class="paramName">hour</td>
        <td class="paramDataType">ViInt16</td>
        <td>
    This parameter specifies the actual hour in the format hh e.g. 14.
        </td>
      </tr>
      <tr>
        <td class="paramName">minute</td>
        <td class="paramDataType">ViInt16</td>
        <td>
    This parameter specifies the actual minute in the format mm e.g. 43.
        </td>
      </tr>
      <tr>
        <td class="paramName">second</td>
        <td class="paramDataType">ViInt16</td>
        <td>
    This parameter specifies the actual second in the format ss e.g. 50.
        </td>
      </tr>
    </table>
    <h2>Return Value</h2>
    <table class="borderless" summary="Return value help">
      <tr>
        <td class="tablehead">Name</td>
        <td class="tablehead">Type</td>
        <td class="tablehead">Description</td>
      </tr>
      <tr>
        <td class="paramName">status</td>
        <td class="paramDataType">ViStatus</td>
        <td>
    This is the error code returned by the function call. For error codes and descriptions see &lt;Error Message&gt;.
        </td>
      </tr>
    </table>
    <h2>References</h2>
    <p class="body">
      <a href="ClassDate%20Time.html">Date Time</a><br/>
      <a href="ClassSystem.html">System</a><br/>
      <a href="Thorlabs%20Power%20Meter%20Instrument%20Driver.html">Thorlabs Power Meter Instrument Driver</a><br/>
      <a href="Alphabetical%20Function%20Index.html">Alphabetical Function Index</a><br/>
      <a href="Hierarchical%20Function%20Index.html">Hierarchical Function Index</a><br/>
    </p>
  </body>
</html>