<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=ISO-8859-1">
    <link rel="stylesheet" href="htmlhelp.css" type="text/css">
    <title>TLPM_getMeasurementSequenceHWTrigger</title>
  </head>
  <body>
    <h1>TLPM_getMeasurementSequenceHWTrigger</h1>
    <p class="syntax">
      ViStatus TLPM_getMeasurementSequenceHWTrigger (ViSession instrumentHandle,
                                               ViUInt32 baseTime,
                                               float timeStamps[],
                                               float values[]);
    </p>
    <h2 class="purpose">Purpose</h2>
    <p class="body">
    Should be called if the method confPowerMeasurementSequenceHWTrigger and startMeasurementSequence were called first, (or confCurrentMeasurementSequenceHWTrigger and startMeasurementSequence)
    <br/>
    PM103:
    <br/>
    This function fills the given array with (100 * baseTime) measurements from the device, external triggered.
    <br/>
    Set the bandwidth to high(setInputFilterState to OFF) and disable auto ranging(setPowerAutoRange to OFF)
    <br/>
    PM101 special:
    <br/>
    This function fills the Values array with measurements from the device, external triggered.
    <br/>
    The size of measurements to set in the array is in the parameter Base Time. Base Time is equal to the time of measurement through the intervall between each measurement. These parameters are set in the method confPowerMeasurementSequenceHWTrigger.
    <br/>
    Note: The function is only available on PM103 and PM101 special (Not HWT).
    </p>
    <h2>Parameters</h2>
    <table class="borderless" summary="Parameter help">
      <tr>
        <td valign="top"><em class="label">Input</em></td>
      </tr>
      <tr>
        <td class="tablehead">Name</td>
        <td class="tablehead">Type</td>
        <td class="tablehead">Description</td>
      </tr>
      <tr>
        <td class="paramName">instrumentHandle</td>
        <td class="paramDataType">ViSession</td>
        <td>
    This parameter accepts the instrument handle returned by &lt;Initialize&gt; to select the desired instrument driver session.
        </td>
      </tr>
      <tr>
        <td class="paramName">baseTime</td>
        <td class="paramDataType">ViUInt32</td>
        <td>
    PM103:
    <br/>
    The amount of samples to collect in the internal interation of the method. The value can be from 1 to 100.
    <br/>
    PM101:
    <br/>
    Size of measuremnts to collect from the PM101. Time of measurement / intervall.
        </td>
      </tr>
      <tr>
        <td valign="top"><em class="label">Output</em></td>
      </tr>
      <tr>
        <td class="tablehead">Name</td>
        <td class="tablehead">Type</td>
        <td class="tablehead">Description</td>
      </tr>
      <tr>
        <td class="paramName">timeStamps</td>
        <td class="paramDataType">float []</td>
        <td>
    PM103:
    <br/>
    Array of time stamps in ms. The size of this array is 100 * baseTime.
    <br/>
    PM101 special:
    <br/>
    Not used.
        </td>
      </tr>
      <tr>
        <td class="paramName">values</td>
        <td class="paramDataType">float []</td>
        <td>
    PM103:
    <br/>
    Array of power/current measurements. The size of this array is 100 * baseTime.
    <br/>
    PM101:
    <br/>
    Array of power/current measurements. The size of this array is the time of measurement through the interval.
        </td>
      </tr>
    </table>
    <h2>Return Value</h2>
    <table class="borderless" summary="Return value help">
      <tr>
        <td class="tablehead">Name</td>
        <td class="tablehead">Type</td>
        <td class="tablehead">Description</td>
      </tr>
      <tr>
        <td class="paramName">status</td>
        <td class="paramDataType">ViStatus</td>
        <td>
    This is the error code returned by the function call. For error codes and descriptions see &lt;Error Message&gt;.
        </td>
      </tr>
    </table>
    <h2>References</h2>
    <p class="body">
      <a href="ClassArray%20Measurement.html">Array Measurement</a><br/>
      <a href="ClassMeasure.html">Measure</a><br/>
      <a href="Thorlabs%20Power%20Meter%20Instrument%20Driver.html">Thorlabs Power Meter Instrument Driver</a><br/>
      <a href="Alphabetical%20Function%20Index.html">Alphabetical Function Index</a><br/>
      <a href="Hierarchical%20Function%20Index.html">Hierarchical Function Index</a><br/>
    </p>
  </body>
</html>