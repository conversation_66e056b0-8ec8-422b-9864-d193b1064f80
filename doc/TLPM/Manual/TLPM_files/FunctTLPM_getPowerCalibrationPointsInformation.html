<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=ISO-8859-1">
    <link rel="stylesheet" href="htmlhelp.css" type="text/css">
    <title>TLPM_getPowerCalibrationPointsInformation</title>
  </head>
  <body>
    <h1>TLPM_getPowerCalibrationPointsInformation</h1>
    <p class="syntax">
      ViStatus TLPM_getPowerCalibrationPointsInformation (ViSession instrumentHandle,
                                                    ViUInt16 index,
                                                    ViChar _VI_FAR serialNumber[],
                                                    ViChar _VI_FAR calibrationDate[],
                                                    ViPUInt16 calibrationPointsCount,
                                                    ViChar _VI_FAR author[],
                                                    ViPUInt16 sensorPosition);
    </p>
    <h2 class="purpose">Purpose</h2>
    <p class="body">
    Queries the customer adjustment header like serial nr, cal date, nr of points at given index
    </p>
    <h2>Parameters</h2>
    <table class="borderless" summary="Parameter help">
      <tr>
        <td valign="top"><em class="label">Input</em></td>
      </tr>
      <tr>
        <td class="tablehead">Name</td>
        <td class="tablehead">Type</td>
        <td class="tablehead">Description</td>
      </tr>
      <tr>
        <td class="paramName">instrumentHandle</td>
        <td class="paramDataType">ViSession</td>
        <td>
    This parameter accepts the instrument handle returned by &lt;Initialize&gt; to select the desired instrument driver session.
        </td>
      </tr>
      <tr>
        <td class="paramName">index</td>
        <td class="paramDataType">ViUInt16</td>
        <td>
    Index of the power calibration (range 1...5)
        </td>
      </tr>
      <tr>
        <td valign="top"><em class="label">Output</em></td>
      </tr>
      <tr>
        <td class="tablehead">Name</td>
        <td class="tablehead">Type</td>
        <td class="tablehead">Description</td>
      </tr>
      <tr>
        <td class="paramName">serialNumber</td>
        <td class="paramDataType">ViChar[]</td>
        <td>
    Serial Number of the sensor.
    <br/>
    Please provide a buffer of 256 characters.
        </td>
      </tr>
      <tr>
        <td class="paramName">calibrationDate</td>
        <td class="paramDataType">ViChar[]</td>
        <td>
    Last calibration date of this sensor
    <br/>
    Please provide a buffer of 256 characters.
        </td>
      </tr>
      <tr>
        <td class="paramName">calibrationPointsCount</td>
        <td class="paramDataType">ViUInt16 (passed by reference)</td>
        <td>
    Number of calibration points of the power calibration with this sensor
        </td>
      </tr>
      <tr>
        <td class="paramName">author</td>
        <td class="paramDataType">ViChar[]</td>
      </tr>
      <tr>
        <td class="paramName">sensorPosition</td>
        <td class="paramDataType">ViUInt16 (passed by reference)</td>
        <td>
    The position of the sencor switch of a Thorlabs S130C
    <br/>
    1 = 5mW
    <br/>
    2 = 500mW
        </td>
      </tr>
    </table>
    <h2>Return Value</h2>
    <table class="borderless" summary="Return value help">
      <tr>
        <td class="tablehead">Name</td>
        <td class="tablehead">Type</td>
        <td class="tablehead">Description</td>
      </tr>
      <tr>
        <td class="paramName">status</td>
        <td class="paramDataType">ViStatus</td>
        <td>
    This is the error code returned by the function call. For error codes and descriptions see &lt;Error Message&gt;.
        </td>
      </tr>
    </table>
    <h2>References</h2>
    <p class="body">
      <a href="ClassUser%20Power%20Calibration.html">User Power Calibration</a><br/>
      <a href="ClassConfigure.html">Configure</a><br/>
      <a href="ClassMeasure.html">Measure</a><br/>
      <a href="Thorlabs%20Power%20Meter%20Instrument%20Driver.html">Thorlabs Power Meter Instrument Driver</a><br/>
      <a href="Alphabetical%20Function%20Index.html">Alphabetical Function Index</a><br/>
      <a href="Hierarchical%20Function%20Index.html">Hierarchical Function Index</a><br/>
    </p>
  </body>
</html>