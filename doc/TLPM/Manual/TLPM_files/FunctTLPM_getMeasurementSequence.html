<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=ISO-8859-1">
    <link rel="stylesheet" href="htmlhelp.css" type="text/css">
    <title>TLPM_getMeasurementSequence</title>
  </head>
  <body>
    <h1>TLPM_getMeasurementSequence</h1>
    <p class="syntax">
      ViStatus TLPM_getMeasurementSequence (ViSession instrumentHandle,
                                      ViUInt32 baseTime, float timeStamps[],
                                      float values[]);
    </p>
    <h2 class="purpose">Purpose</h2>
    <p class="body">
    Should be called if the methods confPowerMeasurementSequence and startMeasurementSequence were called first.
    <br/>
    This function filles the given array with (100 * baseTime) measurements from the device.
    <br/>
    Duration of measurement in µsec = Count* Interval
    <br/>
    The maximum capture time is 1 sec regardless of the used inteval
    <br/>
    Set the bandwidth to high(setInputFilterState to OFF) and disable auto ranging(setPowerAutoRange to OFF)
    <br/>
    Note: The function is only available on PM103.
    </p>
    <h2>Parameters</h2>
    <table class="borderless" summary="Parameter help">
      <tr>
        <td valign="top"><em class="label">Input</em></td>
      </tr>
      <tr>
        <td class="tablehead">Name</td>
        <td class="tablehead">Type</td>
        <td class="tablehead">Description</td>
      </tr>
      <tr>
        <td class="paramName">instrumentHandle</td>
        <td class="paramDataType">ViSession</td>
        <td>
    This parameter accepts the instrument handle returned by &lt;Initialize&gt; to select the desired instrument driver session.
        </td>
      </tr>
      <tr>
        <td class="paramName">baseTime</td>
        <td class="paramDataType">ViUInt32</td>
        <td>
    The amount of samples to collect in the internal interation of the method.
    <br/>
    The value can be from 1 to 100.
        </td>
      </tr>
      <tr>
        <td valign="top"><em class="label">Output</em></td>
      </tr>
      <tr>
        <td class="tablehead">Name</td>
        <td class="tablehead">Type</td>
        <td class="tablehead">Description</td>
      </tr>
      <tr>
        <td class="paramName">timeStamps</td>
        <td class="paramDataType">float []</td>
        <td>
    Array of time stamps in ms. The size of this array is 100 * baseTime.
        </td>
      </tr>
      <tr>
        <td class="paramName">values</td>
        <td class="paramDataType">float []</td>
        <td>
    Array of power/current measurements. The size of this array is 100 * baseTime.
        </td>
      </tr>
    </table>
    <h2>Return Value</h2>
    <table class="borderless" summary="Return value help">
      <tr>
        <td class="tablehead">Name</td>
        <td class="tablehead">Type</td>
        <td class="tablehead">Description</td>
      </tr>
      <tr>
        <td class="paramName">status</td>
        <td class="paramDataType">ViStatus</td>
        <td>
    This is the error code returned by the function call. For error codes and descriptions see &lt;Error Message&gt;.
        </td>
      </tr>
    </table>
    <h2>References</h2>
    <p class="body">
      <a href="ClassArray%20Measurement.html">Array Measurement</a><br/>
      <a href="ClassMeasure.html">Measure</a><br/>
      <a href="Thorlabs%20Power%20Meter%20Instrument%20Driver.html">Thorlabs Power Meter Instrument Driver</a><br/>
      <a href="Alphabetical%20Function%20Index.html">Alphabetical Function Index</a><br/>
      <a href="Hierarchical%20Function%20Index.html">Hierarchical Function Index</a><br/>
    </p>
  </body>
</html>