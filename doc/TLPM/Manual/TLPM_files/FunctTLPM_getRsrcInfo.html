<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=ISO-8859-1">
    <link rel="stylesheet" href="htmlhelp.css" type="text/css">
    <title>TLPM_getRsrcInfo</title>
  </head>
  <body>
    <h1>TLPM_getRsrcInfo</h1>
    <p class="syntax">
      ViStatus TLPM_getRsrcInfo (ViSession instrumentHandle, ViUInt32 index,
                           ViChar _VI_FAR modelName[],
                           ViChar _VI_FAR serialNumber[],
                           ViChar _VI_FAR manufacturer[],
                           ViPBoolean deviceAvailable);
    </p>
    <h2 class="purpose">Purpose</h2>
    <p class="body">
    This function gets information about a connected resource.
    <br/>
    Notes:
    <br/>
    (1) The data provided by this function was updated at the last call of &lt;Find Resources&gt;.
    </p>
    <h2>Parameters</h2>
    <table class="borderless" summary="Parameter help">
      <tr>
        <td valign="top"><em class="label">Input</em></td>
      </tr>
      <tr>
        <td class="tablehead">Name</td>
        <td class="tablehead">Type</td>
        <td class="tablehead">Description</td>
      </tr>
      <tr>
        <td class="paramName">instrumentHandle</td>
        <td class="paramDataType">ViSession</td>
        <td>
    Pass 0 to this parameter.
        </td>
      </tr>
      <tr>
        <td class="paramName">index</td>
        <td class="paramDataType">ViUInt32</td>
        <td>
    This parameter accepts the index of the device to get the resource descriptor from.
    <br/>
    Notes:
    <br/>
    (1) The index is zero based. The maximum index to be used here is one less than the number of devices found by the last call of &lt;Find Resources&gt;.
        </td>
      </tr>
      <tr>
        <td valign="top"><em class="label">Output</em></td>
      </tr>
      <tr>
        <td class="tablehead">Name</td>
        <td class="tablehead">Type</td>
        <td class="tablehead">Description</td>
      </tr>
      <tr>
        <td class="paramName">modelName</td>
        <td class="paramDataType">ViChar[]</td>
        <td>
    This parameter returns the model name of the device.
    <br/>
    Notes:
    <br/>
    (1) The array must contain at least TLPM_BUFFER_SIZE (256) elements ViChar[256].
    <br/>
    (2) You may pass VI_NULL if you do not need this parameter.
    <br/>
    (3) Serial interfaces over Bluetooth will return the interface name instead of the device model name.
        </td>
      </tr>
      <tr>
        <td class="paramName">serialNumber</td>
        <td class="paramDataType">ViChar[]</td>
        <td>
    This parameter returns the serial number of the device.
    <br/>
    Notes:
    <br/>
    (1) The array must contain at least TLPM_BUFFER_SIZE (256) elements ViChar[256].
    <br/>
    (2) You may pass VI_NULL if you do not need this parameter.
    <br/>
    (3) The serial number is not available for serial interfaces over Bluetooth.
        </td>
      </tr>
      <tr>
        <td class="paramName">manufacturer</td>
        <td class="paramDataType">ViChar[]</td>
        <td>
    This parameter returns the manufacturer name of the device.
    <br/>
    Notes:
    <br/>
    (1) The array must contain at least TLPM_BUFFER_SIZE (256) elements ViChar[256].
    <br/>
    (2) You may pass VI_NULL if you do not need this parameter.
    <br/>
    (3) The manufacturer name is not available for serial interfaces over Bluetooth.
        </td>
      </tr>
      <tr>
        <td class="paramName">deviceAvailable</td>
        <td class="paramDataType">ViBoolean (passed by reference)</td>
        <td>
    Returns the information if the device is available.
    <br/>
    Devices that are not available are used by other applications.
    <br/>
    Notes:
    <br/>
    (1) You may pass VI_NULL if you do not need this parameter.
        </td>
      </tr>
    </table>
    <h2>Return Value</h2>
    <table class="borderless" summary="Return value help">
      <tr>
        <td class="tablehead">Name</td>
        <td class="tablehead">Type</td>
        <td class="tablehead">Description</td>
      </tr>
      <tr>
        <td class="paramName">status</td>
        <td class="paramDataType">ViStatus</td>
        <td>
    This is the error code returned by the function call. For error codes and descriptions see &lt;Error Message&gt;.
    <br/>
    Completition Codes
    <br/>
    ----------------------------------------------------------------
    <br/>
    VI_SUCCESS&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Successful
    <br/>
    VI_ERROR_INV_OBJECT&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Index specifies an invalid object
        </td>
      </tr>
    </table>
    <h2>References</h2>
    <p class="body">
      <a href="ClassResource%20Functions.html">Resource Functions</a><br/>
      <a href="Thorlabs%20Power%20Meter%20Instrument%20Driver.html">Thorlabs Power Meter Instrument Driver</a><br/>
      <a href="Alphabetical%20Function%20Index.html">Alphabetical Function Index</a><br/>
      <a href="Hierarchical%20Function%20Index.html">Hierarchical Function Index</a><br/>
    </p>
  </body>
</html>