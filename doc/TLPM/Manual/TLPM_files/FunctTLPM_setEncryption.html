<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=ISO-8859-1">
    <link rel="stylesheet" href="htmlhelp.css" type="text/css">
    <title>TLPM_setEncryption</title>
  </head>
  <body>
    <h1>TLPM_setEncryption</h1>
    <p class="syntax">
      ViStatus TLPM_setEncryption (ViSession instrumentHandle, ViString oldPassword,
                             ViString newPassword, ViBoolean encryption);
    </p>
    <h2 class="purpose">Purpose</h2>
    <p class="body">
    Overwrites the system password used for authentication, Default password ist THORlabs
    </p>
    <h2>Parameters</h2>
    <table class="borderless" summary="Parameter help">
      <tr>
        <td valign="top"><em class="label">Input</em></td>
      </tr>
      <tr>
        <td class="tablehead">Name</td>
        <td class="tablehead">Type</td>
        <td class="tablehead">Description</td>
      </tr>
      <tr>
        <td class="paramName">instrumentHandle</td>
        <td class="paramDataType">ViSession</td>
        <td>
    This parameter accepts the instrument handle returned by &lt;Initialize&gt; to select the desired instrument driver session.
        </td>
      </tr>
      <tr>
        <td class="paramName">oldPassword</td>
        <td class="paramDataType">ViString</td>
        <td>
    old ASCII passwort string. Max length is 25. Min length is 5
        </td>
      </tr>
      <tr>
        <td class="paramName">newPassword</td>
        <td class="paramDataType">ViString</td>
        <td>
    new ASCII passwort string. Max length is 25. Min length is 5
        </td>
      </tr>
      <tr>
        <td class="paramName">encryption</td>
        <td class="paramDataType">ViBoolean</td>
        <td>
    True if SCPI LAN interface should be crypted
        </td>
      </tr>
    </table>
    <h2>Return Value</h2>
    <table class="borderless" summary="Return value help">
      <tr>
        <td class="tablehead">Name</td>
        <td class="tablehead">Type</td>
        <td class="tablehead">Description</td>
      </tr>
      <tr>
        <td class="paramName">status</td>
        <td class="paramDataType">ViStatus</td>
        <td>
    This is the error code returned by the function call. For error codes and descriptions see &lt;Error Message&gt;.
        </td>
      </tr>
    </table>
    <h2>References</h2>
    <p class="body">
      <a href="ClassEthernet%20Interface.html">Ethernet Interface</a><br/>
      <a href="Thorlabs%20Power%20Meter%20Instrument%20Driver.html">Thorlabs Power Meter Instrument Driver</a><br/>
      <a href="Alphabetical%20Function%20Index.html">Alphabetical Function Index</a><br/>
      <a href="Hierarchical%20Function%20Index.html">Hierarchical Function Index</a><br/>
    </p>
  </body>
</html>