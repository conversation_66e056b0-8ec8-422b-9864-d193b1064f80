<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=ISO-8859-1">
    <link rel="stylesheet" href="htmlhelp.css" type="text/css">
    <title>TLPM_errorQueryMode</title>
  </head>
  <body>
    <h1>TLPM_errorQueryMode</h1>
    <p class="syntax">
      ViStatus TLPM_errorQueryMode (ViSession instrumentHandle, ViBoolean mode);
    </p>
    <h2 class="purpose">Purpose</h2>
    <p class="body">
    This function selects the driver's error query mode.
    </p>
    <h2>Parameters</h2>
    <table class="borderless" summary="Parameter help">
      <tr>
        <td valign="top"><em class="label">Input</em></td>
      </tr>
      <tr>
        <td class="tablehead">Name</td>
        <td class="tablehead">Type</td>
        <td class="tablehead">Description</td>
      </tr>
      <tr>
        <td class="paramName">instrumentHandle</td>
        <td class="paramDataType">ViSession</td>
        <td>
    This parameter accepts the instrument handle returned by &lt;Initialize&gt; to select the desired instrument driver session.
        </td>
      </tr>
      <tr>
        <td class="paramName">mode</td>
        <td class="paramDataType">ViBoolean</td>
        <td>
    This parameter specifies the driver's error query mode.
    <br/>
    If set to Automatic each driver function queries the instrument's error queue and in case an error occured returns the error number.
    <br/>
    If set to Manual the driver does not query the instrument for errors and therefore a driver function does not return instrument errors. You should use &lt;Error Query&gt; to manually query the instrument's error queue.
    <br/>
    VI_OFF (0): Manual error query.
    <br/>
    VI_ON&nbsp;&nbsp;(1): Automatic error query (default).
        </td>
      </tr>
    </table>
    <h2>Return Value</h2>
    <table class="borderless" summary="Return value help">
      <tr>
        <td class="tablehead">Name</td>
        <td class="tablehead">Type</td>
        <td class="tablehead">Description</td>
      </tr>
      <tr>
        <td class="paramName">status</td>
        <td class="paramDataType">ViStatus</td>
        <td>
    This is the error code returned by the function call. For error codes and descriptions see &lt;Error Message&gt;.
        </td>
      </tr>
    </table>
    <h2>References</h2>
    <p class="body">
      <a href="ClassUtility%20Functions.html">Utility Functions</a><br/>
      <a href="Thorlabs%20Power%20Meter%20Instrument%20Driver.html">Thorlabs Power Meter Instrument Driver</a><br/>
      <a href="Alphabetical%20Function%20Index.html">Alphabetical Function Index</a><br/>
      <a href="Hierarchical%20Function%20Index.html">Hierarchical Function Index</a><br/>
    </p>
  </body>
</html>