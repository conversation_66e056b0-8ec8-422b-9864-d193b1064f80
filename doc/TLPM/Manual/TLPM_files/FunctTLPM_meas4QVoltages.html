<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=ISO-8859-1">
    <link rel="stylesheet" href="htmlhelp.css" type="text/css">
    <title>TLPM_meas4QVoltages</title>
  </head>
  <body>
    <h1>TLPM_meas4QVoltages</h1>
    <p class="syntax">
      ViStatus TLPM_meas4QVoltages (ViSession instrumentHandle, ViPReal64 voltage1,
                              ViPReal64 voltage2, ViPReal64 voltage3,
                              ViPReal64 voltage4);
    </p>
    <h2 class="purpose">Purpose</h2>
    <p class="body">
    This function returns the voltage of each sector of a 4q sensor
    <br/>
    Notes:
    <br/>
    (1) The function is only available on PM101, PM102, PM400.
    </p>
    <h2>Parameters</h2>
    <table class="borderless" summary="Parameter help">
      <tr>
        <td valign="top"><em class="label">Input</em></td>
      </tr>
      <tr>
        <td class="tablehead">Name</td>
        <td class="tablehead">Type</td>
        <td class="tablehead">Description</td>
      </tr>
      <tr>
        <td class="paramName">instrumentHandle</td>
        <td class="paramDataType">ViSession</td>
        <td>
    This parameter accepts the instrument handle returned by &lt;Initialize&gt; to select the desired instrument driver session.
        </td>
      </tr>
      <tr>
        <td valign="top"><em class="label">Output</em></td>
      </tr>
      <tr>
        <td class="tablehead">Name</td>
        <td class="tablehead">Type</td>
        <td class="tablehead">Description</td>
      </tr>
      <tr>
        <td class="paramName">voltage1</td>
        <td class="paramDataType">ViReal64 (passed by reference)</td>
        <td>
    This parameter returns the actual measured voltage of the upper left sector of a 4q sensor.
        </td>
      </tr>
      <tr>
        <td class="paramName">voltage2</td>
        <td class="paramDataType">ViReal64 (passed by reference)</td>
      </tr>
      <tr>
        <td class="paramName">voltage3</td>
        <td class="paramDataType">ViReal64 (passed by reference)</td>
      </tr>
      <tr>
        <td class="paramName">voltage4</td>
        <td class="paramDataType">ViReal64 (passed by reference)</td>
      </tr>
    </table>
    <h2>Return Value</h2>
    <table class="borderless" summary="Return value help">
      <tr>
        <td class="tablehead">Name</td>
        <td class="tablehead">Type</td>
        <td class="tablehead">Description</td>
      </tr>
      <tr>
        <td class="paramName">status</td>
        <td class="paramDataType">ViStatus</td>
        <td>
    This is the error code returned by the function call. For error codes and descriptions see &lt;Error Message&gt;.
        </td>
      </tr>
    </table>
    <h2>References</h2>
    <p class="body">
      <a href="ClassRead.html">Read</a><br/>
      <a href="ClassMeasure.html">Measure</a><br/>
      <a href="Thorlabs%20Power%20Meter%20Instrument%20Driver.html">Thorlabs Power Meter Instrument Driver</a><br/>
      <a href="Alphabetical%20Function%20Index.html">Alphabetical Function Index</a><br/>
      <a href="Hierarchical%20Function%20Index.html">Hierarchical Function Index</a><br/>
    </p>
  </body>
</html>