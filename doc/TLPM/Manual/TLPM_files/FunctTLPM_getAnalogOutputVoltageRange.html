<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=ISO-8859-1">
    <link rel="stylesheet" href="htmlhelp.css" type="text/css">
    <title>TLPM_getAnalogOutputVoltageRange</title>
  </head>
  <body>
    <h1>TLPM_getAnalogOutputVoltageRange</h1>
    <p class="syntax">
      ViStatus TLPM_getAnalogOutputVoltageRange (ViSession instrumentHandle,
                                           ViPReal64 minVoltage,
                                           ViPReal64 maxVoltage);
    </p>
    <h2 class="purpose">Purpose</h2>
    <p class="body">
    This function returns the range in Volt [V] of the analog output.
    <br/>
    Notes:
    <br/>
    (1) The function is only available on PM101 and PM102
    </p>
    <h2>Parameters</h2>
    <table class="borderless" summary="Parameter help">
      <tr>
        <td valign="top"><em class="label">Input</em></td>
      </tr>
      <tr>
        <td class="tablehead">Name</td>
        <td class="tablehead">Type</td>
        <td class="tablehead">Description</td>
      </tr>
      <tr>
        <td class="paramName">instrumentHandle</td>
        <td class="paramDataType">ViSession</td>
        <td>
    This parameter accepts the instrument handle returned by &lt;Initialize&gt; to select the desired instrument driver session.
        </td>
      </tr>
      <tr>
        <td valign="top"><em class="label">Output</em></td>
      </tr>
      <tr>
        <td class="tablehead">Name</td>
        <td class="tablehead">Type</td>
        <td class="tablehead">Description</td>
      </tr>
      <tr>
        <td class="paramName">minVoltage</td>
        <td class="paramDataType">ViReal64 (passed by reference)</td>
        <td>
    This parameter returns the minimum voltage in Volt [V] of the analog output.
    <br/>
    Lower voltage is clipped to the minimum.
        </td>
      </tr>
      <tr>
        <td class="paramName">maxVoltage</td>
        <td class="paramDataType">ViReal64 (passed by reference)</td>
        <td>
    This parameter returns the maximum voltage in Volt [V] of the analog output.
    <br/>
    Higher voltage values are clipped to the maximum.
        </td>
      </tr>
    </table>
    <h2>Return Value</h2>
    <table class="borderless" summary="Return value help">
      <tr>
        <td class="tablehead">Name</td>
        <td class="tablehead">Type</td>
        <td class="tablehead">Description</td>
      </tr>
      <tr>
        <td class="paramName">status</td>
        <td class="paramDataType">ViStatus</td>
        <td>
    This is the error code returned by the function call. For error codes and descriptions see &lt;Error Message&gt;.
        </td>
      </tr>
    </table>
    <h2>References</h2>
    <p class="body">
      <a href="ClassAnalog%20Output.html">Analog Output</a><br/>
      <a href="ClassConfigure.html">Configure</a><br/>
      <a href="ClassMeasure.html">Measure</a><br/>
      <a href="Thorlabs%20Power%20Meter%20Instrument%20Driver.html">Thorlabs Power Meter Instrument Driver</a><br/>
      <a href="Alphabetical%20Function%20Index.html">Alphabetical Function Index</a><br/>
      <a href="Hierarchical%20Function%20Index.html">Hierarchical Function Index</a><br/>
    </p>
  </body>
</html>