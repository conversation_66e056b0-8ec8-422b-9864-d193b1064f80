<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=ISO-8859-1">
    <link rel="stylesheet" href="htmlhelp.css" type="text/css">
    <title>TLPM_setEnergyRef</title>
  </head>
  <body>
    <h1>TLPM_setEnergyRef</h1>
    <p class="syntax">
      ViStatus TLPM_setEnergyRef (ViSession instrumentHandle,
                            ViReal64 energyReferenceValue);
    </p>
    <h2 class="purpose">Purpose</h2>
    <p class="body">
    This function sets the pyro sensor's energy reference value
    <br/>
    Notes:
    <br/>
    (1) The function is only available on PM100D, PM100USB, PM200, PM400.
    <br/>
    (2) This value is used for calculating differences between the actual energy value and this energy reference value.
    </p>
    <h2>Parameters</h2>
    <table class="borderless" summary="Parameter help">
      <tr>
        <td valign="top"><em class="label">Input</em></td>
      </tr>
      <tr>
        <td class="tablehead">Name</td>
        <td class="tablehead">Type</td>
        <td class="tablehead">Description</td>
      </tr>
      <tr>
        <td class="paramName">instrumentHandle</td>
        <td class="paramDataType">ViSession</td>
        <td>
    This parameter accepts the instrument handle returned by &lt;Initialize&gt; to select the desired instrument driver session.
        </td>
      </tr>
      <tr>
        <td class="paramName">energyReferenceValue</td>
        <td class="paramDataType">ViReal64</td>
        <td>
    This parameter specifies the pyro sensor's energy reference value in joule [J].
    <br/>
    Remark:
    <br/>
    This value is used for calculating differences between the actual energy value and this energy reference value if Energy Reference State is ON.
        </td>
      </tr>
    </table>
    <h2>Return Value</h2>
    <table class="borderless" summary="Return value help">
      <tr>
        <td class="tablehead">Name</td>
        <td class="tablehead">Type</td>
        <td class="tablehead">Description</td>
      </tr>
      <tr>
        <td class="paramName">status</td>
        <td class="paramDataType">ViStatus</td>
        <td>
    This is the error code returned by the function call. For error codes and descriptions see &lt;Error Message&gt;.
        </td>
      </tr>
    </table>
    <h2>References</h2>
    <p class="body">
      <a href="ClassEnergy%20Measurement.html">Energy Measurement</a><br/>
      <a href="ClassConfigure.html">Configure</a><br/>
      <a href="ClassMeasure.html">Measure</a><br/>
      <a href="Thorlabs%20Power%20Meter%20Instrument%20Driver.html">Thorlabs Power Meter Instrument Driver</a><br/>
      <a href="Alphabetical%20Function%20Index.html">Alphabetical Function Index</a><br/>
      <a href="Hierarchical%20Function%20Index.html">Hierarchical Function Index</a><br/>
    </p>
  </body>
</html>