<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=ISO-8859-1">
    <link rel="stylesheet" href="htmlhelp.css" type="text/css">
    <title>TLPM_setVoltageAutoRange</title>
  </head>
  <body>
    <h1>TLPM_setVoltageAutoRange</h1>
    <p class="syntax">
      ViStatus TLPM_setVoltageAutoRange (ViSession instrumentHandle,
                                   ViBoolean voltageAutorangeMode);
    </p>
    <h2 class="purpose">Purpose</h2>
    <p class="body">
    This function sets the voltage auto range mode.
    <br/>
    Notes:
    <br/>
    (1) The function is only available on PM100D, PM100A, PM100USB, PM160T, PM200, PM400.
    </p>
    <h2>Parameters</h2>
    <table class="borderless" summary="Parameter help">
      <tr>
        <td valign="top"><em class="label">Input</em></td>
      </tr>
      <tr>
        <td class="tablehead">Name</td>
        <td class="tablehead">Type</td>
        <td class="tablehead">Description</td>
      </tr>
      <tr>
        <td class="paramName">instrumentHandle</td>
        <td class="paramDataType">ViSession</td>
        <td>
    This parameter accepts the instrument handle returned by &lt;Initialize&gt; to select the desired instrument driver session.
        </td>
      </tr>
      <tr>
        <td class="paramName">voltageAutorangeMode</td>
        <td class="paramDataType">ViBoolean</td>
        <td>
    This parameter specifies the voltage auto range mode.
    <br/>
    Acceptable values:
    <br/>
    TLPM_AUTORANGE_VOLTAGE_OFF (0): voltage auto range disabled
    <br/>
    TLPM_AUTORANGE_VOLTAGE_ON&nbsp;&nbsp;(1): voltage auto range enabled
        </td>
      </tr>
    </table>
    <h2>Return Value</h2>
    <table class="borderless" summary="Return value help">
      <tr>
        <td class="tablehead">Name</td>
        <td class="tablehead">Type</td>
        <td class="tablehead">Description</td>
      </tr>
      <tr>
        <td class="paramName">status</td>
        <td class="paramDataType">ViStatus</td>
        <td>
    This value shows the status code returned by the function call.
    <br/>
    For Status Codes see function &lt;Error Message&gt;.
        </td>
      </tr>
    </table>
    <h2>References</h2>
    <p class="body">
      <a href="ClassVoltage%20Measurement.html">Voltage Measurement</a><br/>
      <a href="ClassConfigure.html">Configure</a><br/>
      <a href="ClassMeasure.html">Measure</a><br/>
      <a href="Thorlabs%20Power%20Meter%20Instrument%20Driver.html">Thorlabs Power Meter Instrument Driver</a><br/>
      <a href="Alphabetical%20Function%20Index.html">Alphabetical Function Index</a><br/>
      <a href="Hierarchical%20Function%20Index.html">Hierarchical Function Index</a><br/>
    </p>
  </body>
</html>