<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=ISO-8859-1">
    <link rel="stylesheet" href="htmlhelp.css" type="text/css">
    <title>Alphabetical Function Index</title>
  </head>
  <body>
    <h1>Alphabetical Function Index</h1>
    <ul class="list-index">
    <li><a href="FunctTLPM_cancelDarkAdjust.html">TLPM_cancelDarkAdjust</a></li>
    <li><a href="FunctTLPM_cancelZeroPos.html">TLPM_cancelZeroPos</a></li>
    <li><a href="FunctTLPM_close.html">TLPM_close</a></li>
    <li><a href="FunctTLPM_confCurrentFastArrayMeasurement.html">TLPM_confCurrentFastArrayMeasurement</a></li>
    <li><a href="FunctTLPM_confCurrentMeasurementSequence.html">TLPM_confCurrentMeasurementSequence</a></li>
    <li><a href="FunctTLPM_confCurrentMeasurementSequenceHWTrigger.html">TLPM_confCurrentMeasurementSequenceHWTrigger</a></li>
    <li><a href="FunctTLPM_confEDensityFastArrayMeasurement.html">TLPM_confEDensityFastArrayMeasurement</a></li>
    <li><a href="FunctTLPM_confEnergyFastArrayMeasurement.html">TLPM_confEnergyFastArrayMeasurement</a></li>
    <li><a href="FunctTLPM_confPDensityFastArrayMeasurement.html">TLPM_confPDensityFastArrayMeasurement</a></li>
    <li><a href="FunctTLPM_confPowerFastArrayMeasurement.html">TLPM_confPowerFastArrayMeasurement</a></li>
    <li><a href="FunctTLPM_confPowerMeasurementSequence.html">TLPM_confPowerMeasurementSequence</a></li>
    <li><a href="FunctTLPM_confPowerMeasurementSequenceHWTrigger.html">TLPM_confPowerMeasurementSequenceHWTrigger</a></li>
    <li><a href="FunctTLPM_confVoltageFastArrayMeasurement.html">TLPM_confVoltageFastArrayMeasurement</a></li>
    <li><a href="FunctTLPM_errorMessage.html">TLPM_errorMessage</a></li>
    <li><a href="FunctTLPM_errorQuery.html">TLPM_errorQuery</a></li>
    <li><a href="FunctTLPM_errorQueryMode.html">TLPM_errorQueryMode</a></li>
    <li><a href="FunctTLPM_findRsrc.html">TLPM_findRsrc</a></li>
    <li><a href="FunctTLPM_getAccelMode.html">TLPM_getAccelMode</a></li>
    <li><a href="FunctTLPM_getAccelState.html">TLPM_getAccelState</a></li>
    <li><a href="FunctTLPM_getAccelTau.html">TLPM_getAccelTau</a></li>
    <li><a href="FunctTLPM_getAnalogOutputHub.html">TLPM_getAnalogOutputHub</a></li>
    <li><a href="FunctTLPM_getAnalogOutputSlope.html">TLPM_getAnalogOutputSlope</a></li>
    <li><a href="FunctTLPM_getAnalogOutputSlopeRange.html">TLPM_getAnalogOutputSlopeRange</a></li>
    <li><a href="FunctTLPM_getAnalogOutputVoltage.html">TLPM_getAnalogOutputVoltage</a></li>
    <li><a href="FunctTLPM_getAnalogOutputVoltageRange.html">TLPM_getAnalogOutputVoltageRange</a></li>
    <li><a href="FunctTLPM_getAttenuation.html">TLPM_getAttenuation</a></li>
    <li><a href="FunctTLPM_getAvgCnt.html">TLPM_getAvgCnt</a></li>
    <li><a href="FunctTLPM_getAvgTime.html">TLPM_getAvgTime</a></li>
    <li><a href="FunctTLPM_getBatteryVoltage.html">TLPM_getBatteryVoltage</a></li>
    <li><a href="FunctTLPM_getBeamDia.html">TLPM_getBeamDia</a></li>
    <li><a href="FunctTLPM_getCalibrationMsg.html">TLPM_getCalibrationMsg</a></li>
    <li><a href="FunctTLPM_getCurrentAutorange.html">TLPM_getCurrentAutorange</a></li>
    <li><a href="FunctTLPM_getCurrentRange.html">TLPM_getCurrentRange</a></li>
    <li><a href="FunctTLPM_getCurrentRanges.html">TLPM_getCurrentRanges</a></li>
    <li><a href="FunctTLPM_getCurrentRef.html">TLPM_getCurrentRef</a></li>
    <li><a href="FunctTLPM_getCurrentRefState.html">TLPM_getCurrentRefState</a></li>
    <li><a href="FunctTLPM_getDarkAdjustState.html">TLPM_getDarkAdjustState</a></li>
    <li><a href="FunctTLPM_getDarkOffset.html">TLPM_getDarkOffset</a></li>
    <li><a href="FunctTLPM_getDeviceBaudrate.html">TLPM_getDeviceBaudrate</a></li>
    <li><a href="FunctTLPM_getDFUPort.html">TLPM_getDFUPort</a></li>
    <li><a href="FunctTLPM_getDHCP.html">TLPM_getDHCP</a></li>
    <li><a href="FunctTLPM_getDigIoDirection.html">TLPM_getDigIoDirection</a></li>
    <li><a href="FunctTLPM_getDigIoOutput.html">TLPM_getDigIoOutput</a></li>
    <li><a href="FunctTLPM_getDigIoPinInput.html">TLPM_getDigIoPinInput</a></li>
    <li><a href="FunctTLPM_getDigIoPinMode.html">TLPM_getDigIoPinMode</a></li>
    <li><a href="FunctTLPM_getDigIoPinOutput.html">TLPM_getDigIoPinOutput</a></li>
    <li><a href="FunctTLPM_getDigIoPort.html">TLPM_getDigIoPort</a></li>
    <li><a href="FunctTLPM_getDispBrightness.html">TLPM_getDispBrightness</a></li>
    <li><a href="FunctTLPM_getDispContrast.html">TLPM_getDispContrast</a></li>
    <li><a href="FunctTLPM_getDisplayName.html">TLPM_getDisplayName</a></li>
    <li><a href="FunctTLPM_getDriverBaudrate.html">TLPM_getDriverBaudrate</a></li>
    <li><a href="FunctTLPM_getEnableNetSearch.html">TLPM_getEnableNetSearch</a></li>
    <li><a href="FunctTLPM_getEncryption.html">TLPM_getEncryption</a></li>
    <li><a href="FunctTLPM_getEnergyRange.html">TLPM_getEnergyRange</a></li>
    <li><a href="FunctTLPM_getEnergyRef.html">TLPM_getEnergyRef</a></li>
    <li><a href="FunctTLPM_getEnergyRefState.html">TLPM_getEnergyRefState</a></li>
    <li><a href="FunctTLPM_getExtNtcParameter.html">TLPM_getExtNtcParameter</a></li>
    <li><a href="FunctTLPM_getFastMaxSamplerate.html">TLPM_getFastMaxSamplerate</a></li>
    <li><a href="FunctTLPM_getFilterAutoMode.html">TLPM_getFilterAutoMode</a></li>
    <li><a href="FunctTLPM_getFilterPosition.html">TLPM_getFilterPosition</a></li>
    <li><a href="FunctTLPM_getFreqMode.html">TLPM_getFreqMode</a></li>
    <li><a href="FunctTLPM_getFreqRange.html">TLPM_getFreqRange</a></li>
    <li><a href="FunctTLPM_getHostname.html">TLPM_getHostname</a></li>
    <li><a href="FunctTLPM_getInputAdapterType.html">TLPM_getInputAdapterType</a></li>
    <li><a href="FunctTLPM_getInputFilterState.html">TLPM_getInputFilterState</a></li>
    <li><a href="FunctTLPM_getIPAddress.html">TLPM_getIPAddress</a></li>
    <li><a href="FunctTLPM_getIPMask.html">TLPM_getIPMask</a></li>
    <li><a href="FunctTLPM_getLANPropagation.html">TLPM_getLANPropagation</a></li>
    <li><a href="FunctTLPM_getLineFrequency.html">TLPM_getLineFrequency</a></li>
    <li><a href="FunctTLPM_getMACAddress.html">TLPM_getMACAddress</a></li>
    <li><a href="FunctTLPM_getMeasPinEnergyLevel.html">TLPM_getMeasPinEnergyLevel</a></li>
    <li><a href="FunctTLPM_getMeasPinMode.html">TLPM_getMeasPinMode</a></li>
    <li><a href="FunctTLPM_getMeasPinPowerLevel.html">TLPM_getMeasPinPowerLevel</a></li>
    <li><a href="FunctTLPM_getMeasurementSequence.html">TLPM_getMeasurementSequence</a></li>
    <li><a href="FunctTLPM_getMeasurementSequenceHWTrigger.html">TLPM_getMeasurementSequenceHWTrigger</a></li>
    <li><a href="FunctTLPM_getNextFastArrayMeasurement.html">TLPM_getNextFastArrayMeasurement</a></li>
    <li><a href="FunctTLPM_getPeakFilter.html">TLPM_getPeakFilter</a></li>
    <li><a href="FunctTLPM_getPeakThreshold.html">TLPM_getPeakThreshold</a></li>
    <li><a href="FunctTLPM_getPhotodiodeResponsivity.html">TLPM_getPhotodiodeResponsivity</a></li>
    <li><a href="FunctTLPM_getPositionAnalogOutputSlope.html">TLPM_getPositionAnalogOutputSlope</a></li>
    <li><a href="FunctTLPM_getPositionAnalogOutputSlopeRange.html">TLPM_getPositionAnalogOutputSlopeRange</a></li>
    <li><a href="FunctTLPM_getPositionAnalogOutputVoltage.html">TLPM_getPositionAnalogOutputVoltage</a></li>
    <li><a href="FunctTLPM_getPositionAnalogOutputVoltageRange.html">TLPM_getPositionAnalogOutputVoltageRange</a></li>
    <li><a href="FunctTLPM_getPowerAutorange.html">TLPM_getPowerAutorange</a></li>
    <li><a href="FunctTLPM_getPowerCalibrationPoints.html">TLPM_getPowerCalibrationPoints</a></li>
    <li><a href="FunctTLPM_getPowerCalibrationPointsInformation.html">TLPM_getPowerCalibrationPointsInformation</a></li>
    <li><a href="FunctTLPM_getPowerCalibrationPointsState.html">TLPM_getPowerCalibrationPointsState</a></li>
    <li><a href="FunctTLPM_getPowerRange.html">TLPM_getPowerRange</a></li>
    <li><a href="FunctTLPM_getPowerRef.html">TLPM_getPowerRef</a></li>
    <li><a href="FunctTLPM_getPowerRefState.html">TLPM_getPowerRefState</a></li>
    <li><a href="FunctTLPM_getPowerUnit.html">TLPM_getPowerUnit</a></li>
    <li><a href="FunctTLPM_getPyrosensorResponsivity.html">TLPM_getPyrosensorResponsivity</a></li>
    <li><a href="FunctTLPM_getRsrcInfo.html">TLPM_getRsrcInfo</a></li>
    <li><a href="FunctTLPM_getRsrcName.html">TLPM_getRsrcName</a></li>
    <li><a href="FunctTLPM_getSCPIPort.html">TLPM_getSCPIPort</a></li>
    <li><a href="FunctTLPM_getSensorInfo.html">TLPM_getSensorInfo</a></li>
    <li><a href="FunctTLPM_getThermopileResponsivity.html">TLPM_getThermopileResponsivity</a></li>
    <li><a href="FunctTLPM_getTime.html">TLPM_getTime</a></li>
    <li><a href="FunctTLPM_getTimeoutValue.html">TLPM_getTimeoutValue</a></li>
    <li><a href="FunctTLPM_getVoltageAutorange.html">TLPM_getVoltageAutorange</a></li>
    <li><a href="FunctTLPM_getVoltageRange.html">TLPM_getVoltageRange</a></li>
    <li><a href="FunctTLPM_getVoltageRanges.html">TLPM_getVoltageRanges</a></li>
    <li><a href="FunctTLPM_getVoltageRef.html">TLPM_getVoltageRef</a></li>
    <li><a href="FunctTLPM_getVoltageRefState.html">TLPM_getVoltageRefState</a></li>
    <li><a href="FunctTLPM_getWavelength.html">TLPM_getWavelength</a></li>
    <li><a href="FunctTLPM_getWebPort.html">TLPM_getWebPort</a></li>
    <li><a href="FunctTLPM_getZeroPos.html">TLPM_getZeroPos</a></li>
    <li><a href="FunctTLPM_identificationQuery.html">TLPM_identificationQuery</a></li>
    <li><a href="FunctTLPM_init.html">TLPM_init</a></li>
    <li><a href="FunctTLPM_isPeakDetectorRunning.html">TLPM_isPeakDetectorRunning</a></li>
    <li><a href="FunctTLPM_meas4QPositions.html">TLPM_meas4QPositions</a></li>
    <li><a href="FunctTLPM_meas4QVoltages.html">TLPM_meas4QVoltages</a></li>
    <li><a href="FunctTLPM_measAuxAD0.html">TLPM_measAuxAD0</a></li>
    <li><a href="FunctTLPM_measAuxAD1.html">TLPM_measAuxAD1</a></li>
    <li><a href="FunctTLPM_measCurrent.html">TLPM_measCurrent</a></li>
    <li><a href="FunctTLPM_measEmmHumidity.html">TLPM_measEmmHumidity</a></li>
    <li><a href="FunctTLPM_measEmmTemperature.html">TLPM_measEmmTemperature</a></li>
    <li><a href="FunctTLPM_measEnergy.html">TLPM_measEnergy</a></li>
    <li><a href="FunctTLPM_measEnergyDens.html">TLPM_measEnergyDens</a></li>
    <li><a href="FunctTLPM_measExtNtcResistance.html">TLPM_measExtNtcResistance</a></li>
    <li><a href="FunctTLPM_measExtNtcTemperature.html">TLPM_measExtNtcTemperature</a></li>
    <li><a href="FunctTLPM_measFreq.html">TLPM_measFreq</a></li>
    <li><a href="FunctTLPM_measHeadTemperature.html">TLPM_measHeadTemperature</a></li>
    <li><a href="FunctTLPM_measNegDutyCycle.html">TLPM_measNegDutyCycle</a></li>
    <li><a href="FunctTLPM_measNegPulseWidth.html">TLPM_measNegPulseWidth</a></li>
    <li><a href="FunctTLPM_measPosDutyCycle.html">TLPM_measPosDutyCycle</a></li>
    <li><a href="FunctTLPM_measPosPulseWidth.html">TLPM_measPosPulseWidth</a></li>
    <li><a href="FunctTLPM_measPower.html">TLPM_measPower</a></li>
    <li><a href="FunctTLPM_measPowerDens.html">TLPM_measPowerDens</a></li>
    <li><a href="FunctTLPM_measVoltage.html">TLPM_measVoltage</a></li>
    <li><a href="FunctTLPM_presetRegister.html">TLPM_presetRegister</a></li>
    <li><a href="FunctTLPM_readRaw.html">TLPM_readRaw</a></li>
    <li><a href="FunctTLPM_readRegister.html">TLPM_readRegister</a></li>
    <li><a href="FunctTLPM_reinitSensor.html">TLPM_reinitSensor</a></li>
    <li><a href="FunctTLPM_reset.html">TLPM_reset</a></li>
    <li><a href="FunctTLPM_resetFastArrayMeasurement.html">TLPM_resetFastArrayMeasurement</a></li>
    <li><a href="FunctTLPM_revisionQuery.html">TLPM_revisionQuery</a></li>
    <li><a href="FunctTLPM_selfTest.html">TLPM_selfTest</a></li>
    <li><a href="FunctTLPM_setAccelMode.html">TLPM_setAccelMode</a></li>
    <li><a href="FunctTLPM_setAccelState.html">TLPM_setAccelState</a></li>
    <li><a href="FunctTLPM_setAccelTau.html">TLPM_setAccelTau</a></li>
    <li><a href="FunctTLPM_setAnalogOutputHub.html">TLPM_setAnalogOutputHub</a></li>
    <li><a href="FunctTLPM_setAnalogOutputSlope.html">TLPM_setAnalogOutputSlope</a></li>
    <li><a href="FunctTLPM_setAttenuation.html">TLPM_setAttenuation</a></li>
    <li><a href="FunctTLPM_setAvgCnt.html">TLPM_setAvgCnt</a></li>
    <li><a href="FunctTLPM_setAvgTime.html">TLPM_setAvgTime</a></li>
    <li><a href="FunctTLPM_setBeamDia.html">TLPM_setBeamDia</a></li>
    <li><a href="FunctTLPM_setCurrentAutoRange.html">TLPM_setCurrentAutoRange</a></li>
    <li><a href="FunctTLPM_setCurrentRange.html">TLPM_setCurrentRange</a></li>
    <li><a href="FunctTLPM_setCurrentRef.html">TLPM_setCurrentRef</a></li>
    <li><a href="FunctTLPM_setCurrentRefState.html">TLPM_setCurrentRefState</a></li>
    <li><a href="FunctTLPM_setDeviceBaudrate.html">TLPM_setDeviceBaudrate</a></li>
    <li><a href="FunctTLPM_setDFUPort.html">TLPM_setDFUPort</a></li>
    <li><a href="FunctTLPM_setDHCP.html">TLPM_setDHCP</a></li>
    <li><a href="FunctTLPM_setDigIoDirection.html">TLPM_setDigIoDirection</a></li>
    <li><a href="FunctTLPM_setDigIoOutput.html">TLPM_setDigIoOutput</a></li>
    <li><a href="FunctTLPM_setDigIoPinMode.html">TLPM_setDigIoPinMode</a></li>
    <li><a href="FunctTLPM_setDigIoPinOutput.html">TLPM_setDigIoPinOutput</a></li>
    <li><a href="FunctTLPM_setDispBrightness.html">TLPM_setDispBrightness</a></li>
    <li><a href="FunctTLPM_setDispContrast.html">TLPM_setDispContrast</a></li>
    <li><a href="FunctTLPM_setDisplayName.html">TLPM_setDisplayName</a></li>
    <li><a href="FunctTLPM_setDriverBaudrate.html">TLPM_setDriverBaudrate</a></li>
    <li><a href="FunctTLPM_setEnableNetSearch.html">TLPM_setEnableNetSearch</a></li>
    <li><a href="FunctTLPM_setEncryption.html">TLPM_setEncryption</a></li>
    <li><a href="FunctTLPM_setEnergyRange.html">TLPM_setEnergyRange</a></li>
    <li><a href="FunctTLPM_setEnergyRef.html">TLPM_setEnergyRef</a></li>
    <li><a href="FunctTLPM_setEnergyRefState.html">TLPM_setEnergyRefState</a></li>
    <li><a href="FunctTLPM_setExtNtcParameter.html">TLPM_setExtNtcParameter</a></li>
    <li><a href="FunctTLPM_setFilterAutoMode.html">TLPM_setFilterAutoMode</a></li>
    <li><a href="FunctTLPM_setFilterPosition.html">TLPM_setFilterPosition</a></li>
    <li><a href="FunctTLPM_setFreqMode.html">TLPM_setFreqMode</a></li>
    <li><a href="FunctTLPM_setHostname.html">TLPM_setHostname</a></li>
    <li><a href="FunctTLPM_setInputAdapterType.html">TLPM_setInputAdapterType</a></li>
    <li><a href="FunctTLPM_setInputFilterState.html">TLPM_setInputFilterState</a></li>
    <li><a href="FunctTLPM_setIPAddress.html">TLPM_setIPAddress</a></li>
    <li><a href="FunctTLPM_setIPMask.html">TLPM_setIPMask</a></li>
    <li><a href="FunctTLPM_setLANPropagation.html">TLPM_setLANPropagation</a></li>
    <li><a href="FunctTLPM_setLineFrequency.html">TLPM_setLineFrequency</a></li>
    <li><a href="FunctTLPM_setMeasPinEnergyLevel.html">TLPM_setMeasPinEnergyLevel</a></li>
    <li><a href="FunctTLPM_setMeasPinPowerLevel.html">TLPM_setMeasPinPowerLevel</a></li>
    <li><a href="FunctTLPM_setNegativeDutyCycle.html">TLPM_setNegativeDutyCycle</a></li>
    <li><a href="FunctTLPM_setNegativePulseWidth.html">TLPM_setNegativePulseWidth</a></li>
    <li><a href="FunctTLPM_setNetSearchMask.html">TLPM_setNetSearchMask</a></li>
    <li><a href="FunctTLPM_setPeakFilter.html">TLPM_setPeakFilter</a></li>
    <li><a href="FunctTLPM_setPeakThreshold.html">TLPM_setPeakThreshold</a></li>
    <li><a href="FunctTLPM_setPhotodiodeResponsivity.html">TLPM_setPhotodiodeResponsivity</a></li>
    <li><a href="FunctTLPM_setPositionAnalogOutputSlope.html">TLPM_setPositionAnalogOutputSlope</a></li>
    <li><a href="FunctTLPM_setPositiveDutyCycle.html">TLPM_setPositiveDutyCycle</a></li>
    <li><a href="FunctTLPM_setPositivePulseWidth.html">TLPM_setPositivePulseWidth</a></li>
    <li><a href="FunctTLPM_setPowerAutoRange.html">TLPM_setPowerAutoRange</a></li>
    <li><a href="FunctTLPM_setPowerCalibrationPoints.html">TLPM_setPowerCalibrationPoints</a></li>
    <li><a href="FunctTLPM_setPowerCalibrationPointsState.html">TLPM_setPowerCalibrationPointsState</a></li>
    <li><a href="FunctTLPM_setPowerRange.html">TLPM_setPowerRange</a></li>
    <li><a href="FunctTLPM_setPowerRef.html">TLPM_setPowerRef</a></li>
    <li><a href="FunctTLPM_setPowerRefState.html">TLPM_setPowerRefState</a></li>
    <li><a href="FunctTLPM_setPowerUnit.html">TLPM_setPowerUnit</a></li>
    <li><a href="FunctTLPM_setPyrosensorResponsivity.html">TLPM_setPyrosensorResponsivity</a></li>
    <li><a href="FunctTLPM_setSCPIPort.html">TLPM_setSCPIPort</a></li>
    <li><a href="FunctTLPM_setThermopileResponsivity.html">TLPM_setThermopileResponsivity</a></li>
    <li><a href="FunctTLPM_setTime.html">TLPM_setTime</a></li>
    <li><a href="FunctTLPM_setTimeoutValue.html">TLPM_setTimeoutValue</a></li>
    <li><a href="FunctTLPM_setVoltageAutoRange.html">TLPM_setVoltageAutoRange</a></li>
    <li><a href="FunctTLPM_setVoltageRange.html">TLPM_setVoltageRange</a></li>
    <li><a href="FunctTLPM_setVoltageRef.html">TLPM_setVoltageRef</a></li>
    <li><a href="FunctTLPM_setVoltageRefState.html">TLPM_setVoltageRefState</a></li>
    <li><a href="FunctTLPM_setWavelength.html">TLPM_setWavelength</a></li>
    <li><a href="FunctTLPM_setWebPort.html">TLPM_setWebPort</a></li>
    <li><a href="FunctTLPM_setZeroPos.html">TLPM_setZeroPos</a></li>
    <li><a href="FunctTLPM_startDarkAdjust.html">TLPM_startDarkAdjust</a></li>
    <li><a href="FunctTLPM_startMeasurementSequence.html">TLPM_startMeasurementSequence</a></li>
    <li><a href="FunctTLPM_startPeakDetector.html">TLPM_startPeakDetector</a></li>
    <li><a href="FunctTLPM_startZeroPos.html">TLPM_startZeroPos</a></li>
    <li><a href="FunctTLPM_writeRaw.html">TLPM_writeRaw</a></li>
    <li><a href="FunctTLPM_writeRegister.html">TLPM_writeRegister</a></li>
    </ul>
  </body>
</html>