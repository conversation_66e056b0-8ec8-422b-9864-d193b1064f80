<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=ISO-8859-1">
    <link rel="stylesheet" href="htmlhelp.css" type="text/css">
    <title>TLPM_getDigIoOutput</title>
  </head>
  <body>
    <h1>TLPM_getDigIoOutput</h1>
    <p class="syntax">
      ViStatus TLPM_getDigIoOutput (ViSession instrumentHandle, ViPBoolean IO0,
                              ViPBoolean IO1, ViPBoolean IO2, ViPBoolean IO3);
    </p>
    <h2 class="purpose">Purpose</h2>
    <p class="body">
    This function returns the digital I/O output settings.
    <br/>
    Note: The function is only available on PM200 and PM400.
    </p>
    <h2>Parameters</h2>
    <table class="borderless" summary="Parameter help">
      <tr>
        <td valign="top"><em class="label">Input</em></td>
      </tr>
      <tr>
        <td class="tablehead">Name</td>
        <td class="tablehead">Type</td>
        <td class="tablehead">Description</td>
      </tr>
      <tr>
        <td class="paramName">instrumentHandle</td>
        <td class="paramDataType">ViSession</td>
        <td>
    This parameter accepts the instrument handle returned by &lt;Initialize&gt; to select the desired instrument driver session.
        </td>
      </tr>
      <tr>
        <td valign="top"><em class="label">Output</em></td>
      </tr>
      <tr>
        <td class="tablehead">Name</td>
        <td class="tablehead">Type</td>
        <td class="tablehead">Description</td>
      </tr>
      <tr>
        <td class="paramName">IO0</td>
        <td class="paramDataType">ViBoolean (passed by reference)</td>
        <td>
    This parameter returns the I/O port #0 output where VI_OFF (0) indicates low level and VI_ON (1) indicates high level.
    <br/>
    Note: You may pass VI_NULL if you don't need this value.
        </td>
      </tr>
      <tr>
        <td class="paramName">IO1</td>
        <td class="paramDataType">ViBoolean (passed by reference)</td>
        <td>
    This parameter returns the I/O port #1 output where VI_OFF (0) indicates low level and VI_ON (1) indicates high level.
    <br/>
    Note: You may pass VI_NULL if you don't need this value.
        </td>
      </tr>
      <tr>
        <td class="paramName">IO2</td>
        <td class="paramDataType">ViBoolean (passed by reference)</td>
        <td>
    This parameter returns the I/O port #2 output where VI_OFF (0) indicates low level and VI_ON (1) indicates high level.
    <br/>
    Note: You may pass VI_NULL if you don't need this value.
        </td>
      </tr>
      <tr>
        <td class="paramName">IO3</td>
        <td class="paramDataType">ViBoolean (passed by reference)</td>
        <td>
    This parameter returns the I/O port #3 output where VI_OFF (0) indicates low level and VI_ON (1) indicates high level.
    <br/>
    Note: You may pass VI_NULL if you don't need this value.
        </td>
      </tr>
    </table>
    <h2>Return Value</h2>
    <table class="borderless" summary="Return value help">
      <tr>
        <td class="tablehead">Name</td>
        <td class="tablehead">Type</td>
        <td class="tablehead">Description</td>
      </tr>
      <tr>
        <td class="paramName">status</td>
        <td class="paramDataType">ViStatus</td>
        <td>
    This is the error code returned by the function call. For error codes and descriptions see &lt;Error Message&gt;.
        </td>
      </tr>
    </table>
    <h2>References</h2>
    <p class="body">
      <a href="ClassDigital%20I,O.html">Digital I/O</a><br/>
      <a href="Thorlabs%20Power%20Meter%20Instrument%20Driver.html">Thorlabs Power Meter Instrument Driver</a><br/>
      <a href="Alphabetical%20Function%20Index.html">Alphabetical Function Index</a><br/>
      <a href="Hierarchical%20Function%20Index.html">Hierarchical Function Index</a><br/>
    </p>
  </body>
</html>