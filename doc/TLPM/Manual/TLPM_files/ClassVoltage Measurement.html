<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=ISO-8859-1">
    <link rel="stylesheet" href="htmlhelp.css" type="text/css">
    <title>Voltage Measurement</title>
  </head>
  <body>
    <h1>Voltage Measurement</h1>
    <p class="body">
    This class of functions handles voltage measurement related items of the instrument. This set of functions is only useable with sensor types that produce an electrical voltage as measurement value (Thermopile sensors and pyroelectric sensors).
    </p>
    <h3>Functions</h3>
    <ul class="list-index">
    <li><a href="FunctTLPM_getVoltageAutorange.html">TLPM_getVoltageAutorange</a></li>
    <li><a href="FunctTLPM_getVoltageRange.html">TLPM_getVoltageRange</a></li>
    <li><a href="FunctTLPM_getVoltageRanges.html">TLPM_getVoltageRanges</a></li>
    <li><a href="FunctTLPM_getVoltageRef.html">TLPM_getVoltageRef</a></li>
    <li><a href="FunctTLPM_getVoltageRefState.html">TLPM_getVoltageRefState</a></li>
    <li><a href="FunctTLPM_setVoltageAutoRange.html">TLPM_setVoltageAutoRange</a></li>
    <li><a href="FunctTLPM_setVoltageRange.html">TLPM_setVoltageRange</a></li>
    <li><a href="FunctTLPM_setVoltageRef.html">TLPM_setVoltageRef</a></li>
    <li><a href="FunctTLPM_setVoltageRefState.html">TLPM_setVoltageRefState</a></li>
    </ul>
    <h2>References</h2>
    <p class="body">
      <a href="ClassConfigure.html">Configure</a><br/>
      <a href="ClassMeasure.html">Measure</a><br/>
      <a href="Thorlabs%20Power%20Meter%20Instrument%20Driver.html">Thorlabs Power Meter Instrument Driver</a><br/>
      <a href="Alphabetical%20Function%20Index.html">Alphabetical Function Index</a><br/>
      <a href="Hierarchical%20Function%20Index.html">Hierarchical Function Index</a><br/>
    </p>
  </body>
</html>