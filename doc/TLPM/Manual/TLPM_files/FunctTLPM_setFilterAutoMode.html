<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=ISO-8859-1">
    <link rel="stylesheet" href="htmlhelp.css" type="text/css">
    <title>TLPM_setFilterAutoMode</title>
  </head>
  <body>
    <h1>TLPM_setFilterAutoMode</h1>
    <p class="syntax">
      ViStatus TLPM_setFilterAutoMode (ViSession instrumentHandle,
                                 ViBoolean filterAutoPositionDetection);
    </p>
    <h2 class="purpose">Purpose</h2>
    <p class="body">
    This function enables / disables the automatic filter position detection
    <br/>
    Notes:
    <br/>
    (1) The function is only available on PM160 with firmware version 1.5.4 and higher
    </p>
    <h2>Parameters</h2>
    <table class="borderless" summary="Parameter help">
      <tr>
        <td valign="top"><em class="label">Input</em></td>
      </tr>
      <tr>
        <td class="tablehead">Name</td>
        <td class="tablehead">Type</td>
        <td class="tablehead">Description</td>
      </tr>
      <tr>
        <td class="paramName">instrumentHandle</td>
        <td class="paramDataType">ViSession</td>
        <td>
    This parameter accepts the instrument handle returned by &lt;Initialize&gt; to select the desired instrument driver session.
        </td>
      </tr>
      <tr>
        <td class="paramName">filterAutoPositionDetection</td>
        <td class="paramDataType">ViBoolean</td>
        <td>
    This parameter specifies if the automatic filter position detection is enabled/disabled
    <br/>
    Acceptable values:
    <br/>
    VI_OFF (0): Filter position detection is OFF. The manual set fitler position is used
    <br/>
    VI_ON&nbsp;&nbsp;(1): Filter position detection is ON, The filter position will be automatically detected
        </td>
      </tr>
    </table>
    <h2>Return Value</h2>
    <table class="borderless" summary="Return value help">
      <tr>
        <td class="tablehead">Name</td>
        <td class="tablehead">Type</td>
        <td class="tablehead">Description</td>
      </tr>
      <tr>
        <td class="paramName">status</td>
        <td class="paramDataType">ViStatus</td>
        <td>
    This value shows the status code returned by the function call.
    <br/>
    For Status Codes see function &lt;Error Message&gt;.
        </td>
      </tr>
    </table>
    <h2>References</h2>
    <p class="body">
      <a href="ClassFilter%20Position.html">Filter Position</a><br/>
      <a href="ClassConfigure.html">Configure</a><br/>
      <a href="ClassMeasure.html">Measure</a><br/>
      <a href="Thorlabs%20Power%20Meter%20Instrument%20Driver.html">Thorlabs Power Meter Instrument Driver</a><br/>
      <a href="Alphabetical%20Function%20Index.html">Alphabetical Function Index</a><br/>
      <a href="Hierarchical%20Function%20Index.html">Hierarchical Function Index</a><br/>
    </p>
  </body>
</html>