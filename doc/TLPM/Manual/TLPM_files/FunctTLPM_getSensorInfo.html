<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=ISO-8859-1">
    <link rel="stylesheet" href="htmlhelp.css" type="text/css">
    <title>TLPM_getSensorInfo</title>
  </head>
  <body>
    <h1>TLPM_getSensorInfo</h1>
    <p class="syntax">
      ViStatus TLPM_getSensorInfo (ViSession instrumentHandle,
                             ViChar _VI_FAR sensorName[],
                             ViChar _VI_FAR sensorSerialNumber[],
                             ViChar _VI_FAR sensorCalibrationMessage[],
                             ViPInt16 sensorType, ViPInt16 sensorSubtype,
                             ViPInt16 sensorFlags);
    </p>
    <h2 class="purpose">Purpose</h2>
    <p class="body">
    This function is used to obtain informations from the connected sensor like sensor name, serial number, calibration message, sensor type, sensor subtype and sensor flags.&nbsp;&nbsp;
    <br/>
    Remark:
    <br/>
    The meanings of the obtained sensor type, subtype and flags are:
    <br/>
    Sensor Types:
    <br/>
    SENSOR_TYPE_NONE&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;0x00 // No sensor
    <br/>
    SENSOR_TYPE_PD_SINGLE&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;0x01 // Photodiode sensor
    <br/>
    SENSOR_TYPE_THERMO&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;0x02 // Thermopile sensor
    <br/>
    SENSOR_TYPE_PYRO&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;0x03 // Pyroelectric sensor
    <br/>
    Sensor Subtypes:
    <br/>
    SENSOR_SUBTYPE_NONE&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;0x00 // No sensor
    <br/>
    Sensor Subtypes Photodiode:
    <br/>
    SENSOR_SUBTYPE_PD_ADAPTER&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;0x01 // Photodiode adapter
    <br/>
    SENSOR_SUBTYPE_PD_SINGLE_STD&nbsp;&nbsp;&nbsp;0x02 // Photodiode sensor
    <br/>
    SENSOR_SUBTYPE_PD_SINGLE_FSR&nbsp;&nbsp;&nbsp;0x03 // Photodiode sensor with
    <br/>
    integrated filter
    <br/>
    identified by position
    <br/>
    SENSOR_SUBTYPE_PD_SINGLE_STD_T 0x12 // Photodiode sensor with
    <br/>
    temperature sensor
    <br/>
    Sensor Subtypes Thermopile:
    <br/>
    SENSOR_SUBTYPE_THERMO_ADAPTER&nbsp;&nbsp;0x01 // Thermopile adapter
    <br/>
    SENSOR_SUBTYPE_THERMO_STD&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;0x02 // Thermopile sensor
    <br/>
    SENSOR_SUBTYPE_THERMO_STD_T&nbsp;&nbsp;&nbsp;&nbsp;0x12 // Thermopile sensor with
    <br/>
    temperature sensor
    <br/>
    Sensor Subtypes Pyroelectric Sensor:
    <br/>
    SENSOR_SUBTYPE_PYRO_ADAPTER&nbsp;&nbsp;&nbsp;&nbsp;0x01 // Pyroelectric adapter
    <br/>
    SENSOR_SUBTYPE_PYRO_STD&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;0x02 // Pyroelectric sensor
    <br/>
    SENSOR_SUBTYPE_PYRO_STD_T&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;0x12 // Pyroelectric sensor with
    <br/>
    temperature sensor
    <br/>
    Sensor Flags:
    <br/>
    TLPM_SENS_FLAG_IS_POWER&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;0x0001 // Power sensor
    <br/>
    TLPM_SENS_FLAG_IS_ENERGY&nbsp;&nbsp;&nbsp;&nbsp;0x0002 // Energy sensor
    <br/>
    TLPM_SENS_FLAG_IS_RESP_SET&nbsp;&nbsp;0x0010 // Responsivity settable
    <br/>
    TLPM_SENS_FLAG_IS_WAVEL_SET 0x0020 // Wavelength settable
    <br/>
    TLPM_SENS_FLAG_IS_TAU_SET&nbsp;&nbsp;&nbsp;0x0040 // Time constant settable
    <br/>
    TLPM_SENS_FLAG_HAS_TEMP&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;0x0100 // With Temperature sensor
    </p>
    <h2>Parameters</h2>
    <table class="borderless" summary="Parameter help">
      <tr>
        <td valign="top"><em class="label">Input</em></td>
      </tr>
      <tr>
        <td class="tablehead">Name</td>
        <td class="tablehead">Type</td>
        <td class="tablehead">Description</td>
      </tr>
      <tr>
        <td class="paramName">instrumentHandle</td>
        <td class="paramDataType">ViSession</td>
        <td>
    This parameter accepts the instrument handle returned by &lt;Initialize&gt; to select the desired instrument driver session.
        </td>
      </tr>
      <tr>
        <td valign="top"><em class="label">Output</em></td>
      </tr>
      <tr>
        <td class="tablehead">Name</td>
        <td class="tablehead">Type</td>
        <td class="tablehead">Description</td>
      </tr>
      <tr>
        <td class="paramName">sensorName</td>
        <td class="paramDataType">ViChar[]</td>
        <td>
    This parameter returns the name of the connected sensor.
        </td>
      </tr>
      <tr>
        <td class="paramName">sensorSerialNumber</td>
        <td class="paramDataType">ViChar[]</td>
        <td>
    This parameter returns the serial number of the connected sensor.
        </td>
      </tr>
      <tr>
        <td class="paramName">sensorCalibrationMessage</td>
        <td class="paramDataType">ViChar[]</td>
        <td>
    This parameter returns the calibration message of the connected sensor.
        </td>
      </tr>
      <tr>
        <td class="paramName">sensorType</td>
        <td class="paramDataType">ViInt16 (passed by reference)</td>
        <td>
    This parameter returns the sensor type of the connected sensor.
    <br/>
    Remark:
    <br/>
    The meanings of the obtained sensor type are:
    <br/>
    Sensor Types:
    <br/>
    SENSOR_TYPE_NONE&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;0x00 // No sensor
    <br/>
    SENSOR_TYPE_PD_SINGLE&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;0x01 // Photodiode sensor
    <br/>
    SENSOR_TYPE_THERMO&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;0x02 // Thermopile sensor
    <br/>
    SENSOR_TYPE_PYRO&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;0x03 // Pyroelectric sensor
        </td>
      </tr>
      <tr>
        <td class="paramName">sensorSubtype</td>
        <td class="paramDataType">ViInt16 (passed by reference)</td>
        <td>
    This parameter returns the subtype of the connected sensor.
    <br/>
    Remark:
    <br/>
    The meanings of the obtained sensor subtype are:
    <br/>
    Sensor Subtypes:
    <br/>
    SENSOR_SUBTYPE_NONE&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;0x00 // No sensor
    <br/>
    Sensor Subtypes Photodiode:
    <br/>
    SENSOR_SUBTYPE_PD_ADAPTER&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;0x01 // Photodiode adapter
    <br/>
    SENSOR_SUBTYPE_PD_SINGLE_STD&nbsp;&nbsp;&nbsp;0x02 // Photodiode sensor
    <br/>
    SENSOR_SUBTYPE_PD_SINGLE_FSR&nbsp;&nbsp;&nbsp;0x03 // Photodiode sensor with
    <br/>
    integrated filter
    <br/>
    identified by position
    <br/>
    SENSOR_SUBTYPE_PD_SINGLE_STD_T 0x12 // Photodiode sensor with
    <br/>
    temperature sensor
    <br/>
    Sensor Subtypes Thermopile:
    <br/>
    SENSOR_SUBTYPE_THERMO_ADAPTER&nbsp;&nbsp;0x01 // Thermopile adapter
    <br/>
    SENSOR_SUBTYPE_THERMO_STD&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;0x02 // Thermopile sensor
    <br/>
    SENSOR_SUBTYPE_THERMO_STD_T&nbsp;&nbsp;&nbsp;&nbsp;0x12 // Thermopile sensor with
    <br/>
    temperature sensor
    <br/>
    Sensor Subtypes Pyroelectric Sensor:
    <br/>
    SENSOR_SUBTYPE_PYRO_ADAPTER&nbsp;&nbsp;&nbsp;&nbsp;0x01 // Pyroelectric adapter
    <br/>
    SENSOR_SUBTYPE_PYRO_STD&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;0x02 // Pyroelectric sensor
    <br/>
    SENSOR_SUBTYPE_PYRO_STD_T&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;0x12 // Pyroelectric sensor with
    <br/>
    temperature sensor
        </td>
      </tr>
      <tr>
        <td class="paramName">sensorFlags</td>
        <td class="paramDataType">ViInt16 (passed by reference)</td>
        <td>
    This parameter returns the flags of the connected sensor.
    <br/>
    Remark:
    <br/>
    The meanings of the obtained sensor flags are:
    <br/>
    Sensor Flags:
    <br/>
    TLPM_SENS_FLAG_IS_POWER&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;0x0001 // Power sensor
    <br/>
    TLPM_SENS_FLAG_IS_ENERGY&nbsp;&nbsp;&nbsp;&nbsp;0x0002 // Energy sensor
    <br/>
    TLPM_SENS_FLAG_IS_RESP_SET&nbsp;&nbsp;0x0010 // Responsivity settable
    <br/>
    TLPM_SENS_FLAG_IS_WAVEL_SET 0x0020 // Wavelength settable
    <br/>
    TLPM_SENS_FLAG_IS_TAU_SET&nbsp;&nbsp;&nbsp;0x0040 // Time constant settable
    <br/>
    TLPM_SENS_FLAG_HAS_TEMP&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;0x0100 // With Temperature sensor
        </td>
      </tr>
    </table>
    <h2>Return Value</h2>
    <table class="borderless" summary="Return value help">
      <tr>
        <td class="tablehead">Name</td>
        <td class="tablehead">Type</td>
        <td class="tablehead">Description</td>
      </tr>
      <tr>
        <td class="paramName">status</td>
        <td class="paramDataType">ViStatus</td>
        <td>
    This is the error code returned by the function call. For error codes and descriptions see &lt;Error Message&gt;.
        </td>
      </tr>
    </table>
    <h2>References</h2>
    <p class="body">
      <a href="ClassUtility%20Functions.html">Utility Functions</a><br/>
      <a href="Thorlabs%20Power%20Meter%20Instrument%20Driver.html">Thorlabs Power Meter Instrument Driver</a><br/>
      <a href="Alphabetical%20Function%20Index.html">Alphabetical Function Index</a><br/>
      <a href="Hierarchical%20Function%20Index.html">Hierarchical Function Index</a><br/>
    </p>
  </body>
</html>