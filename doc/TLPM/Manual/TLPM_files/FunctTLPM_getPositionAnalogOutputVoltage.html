<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=ISO-8859-1">
    <link rel="stylesheet" href="htmlhelp.css" type="text/css">
    <title>TLPM_getPositionAnalogOutputVoltage</title>
  </head>
  <body>
    <h1>TLPM_getPositionAnalogOutputVoltage</h1>
    <p class="syntax">
      ViStatus TLPM_getPositionAnalogOutputVoltage (ViSession instrumentHandle,
                                              ViInt16 attribute,
                                              ViPReal64 voltageX,
                                              ViPReal64 voltageY);
    </p>
    <h2 class="purpose">Purpose</h2>
    <p class="body">
    This function returns the analog output in Volt [V].
    <br/>
    Notes:
    <br/>
    (1) The function is only available on PM102
    </p>
    <h2>Parameters</h2>
    <table class="borderless" summary="Parameter help">
      <tr>
        <td valign="top"><em class="label">Input</em></td>
      </tr>
      <tr>
        <td class="tablehead">Name</td>
        <td class="tablehead">Type</td>
        <td class="tablehead">Description</td>
      </tr>
      <tr>
        <td class="paramName">instrumentHandle</td>
        <td class="paramDataType">ViSession</td>
        <td>
    This parameter accepts the instrument handle returned by &lt;Initialize&gt; to select the desired instrument driver session.
        </td>
      </tr>
      <tr>
        <td class="paramName">attribute</td>
        <td class="paramDataType">ViInt16</td>
        <td>
    This parameter specifies the value to be queried.
    <br/>
    Acceptable values:
    <br/>
    TLPM_ATTR_SET_VAL&nbsp;&nbsp;(0): Set value
    <br/>
    TLPM_ATTR_MIN_VAL&nbsp;&nbsp;(1): Minimum value
    <br/>
    TLPM_ATTR_MAX_VAL&nbsp;&nbsp;(2): Maximum value
    <br/>
    TLPM_ATTR_DFLT_VAL (3): Default value
        </td>
      </tr>
      <tr>
        <td valign="top"><em class="label">Output</em></td>
      </tr>
      <tr>
        <td class="tablehead">Name</td>
        <td class="tablehead">Type</td>
        <td class="tablehead">Description</td>
      </tr>
      <tr>
        <td class="paramName">voltageX</td>
        <td class="paramDataType">ViReal64 (passed by reference)</td>
        <td>
    This parameter returns the analog output in Volt [V] for the AO2 channel ( x direction)
        </td>
      </tr>
      <tr>
        <td class="paramName">voltageY</td>
        <td class="paramDataType">ViReal64 (passed by reference)</td>
        <td>
    This parameter returns the analog output in Volt [V] for the AO3 channel ( y direction)
        </td>
      </tr>
    </table>
    <h2>Return Value</h2>
    <table class="borderless" summary="Return value help">
      <tr>
        <td class="tablehead">Name</td>
        <td class="tablehead">Type</td>
        <td class="tablehead">Description</td>
      </tr>
      <tr>
        <td class="paramName">status</td>
        <td class="paramDataType">ViStatus</td>
        <td>
    This is the error code returned by the function call. For error codes and descriptions see &lt;Error Message&gt;.
        </td>
      </tr>
    </table>
    <h2>References</h2>
    <p class="body">
      <a href="ClassPosition%20Analog%20Output.html">Position Analog Output</a><br/>
      <a href="ClassConfigure.html">Configure</a><br/>
      <a href="ClassMeasure.html">Measure</a><br/>
      <a href="Thorlabs%20Power%20Meter%20Instrument%20Driver.html">Thorlabs Power Meter Instrument Driver</a><br/>
      <a href="Alphabetical%20Function%20Index.html">Alphabetical Function Index</a><br/>
      <a href="Hierarchical%20Function%20Index.html">Hierarchical Function Index</a><br/>
    </p>
  </body>
</html>