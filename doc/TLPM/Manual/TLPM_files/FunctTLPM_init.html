<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=ISO-8859-1">
    <link rel="stylesheet" href="htmlhelp.css" type="text/css">
    <title>TLPM_init</title>
  </head>
  <body>
    <h1>TLPM_init</h1>
    <p class="syntax">
      ViStatus TLPM_init (ViRsrc resourceName, ViBoolean IDQuery,
                    ViBoolean resetDevice, ViPSession instrumentHandle);
    </p>
    <h2 class="purpose">Purpose</h2>
    <p class="body">
    This function initializes the instrument driver session and performs the following initialization actions:
    <br/>
    (1) Opens a session to the Default Resource Manager resource and a session to the specified device using the Resource Name.
    <br/>
    (2) Performs an identification query on the instrument.
    <br/>
    (3) Resets the instrument to a known state.
    <br/>
    (4) Sends initialization commands to the instrument.
    <br/>
    (5) Returns an instrument handle which is used to distinguish between different sessions of this instrument driver.
    <br/>
    Notes:
    <br/>
    (1) Each time this function is invoked a unique session is opened.&nbsp;&nbsp;
    </p>
    <h2>Parameters</h2>
    <table class="borderless" summary="Parameter help">
      <tr>
        <td valign="top"><em class="label">Input</em></td>
      </tr>
      <tr>
        <td class="tablehead">Name</td>
        <td class="tablehead">Type</td>
        <td class="tablehead">Description</td>
      </tr>
      <tr>
        <td class="paramName">resourceName</td>
        <td class="paramDataType">ViRsrc</td>
        <td>
    This parameter specifies the device (resource) with which to establish a communication session.
    <br/>
    The syntax for the Instrument Descriptor for USB interfaces:
    <br/>
    USB[board]::0x1313::product id::serial number[::interface number][::INSTR]
    <br/>
    The syntax for the Instrument Descriptor for Bluetooth interfaces:
    <br/>
    ASRL&lt;port id&gt;[::INSTR]
    <br/>
    Optional segments are shown in square brackets ([]).
    <br/>
    Remote Access&nbsp;&nbsp;&nbsp;visa://hostname[:port]/resource
    <br/>
    The default values for optional parameters are shown below.
    <br/>
    Optional Segment&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Default Value
    <br/>
    ---------------------------------------
    <br/>
    board&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;0
    <br/>
    USB interface number&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;0
    <br/>
    The USB product id codes for supported instruments are shown below.
    <br/>
    Product ID&nbsp;&nbsp;&nbsp;Instrument Type
    <br/>
    -------------------------------------------------
    <br/>
    0x8070&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;PM100D with DFU interface enabled
    <br/>
    0x8071&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;PM100A with DFU interface enabled
    <br/>
    0x8072&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;PM100USB with DFU interface enabled
    <br/>
    0x8073&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;PM160 with DFU interface enabled
    <br/>
    0x8074&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;PM160T with DFU interface enabled
    <br/>
    0x8075&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;PM400 with DFU interface enabled
    <br/>
    0x8078&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;PM100D with DFU interface disabled
    <br/>
    0x8079&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;PM100A with DFU interface disabled
    <br/>
    0x807B&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;PM160 with DFU interface disabled
    <br/>
    0x807C&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;PM160T with DFU interface disabled
    <br/>
    0x807D&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;PM400 with DFU interface disabled
    <br/>
    0x80B0&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;PM200
    <br/>
    Example Resource Strings:
    <br/>
    --------------------------------------------------------------
    <br/>
    USB::0x1313::0x8070::M12345678::INSTR
    <br/>
    PM100D with a serial number of
    <br/>
    M12345678.
    <br/>
    USB::0x1313::0x80B0::M12345678::INSTR
    <br/>
    PM200 with a serial number of
    <br/>
    M12345678.
    <br/>
    ASRL3::INSTR
    <br/>
    PM160(T) on serial port COM3 over
    <br/>
    bluetooth.
    <br/>
    visa://*******/USB::0x1313::0x8071::*********::INSTR&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
    <br/>
    Remote access to the PM100A with a serial
    <br/>
    number of ********* at the specified IP
    <br/>
    address.
    <br/>
    visa://hostname/USB::0x1313::0x8072::*********::INSTR
    <br/>
    Remote access to the PM100USB with a
    <br/>
    serial number of ********* on the
    <br/>
    specified host.
    <br/>
    Notes:
    <br/>
    (1) You may use VISA &lt;Find Resources&gt; with an appropriate seach pattern to get the Resource Name for your device.
    <br/>
    For PM100x devices use: &quot;USB?*::0x1313::0x807?::?*::INSTR&quot;
    <br/>
    For PM200 devices use:&nbsp;&nbsp;&quot;USB?*::0x1313::0x80B?::?*::INSTR&quot;
    <br/>
    (2) You may use the driver functions in Resource Functions class for creating a list of available devices and getting the according Resource Name string.
        </td>
      </tr>
      <tr>
        <td class="paramName">IDQuery</td>
        <td class="paramDataType">ViBoolean</td>
        <td>
    This parameter specifies whether an identification query is performed during the initialization process.
    <br/>
    VI_OFF (0): Skip query.
    <br/>
    VI_ON&nbsp;&nbsp;(1): Do query (default).
        </td>
      </tr>
      <tr>
        <td class="paramName">resetDevice</td>
        <td class="paramDataType">ViBoolean</td>
        <td>
    This parameter specifies whether the instrument is reset during the initialization process.
    <br/>
    VI_OFF (0) - no reset
    <br/>
    VI_ON&nbsp;&nbsp;(1) - instrument is reset (default)
        </td>
      </tr>
      <tr>
        <td valign="top"><em class="label">Output</em></td>
      </tr>
      <tr>
        <td class="tablehead">Name</td>
        <td class="tablehead">Type</td>
        <td class="tablehead">Description</td>
      </tr>
      <tr>
        <td class="paramName">instrumentHandle</td>
        <td class="paramDataType">ViSession (passed by reference)</td>
        <td>
    This parameter returns an instrument handle that is used in all subsequent calls to distinguish between different sessions of this instrument driver.
        </td>
      </tr>
    </table>
    <h2>Return Value</h2>
    <table class="borderless" summary="Return value help">
      <tr>
        <td class="tablehead">Name</td>
        <td class="tablehead">Type</td>
        <td class="tablehead">Description</td>
      </tr>
      <tr>
        <td class="paramName">status</td>
        <td class="paramDataType">ViStatus</td>
        <td>
    This is the error code returned by the function call. For error codes and descriptions see &lt;Error Message&gt;.
        </td>
      </tr>
    </table>
    <h2>References</h2>
    <p class="body">
      <a href="Thorlabs%20Power%20Meter%20Instrument%20Driver.html">Thorlabs Power Meter Instrument Driver</a><br/>
      <a href="Alphabetical%20Function%20Index.html">Alphabetical Function Index</a><br/>
      <a href="Hierarchical%20Function%20Index.html">Hierarchical Function Index</a><br/>
    </p>
  </body>
</html>