<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=ISO-8859-1">
    <link rel="stylesheet" href="htmlhelp.css" type="text/css">
    <title>Status Register</title>
  </head>
  <body>
    <h1>Status Register</h1>
    <p class="body">
    This class of functions provides access to the instrument's status register structure. Refer to your instrument's user's manual for more details on status structure registers.
    </p>
    <h3>Functions</h3>
    <ul class="list-index">
    <li><a href="FunctTLPM_presetRegister.html">TLPM_presetRegister</a></li>
    <li><a href="FunctTLPM_readRegister.html">TLPM_readRegister</a></li>
    <li><a href="FunctTLPM_writeRegister.html">TLPM_writeRegister</a></li>
    </ul>
    <h2>References</h2>
    <p class="body">
      <a href="Thorlabs%20Power%20Meter%20Instrument%20Driver.html">Thorlabs Power Meter Instrument Driver</a><br/>
      <a href="Alphabetical%20Function%20Index.html">Alphabetical Function Index</a><br/>
      <a href="Hierarchical%20Function%20Index.html">Hierarchical Function Index</a><br/>
    </p>
  </body>
</html>