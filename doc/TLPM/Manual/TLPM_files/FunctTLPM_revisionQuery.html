<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=ISO-8859-1">
    <link rel="stylesheet" href="htmlhelp.css" type="text/css">
    <title>TLPM_revisionQuery</title>
  </head>
  <body>
    <h1>TLPM_revisionQuery</h1>
    <p class="syntax">
      ViStatus TLPM_revisionQuery (ViSession instrumentHandle,
                             ViChar _VI_FAR instrumentDriverRevision[],
                             ViChar _VI_FAR firmwareRevision[]);
    </p>
    <h2 class="purpose">Purpose</h2>
    <p class="body">
    This function returns the revision numbers of the instrument driver and the device firmware.
    </p>
    <h2>Parameters</h2>
    <table class="borderless" summary="Parameter help">
      <tr>
        <td valign="top"><em class="label">Input</em></td>
      </tr>
      <tr>
        <td class="tablehead">Name</td>
        <td class="tablehead">Type</td>
        <td class="tablehead">Description</td>
      </tr>
      <tr>
        <td class="paramName">instrumentHandle</td>
        <td class="paramDataType">ViSession</td>
        <td>
    This parameter accepts the instrument handle returned by &lt;Initialize&gt; to select the desired instrument driver session. You may pass VI_NULL.
    <br/>
    Note: If you pass VI_NULL an empty string will be returned for the firmware revision parameter.
        </td>
      </tr>
      <tr>
        <td valign="top"><em class="label">Output</em></td>
      </tr>
      <tr>
        <td class="tablehead">Name</td>
        <td class="tablehead">Type</td>
        <td class="tablehead">Description</td>
      </tr>
      <tr>
        <td class="paramName">instrumentDriverRevision</td>
        <td class="paramDataType">ViChar[]</td>
        <td>
    This parameter returns the Instrument Driver revision.
    <br/>
    Notes:
    <br/>
    (1) The array must contain at least 256 elements ViChar[256].
    <br/>
    (2) You may pass VI_NULL if you do not need this value.
        </td>
      </tr>
      <tr>
        <td class="paramName">firmwareRevision</td>
        <td class="paramDataType">ViChar[]</td>
        <td>
    This parameter returns the device firmware revision.
    <br/>
    Notes:
    <br/>
    (1) The array must contain at least 256 elements ViChar[256].
    <br/>
    (2) You may pass VI_NULL if you do not need this value.
        </td>
      </tr>
    </table>
    <h2>Return Value</h2>
    <table class="borderless" summary="Return value help">
      <tr>
        <td class="tablehead">Name</td>
        <td class="tablehead">Type</td>
        <td class="tablehead">Description</td>
      </tr>
      <tr>
        <td class="paramName">status</td>
        <td class="paramDataType">ViStatus</td>
        <td>
    This is the error code returned by the function call. For error codes and descriptions see &lt;Error Message&gt;.
        </td>
      </tr>
    </table>
    <h2>References</h2>
    <p class="body">
      <a href="ClassUtility%20Functions.html">Utility Functions</a><br/>
      <a href="Thorlabs%20Power%20Meter%20Instrument%20Driver.html">Thorlabs Power Meter Instrument Driver</a><br/>
      <a href="Alphabetical%20Function%20Index.html">Alphabetical Function Index</a><br/>
      <a href="Hierarchical%20Function%20Index.html">Hierarchical Function Index</a><br/>
    </p>
  </body>
</html>