<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=ISO-8859-1">
    <link rel="stylesheet" href="htmlhelp.css" type="text/css">
    <title>TLPM_errorQuery</title>
  </head>
  <body>
    <h1>TLPM_errorQuery</h1>
    <p class="syntax">
      ViStatus TLPM_errorQuery (ViSession instrumentHandle, ViPInt32 errorNumber,
                          ViChar _VI_FAR errorMessage[]);
    </p>
    <h2 class="purpose">Purpose</h2>
    <p class="body">
    This function queries the instrument's error queue manually.
    <br/>
    Use this function to query the instrument's error queue if the driver's error query mode is set to manual query.
    <br/>
    Notes:
    <br/>
    (1) The returned values are stored in the drivers error store. You may use &lt;Error Message&gt; to get a descriptive text at a later point of time.
    </p>
    <h2>Parameters</h2>
    <table class="borderless" summary="Parameter help">
      <tr>
        <td valign="top"><em class="label">Input</em></td>
      </tr>
      <tr>
        <td class="tablehead">Name</td>
        <td class="tablehead">Type</td>
        <td class="tablehead">Description</td>
      </tr>
      <tr>
        <td class="paramName">instrumentHandle</td>
        <td class="paramDataType">ViSession</td>
        <td>
    This parameter accepts the instrument handle returned by &lt;Initialize&gt; to select the desired instrument driver session.
        </td>
      </tr>
      <tr>
        <td valign="top"><em class="label">Output</em></td>
      </tr>
      <tr>
        <td class="tablehead">Name</td>
        <td class="tablehead">Type</td>
        <td class="tablehead">Description</td>
      </tr>
      <tr>
        <td class="paramName">errorNumber</td>
        <td class="paramDataType">ViInt32 (passed by reference)</td>
        <td>
    This parameter returns the instrument error number.
    <br/>
    Notes:
    <br/>
    (1) You may pass VI_NULL if you don't need this value.
        </td>
      </tr>
      <tr>
        <td class="paramName">errorMessage</td>
        <td class="paramDataType">ViChar[]</td>
        <td>
    This parameter returns the instrument error message.
    <br/>
    Notes:
    <br/>
    (1) The array must contain at least TLPM_ERR_DESCR_BUFFER_SIZE (512) elements ViChar[512].
    <br/>
    (2) You may pass VI_NULL if you do not need this value.
        </td>
      </tr>
    </table>
    <h2>Return Value</h2>
    <table class="borderless" summary="Return value help">
      <tr>
        <td class="tablehead">Name</td>
        <td class="tablehead">Type</td>
        <td class="tablehead">Description</td>
      </tr>
      <tr>
        <td class="paramName">status</td>
        <td class="paramDataType">ViStatus</td>
        <td>
    This is the error code returned by the function call. For error codes and descriptions see &lt;Error Message&gt;.
        </td>
      </tr>
    </table>
    <h2>References</h2>
    <p class="body">
      <a href="ClassUtility%20Functions.html">Utility Functions</a><br/>
      <a href="Thorlabs%20Power%20Meter%20Instrument%20Driver.html">Thorlabs Power Meter Instrument Driver</a><br/>
      <a href="Alphabetical%20Function%20Index.html">Alphabetical Function Index</a><br/>
      <a href="Hierarchical%20Function%20Index.html">Hierarchical Function Index</a><br/>
    </p>
  </body>
</html>