<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=ISO-8859-1">
    <link rel="stylesheet" href="htmlhelp.css" type="text/css">
    <title>Current Measurement</title>
  </head>
  <body>
    <h1>Current Measurement</h1>
    <p class="body">
    This class of functions handles current measurement related items of the instrument. This set of functions is only useable with sensor types that produce an electrical current as measurement value (Photodiodes).
    </p>
    <h3>Functions</h3>
    <ul class="list-index">
    <li><a href="FunctTLPM_getCurrentAutorange.html">TLPM_getCurrentAutorange</a></li>
    <li><a href="FunctTLPM_getCurrentRange.html">TLPM_getCurrentRange</a></li>
    <li><a href="FunctTLPM_getCurrentRanges.html">TLPM_getCurrentRanges</a></li>
    <li><a href="FunctTLPM_getCurrentRef.html">TLPM_getCurrentRef</a></li>
    <li><a href="FunctTLPM_getCurrentRefState.html">TLPM_getCurrentRefState</a></li>
    <li><a href="FunctTLPM_setCurrentAutoRange.html">TLPM_setCurrentAutoRange</a></li>
    <li><a href="FunctTLPM_setCurrentRange.html">TLPM_setCurrentRange</a></li>
    <li><a href="FunctTLPM_setCurrentRef.html">TLPM_setCurrentRef</a></li>
    <li><a href="FunctTLPM_setCurrentRefState.html">TLPM_setCurrentRefState</a></li>
    </ul>
    <h2>References</h2>
    <p class="body">
      <a href="ClassConfigure.html">Configure</a><br/>
      <a href="ClassMeasure.html">Measure</a><br/>
      <a href="Thorlabs%20Power%20Meter%20Instrument%20Driver.html">Thorlabs Power Meter Instrument Driver</a><br/>
      <a href="Alphabetical%20Function%20Index.html">Alphabetical Function Index</a><br/>
      <a href="Hierarchical%20Function%20Index.html">Hierarchical Function Index</a><br/>
    </p>
  </body>
</html>