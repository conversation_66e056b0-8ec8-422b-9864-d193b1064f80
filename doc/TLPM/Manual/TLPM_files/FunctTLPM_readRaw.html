<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=ISO-8859-1">
    <link rel="stylesheet" href="htmlhelp.css" type="text/css">
    <title>TLPM_readRaw</title>
  </head>
  <body>
    <h1>TLPM_readRaw</h1>
    <p class="syntax">
      ViStatus TLPM_readRaw (ViSession instrumentHandle, ViChar _VI_FAR buffer[],
                       ViUInt32 size, ViPUInt32 returnCount);
    </p>
    <h2 class="purpose">Purpose</h2>
    <p class="body">
    This function reads directly from the instrument.
    </p>
    <h2>Parameters</h2>
    <table class="borderless" summary="Parameter help">
      <tr>
        <td valign="top"><em class="label">Input</em></td>
      </tr>
      <tr>
        <td class="tablehead">Name</td>
        <td class="tablehead">Type</td>
        <td class="tablehead">Description</td>
      </tr>
      <tr>
        <td class="paramName">instrumentHandle</td>
        <td class="paramDataType">ViSession</td>
        <td>
    This parameter accepts the instrument handle returned by &lt;Initialize&gt; to select the desired instrument driver session.
        </td>
      </tr>
      <tr>
        <td class="paramName">size</td>
        <td class="paramDataType">ViUInt32</td>
        <td>
    This parameter specifies the buffer size.
        </td>
      </tr>
      <tr>
        <td valign="top"><em class="label">Output</em></td>
      </tr>
      <tr>
        <td class="tablehead">Name</td>
        <td class="tablehead">Type</td>
        <td class="tablehead">Description</td>
      </tr>
      <tr>
        <td class="paramName">buffer</td>
        <td class="paramDataType">ViChar[]</td>
        <td>
    Byte buffer that receives the data read from the instrument.
    <br/>
    Notes:
    <br/>
    (1) If received data is less than buffer size, the buffer is additionaly terminated with a '\0' character.
    <br/>
    (2) If received data is same as buffer size no '\0' character is appended. Its the caller's responsibility to make sure a buffer is '\0' terminated if the caller wants to interprete the buffer as string.
        </td>
      </tr>
      <tr>
        <td class="paramName">returnCount</td>
        <td class="paramDataType">ViUInt32 (passed by reference)</td>
        <td>
    Number of bytes actually transferred and filled into Buffer. This number doesn't count the additional termination '\0' character. If Return Count == size the buffer content will not be '\0' terminated.
    <br/>
    Notes:
    <br/>
    (1) You may pass VI_NULL if you don't need this value.
        </td>
      </tr>
    </table>
    <h2>Return Value</h2>
    <table class="borderless" summary="Return value help">
      <tr>
        <td class="tablehead">Name</td>
        <td class="tablehead">Type</td>
        <td class="tablehead">Description</td>
      </tr>
      <tr>
        <td class="paramName">status</td>
        <td class="paramDataType">ViStatus</td>
        <td>
    This is the error code returned by the function call. For error codes and descriptions see &lt;Error Message&gt;.
        </td>
      </tr>
    </table>
    <h2>References</h2>
    <p class="body">
      <a href="ClassRaw%20I,O.html">Raw I/O</a><br/>
      <a href="ClassUtility%20Functions.html">Utility Functions</a><br/>
      <a href="Thorlabs%20Power%20Meter%20Instrument%20Driver.html">Thorlabs Power Meter Instrument Driver</a><br/>
      <a href="Alphabetical%20Function%20Index.html">Alphabetical Function Index</a><br/>
      <a href="Hierarchical%20Function%20Index.html">Hierarchical Function Index</a><br/>
    </p>
  </body>
</html>