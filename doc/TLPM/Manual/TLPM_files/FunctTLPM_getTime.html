<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=ISO-8859-1">
    <link rel="stylesheet" href="htmlhelp.css" type="text/css">
    <title>TLPM_getTime</title>
  </head>
  <body>
    <h1>TLPM_getTime</h1>
    <p class="syntax">
      ViStatus TLPM_getTime (ViSession instrumentHandle, ViPInt16 year,
                       ViPInt16 month, ViPInt16 day, ViPInt16 hour,
                       ViPInt16 minute, ViPInt16 second);
    </p>
    <h2 class="purpose">Purpose</h2>
    <p class="body">
    This function returns the system date and time of the powermeter.
    <br/>
    Notes:
    <br/>
    (1) Date and time are displayed on instruments screen and are used as timestamp for data saved to memory card.
    <br/>
    (2) The function is only available on PM100D, PM200, PM400.
    </p>
    <h2>Parameters</h2>
    <table class="borderless" summary="Parameter help">
      <tr>
        <td valign="top"><em class="label">Input</em></td>
      </tr>
      <tr>
        <td class="tablehead">Name</td>
        <td class="tablehead">Type</td>
        <td class="tablehead">Description</td>
      </tr>
      <tr>
        <td class="paramName">instrumentHandle</td>
        <td class="paramDataType">ViSession</td>
        <td>
    This parameter accepts the instrument handle returned by &lt;Initialize&gt; to select the desired instrument driver session.
        </td>
      </tr>
      <tr>
        <td valign="top"><em class="label">Output</em></td>
      </tr>
      <tr>
        <td class="tablehead">Name</td>
        <td class="tablehead">Type</td>
        <td class="tablehead">Description</td>
      </tr>
      <tr>
        <td class="paramName">year</td>
        <td class="paramDataType">ViInt16 (passed by reference)</td>
        <td>
    This parameter specifies the actual year in the format yyyy.
        </td>
      </tr>
      <tr>
        <td class="paramName">month</td>
        <td class="paramDataType">ViInt16 (passed by reference)</td>
        <td>
    This parameter specifies the actual month in the format mm.
        </td>
      </tr>
      <tr>
        <td class="paramName">day</td>
        <td class="paramDataType">ViInt16 (passed by reference)</td>
        <td>
    This parameter specifies the actual day in the format dd.
        </td>
      </tr>
      <tr>
        <td class="paramName">hour</td>
        <td class="paramDataType">ViInt16 (passed by reference)</td>
        <td>
    This parameter specifies the actual hour in the format hh.
        </td>
      </tr>
      <tr>
        <td class="paramName">minute</td>
        <td class="paramDataType">ViInt16 (passed by reference)</td>
        <td>
    This parameter specifies the actual minute in the format mm.
        </td>
      </tr>
      <tr>
        <td class="paramName">second</td>
        <td class="paramDataType">ViInt16 (passed by reference)</td>
        <td>
    This parameter specifies the actual second in the format ss.
        </td>
      </tr>
    </table>
    <h2>Return Value</h2>
    <table class="borderless" summary="Return value help">
      <tr>
        <td class="tablehead">Name</td>
        <td class="tablehead">Type</td>
        <td class="tablehead">Description</td>
      </tr>
      <tr>
        <td class="paramName">status</td>
        <td class="paramDataType">ViStatus</td>
        <td>
    This is the error code returned by the function call. For error codes and descriptions see &lt;Error Message&gt;.
        </td>
      </tr>
    </table>
    <h2>References</h2>
    <p class="body">
      <a href="ClassDate%20Time.html">Date Time</a><br/>
      <a href="ClassSystem.html">System</a><br/>
      <a href="Thorlabs%20Power%20Meter%20Instrument%20Driver.html">Thorlabs Power Meter Instrument Driver</a><br/>
      <a href="Alphabetical%20Function%20Index.html">Alphabetical Function Index</a><br/>
      <a href="Hierarchical%20Function%20Index.html">Hierarchical Function Index</a><br/>
    </p>
  </body>
</html>