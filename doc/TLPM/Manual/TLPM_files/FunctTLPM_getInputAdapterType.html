<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=ISO-8859-1">
    <link rel="stylesheet" href="htmlhelp.css" type="text/css">
    <title>TLPM_getInputAdapterType</title>
  </head>
  <body>
    <h1>TLPM_getInputAdapterType</h1>
    <p class="syntax">
      ViStatus TLPM_getInputAdapterType (ViSession instrumentHandle,
                                   ViPInt16 customSensorType);
    </p>
    <h2 class="purpose">Purpose</h2>
    <p class="body">
    This function returns the assumed sensor type for custom sensors without calibration data memory connected to the instrument.
    <br/>
    Notes:
    <br/>
    (1) The function is only available on PM100D, PM100A, PM100USB, PM200, PM400.
    </p>
    <h2>Parameters</h2>
    <table class="borderless" summary="Parameter help">
      <tr>
        <td valign="top"><em class="label">Input</em></td>
      </tr>
      <tr>
        <td class="tablehead">Name</td>
        <td class="tablehead">Type</td>
        <td class="tablehead">Description</td>
      </tr>
      <tr>
        <td class="paramName">instrumentHandle</td>
        <td class="paramDataType">ViSession</td>
        <td>
    This parameter accepts the instrument handle returned by &lt;Initialize&gt; to select the desired instrument driver session.
        </td>
      </tr>
      <tr>
        <td valign="top"><em class="label">Output</em></td>
      </tr>
      <tr>
        <td class="tablehead">Name</td>
        <td class="tablehead">Type</td>
        <td class="tablehead">Description</td>
      </tr>
      <tr>
        <td class="paramName">customSensorType</td>
        <td class="paramDataType">ViInt16 (passed by reference)</td>
        <td>
    This parameter returns the custom sensor type.
    <br/>
    Remark:
    <br/>
    The meanings of the obtained sensor type are:
    <br/>
    Sensor Types:
    <br/>
    SENSOR_TYPE_PD_SINGLE (1): Photodiode sensor
    <br/>
    SENSOR_TYPE_THERMO&nbsp;&nbsp;&nbsp;&nbsp;(2): Thermopile sensor
    <br/>
    SENSOR_TYPE_PYRO&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(3): Pyroelectric sensor
    <br/>
    SENSOR_TYPE_4Q&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(4): 4 Quadrant sensor
        </td>
      </tr>
    </table>
    <h2>Return Value</h2>
    <table class="borderless" summary="Return value help">
      <tr>
        <td class="tablehead">Name</td>
        <td class="tablehead">Type</td>
        <td class="tablehead">Description</td>
      </tr>
      <tr>
        <td class="paramName">status</td>
        <td class="paramDataType">ViStatus</td>
        <td>
    This is the error code returned by the function call. For error codes and descriptions see &lt;Error Message&gt;.
        </td>
      </tr>
    </table>
    <h2>References</h2>
    <p class="body">
      <a href="ClassInput.html">Input</a><br/>
      <a href="ClassConfigure.html">Configure</a><br/>
      <a href="ClassMeasure.html">Measure</a><br/>
      <a href="Thorlabs%20Power%20Meter%20Instrument%20Driver.html">Thorlabs Power Meter Instrument Driver</a><br/>
      <a href="Alphabetical%20Function%20Index.html">Alphabetical Function Index</a><br/>
      <a href="Hierarchical%20Function%20Index.html">Hierarchical Function Index</a><br/>
    </p>
  </body>
</html>