<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=ISO-8859-1">
    <link rel="stylesheet" href="htmlhelp.css" type="text/css">
    <title>TLPM_confCurrentMeasurementSequenceHWTrigger</title>
  </head>
  <body>
    <h1>TLPM_confCurrentMeasurementSequenceHWTrigger</h1>
    <p class="syntax">
      ViStatus TLPM_confCurrentMeasurementSequenceHWTrigger
             (ViSession instrumentHandle, ViUInt32 averagingLimit,
              ViUInt32 triggerHorizontalPosition);
    </p>
    <h2 class="purpose">Purpose</h2>
    <p class="body">
    This function send the SCPI Command &quot;CONF:ARR:HWTrig:CURR&quot; to the device.
    <br/>
    Then is possible to call the method 'getMeasurementSequenceHWTrigger' to get the current data.
    <br/>
    Set the bandwidth to high (setInputFilterState to OFF) and disable auto ranging ( setPowerAutoRange to OFF)
    <br/>
    PM101 special:
    <br/>
    This function send the SCPI Command &quot;CONF:ARR:CURR&quot; to the device.
    <br/>
    Then is possible to call the methods 'startMeasurementSequence' and 'getMeasurementSequenceHWTrigger' to get the current data.
    <br/>
    Note: The function is only available on PM103 and PM101 special.
    </p>
    <h2>Parameters</h2>
    <table class="borderless" summary="Parameter help">
      <tr>
        <td valign="top"><em class="label">Input</em></td>
      </tr>
      <tr>
        <td class="tablehead">Name</td>
        <td class="tablehead">Type</td>
        <td class="tablehead">Description</td>
      </tr>
      <tr>
        <td class="paramName">instrumentHandle</td>
        <td class="paramDataType">ViSession</td>
        <td>
    This parameter accepts the instrument handle returned by &lt;Initialize&gt; to select the desired instrument driver session.
        </td>
      </tr>
      <tr>
        <td class="paramName">averagingLimit</td>
        <td class="paramDataType">ViUInt32</td>
        <td>
    PM103:
    <br/>
    interval between two measurements in the array in µsec. The maximum resolution is defined in the device specifications.
    <br/>
    PM101 special:
    <br/>
    time to collect measurements.
        </td>
      </tr>
      <tr>
        <td class="paramName">triggerHorizontalPosition</td>
        <td class="paramDataType">ViUInt32</td>
        <td>
    PM103:
    <br/>
    Sets the horizontal position of trigger condition in the scope catpure (Between 1 and 9999)
    <br/>
    PM101 special:
    <br/>
    Interval between measurements.
        </td>
      </tr>
    </table>
    <h2>Return Value</h2>
    <table class="borderless" summary="Return value help">
      <tr>
        <td class="tablehead">Name</td>
        <td class="tablehead">Type</td>
        <td class="tablehead">Description</td>
      </tr>
      <tr>
        <td class="paramName">status</td>
        <td class="paramDataType">ViStatus</td>
        <td>
    This is the error code returned by the function call. For error codes and descriptions see &lt;Error Message&gt;.
        </td>
      </tr>
    </table>
    <h2>References</h2>
    <p class="body">
      <a href="ClassArray%20Measurement.html">Array Measurement</a><br/>
      <a href="ClassMeasure.html">Measure</a><br/>
      <a href="Thorlabs%20Power%20Meter%20Instrument%20Driver.html">Thorlabs Power Meter Instrument Driver</a><br/>
      <a href="Alphabetical%20Function%20Index.html">Alphabetical Function Index</a><br/>
      <a href="Hierarchical%20Function%20Index.html">Hierarchical Function Index</a><br/>
    </p>
  </body>
</html>