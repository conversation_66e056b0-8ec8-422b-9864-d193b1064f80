<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=ISO-8859-1">
    <link rel="stylesheet" href="htmlhelp.css" type="text/css">
    <title>TLPM_setPowerCalibrationPointsState</title>
  </head>
  <body>
    <h1>TLPM_setPowerCalibrationPointsState</h1>
    <p class="syntax">
      ViStatus TLPM_setPowerCalibrationPointsState (ViSession instrumentHandle,
                                              ViUInt16 index,
                                              ViBoolean state);
    </p>
    <h2 class="purpose">Purpose</h2>
    <p class="body">
    This function activates/inactivates the power calibration of this sensor.
    </p>
    <h2>Parameters</h2>
    <table class="borderless" summary="Parameter help">
      <tr>
        <td valign="top"><em class="label">Input</em></td>
      </tr>
      <tr>
        <td class="tablehead">Name</td>
        <td class="tablehead">Type</td>
        <td class="tablehead">Description</td>
      </tr>
      <tr>
        <td class="paramName">instrumentHandle</td>
        <td class="paramDataType">ViSession</td>
        <td>
    This parameter accepts the instrument handle returned by &lt;Initialize&gt; to select the desired instrument driver session.
        </td>
      </tr>
      <tr>
        <td class="paramName">index</td>
        <td class="paramDataType">ViUInt16</td>
        <td>
    Index of the power calibration (range 1...5)
        </td>
      </tr>
      <tr>
        <td class="paramName">state</td>
        <td class="paramDataType">ViBoolean</td>
        <td>
    State if the user power calibration is activated and used for the power measurements.
    <br/>
    VI_ON: The user power calibration is used
    <br/>
    VI_OFF: The user power calibration is ignored in the power measurements
        </td>
      </tr>
    </table>
    <h2>Return Value</h2>
    <table class="borderless" summary="Return value help">
      <tr>
        <td class="tablehead">Name</td>
        <td class="tablehead">Type</td>
        <td class="tablehead">Description</td>
      </tr>
      <tr>
        <td class="paramName">status</td>
        <td class="paramDataType">ViStatus</td>
        <td>
    This is the error code returned by the function call. For error codes and descriptions see &lt;Error Message&gt;.
        </td>
      </tr>
    </table>
    <h2>References</h2>
    <p class="body">
      <a href="ClassUser%20Power%20Calibration.html">User Power Calibration</a><br/>
      <a href="ClassConfigure.html">Configure</a><br/>
      <a href="ClassMeasure.html">Measure</a><br/>
      <a href="Thorlabs%20Power%20Meter%20Instrument%20Driver.html">Thorlabs Power Meter Instrument Driver</a><br/>
      <a href="Alphabetical%20Function%20Index.html">Alphabetical Function Index</a><br/>
      <a href="Hierarchical%20Function%20Index.html">Hierarchical Function Index</a><br/>
    </p>
  </body>
</html>