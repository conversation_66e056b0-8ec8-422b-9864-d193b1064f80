<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset=ISO-8859-1">
    <link rel="stylesheet" href="TLPM_files/htmlhelp.css" type="text/css">
    <title>Thorlabs Power Meter Instrument Driver</title>
  </head>
  <body>
    <h1>Thorlabs Power Meter Instrument Driver</h1>
    <p class="body">
    This instrument driver module provides programming support for the THORLABS PM100x/PM200 Series Optical Power Meter instruments.
    <br/>
    LICENSE:
    <br/>
    This software is Copyright © 2008-2014, Thorlabs.
    <br/>
    This library is free software; you can redistribute it and/or modify it under the terms of the GNU Lesser General Public License as published by the Free Software Foundation; either version 2.1 of the License, or (at your option) any later version.
    <br/>
    This library is distributed in the hope that it will be useful, but WITHOUT ANY WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.&nbsp;&nbsp;See the GNU Lesser General Public License for more details.
    <br/>
    You should have received a copy of the GNU Lesser General Public License along with this library; if not, write to the Free Software Foundation, Inc., 59 Temple Place, Suite 330, Boston, MA&nbsp;&nbsp;02111-1307&nbsp;&nbsp;USA
    </p>
    <h3>Classes</h3>
    <ul class="list-index">
    <li><a href="TLPM_files/ClassAttributes.html">Attributes</a></li>
    <li><a href="TLPM_files/ClassDigital%20I,O.html">Digital I/O</a></li>
    <li><a href="TLPM_files/ClassDigital%20I,O%20PM103.html">Digital I/O PM103</a></li>
    <li><a href="TLPM_files/ClassEthernet%20Interface.html">Ethernet Interface</a></li>
    <li><a href="TLPM_files/ClassMeasure.html">Measure</a></li>
    <li><a href="TLPM_files/ClassResource%20Functions.html">Resource Functions</a></li>
    <li><a href="TLPM_files/ClassStatus%20Register.html">Status Register</a></li>
    <li><a href="TLPM_files/ClassSystem.html">System</a></li>
    <li><a href="TLPM_files/ClassUtility%20Functions.html">Utility Functions</a></li>
    </ul>
    <h3>Functions</h3>
    <ul class="list-index">
    <li><a href="TLPM_files/FunctTLPM_close.html">TLPM_close</a></li>
    <li><a href="TLPM_files/FunctTLPM_init.html">TLPM_init</a></li>
    </ul>
    <h2>References</h2>
    <p class="body">
      <a href="TLPM_files/Alphabetical%20Function%20Index.html">Alphabetical Function Index</a><br/>
      <a href="TLPM_files/Hierarchical%20Function%20Index.html">Hierarchical Function Index</a><br/>
    </p>
  </body>
</html>