﻿/*========================================================================*//**

   \file          TLvisa.h
   \brief         Replacement library for VISA (TM) National Instruments.

   Thorlabs GmbH

   \date          Feb-07-2018
   \copyright     copyright(c) 2017-2018 Thorlabs GmbH
   \author        <PERSON><PERSON> (<EMAIL>),

*//*=========================================================================*/

#ifndef __TLVISA_HEADER__
#  define __TLVISA_HEADER__

// The code surrounded by the TLVISA_WITH_LOCKING_AND_EVENTS macros was
// introduced when porting TL-VISA to Deformable Mirror Driver
#  ifdef _WIN32
#    define TLVISA_WITH_LOCKING_AND_EVENTS
#  elif __unix__
//	we start the port to Linux without locking and events!
#  elif __RTX
#  endif  // _WIN32

// The code surrounded by the TLVISA_WITH_ASRL macros was
// introduced when extending TL-VISA for use with RS232 ports (serial ports) additional to USB ports
//	use either:
//	#define TLVISA_WITH_ASRL
//	or pass this to compiler via preprocessor directive
//	-DTLVISA_WITH_ASRL

#  include "visatype.h"

#  if defined(__cplusplus) || defined(__cplusplus__)
extern "C"
{
#  endif

/*========================================================================*//**
\defgroup   TLVISA_x  Thorlabs VISA Library TLVISA
\ingroup    LIBRARIES_x
\brief      VISA library to be used as a replacement for NI-VISA (TM)
\details    The API for this library is implemented as similar as possible to
            National Instruments VISA (TM) implementation
*//*=========================================================================*/

/*========================================================================*//**
\defgroup   TLVISA_API_x  TLVISA API (exported stuff)
\ingroup    TLVISA_x
\brief      This is what the user of the library will see.
\details    Find the documentation for the user API for TLVISA library in this
            chapter
*//*=========================================================================*/

/*========================================================================*//**
\defgroup   TLVISA_MACROS_x  Macros
\ingroup    TLVISA_API_x
\brief      Macros used in TLVISA library code.
*//*=========================================================================*/

/*========================================================================*//**
\defgroup   TLVISA_BUFFER_SIZE_x  Macros for buffer sizes used in TLVISA
\ingroup    TLVISA_MACROS_x
@{
*//*=========================================================================*/
#  define MAX_STATUS_DESCRIPTION    256  ///< buffer size to provide for \ref TL_viStatusDesc
#  define MAX_BUF_SIZE_DEVICE_DESCR 256  ///< buffer size to provide for \ref TL_viFindRsrc and \ref TL_viFindNext
#  define MAX_BUF_SIZE_RSRC         256  ///< buffer size for a TLVISA resource, aka string to provide to \ref TL_viOpen
#  define MAX_BUF_SIZE_SN           128  ///< buffer size for S/N read out from string descriptor
#  define MAX_BUF_SIZE_M            128  ///< buffer size for Manufacturer's Name read out from string descriptor
#  define MAX_BUF_SIZE_DEV          128  ///< buffer size for Device Name read out from string descriptor
#  define MAX_BUF_SIZE_ATTR         256  ///< buffer size for get/set Attribute, whe attribute is a string
#  define MAX_BUF_SIZE_PATTERN      256  ///< buffer size for TL_viFindRsrc string buffers
/**@}*/                                  /* TLVISA_BUFFER_SIZE_x */

/*========================================================================*//**
\defgroup   TLVISA_ERROR_CODES_x  Error codes for TLVISA functions
\ingroup    TLVISA_MACROS_x
@{
*//*=========================================================================*/
#  define TLVI_ERR_CTL_TRANSFER_STALLED (_VI_ERROR + 0x3FFF0200L) /* BFFF0200, -1073806848 */
#  define TLVI_LIBUSB_INTERRUPTED       (_VI_ERROR + 0x3FFF0201L) /* BFFF0201, -1073806847 */

#  define TLVI_NEEDS_EQUIVALENT     (_VI_ERROR + 0x3FFF0202L) /* BFFF0202, -1073806846 */
#  define TLVI_NEEDS_IMPLEMENTATION (_VI_ERROR + 0x3FFF0203L) /* BFFF0203, -1073806845 */
//	error codes introduced with TLlibserial
#  define TLVI_SETUPDI_ERR (_VI_ERROR + 0x3FFF0204L) /* BFFF0204, -1073806844 */
/**@}*/                                              /* TLVISA_ERROR_CODES_x */

/*========================================================================*//**
\defgroup   TLVISA_CONTROL_REN_CODES_x  Macros for parameter mode of \ref TL_viGpibControlREN
\ingroup    TLVISA_MACROS_x
@{
*//*=========================================================================*/
#  define VI_GPIB_REN_DEASSERT           (0)  ///< Deassert REN line.
#  define VI_GPIB_REN_ASSERT             (1)  ///< Assert REN line.
#  define VI_GPIB_REN_DEASSERT_GTL       (2)  ///< Send the Go To Local (GTL) command and deassert REN line.
#  define VI_GPIB_REN_ASSERT_ADDRESS     (3)  ///< Assert REN line and address device.
#  define VI_GPIB_REN_ASSERT_LLO         (4)  ///< Send LLO to any devices that are addressed to listen.
#  define VI_GPIB_REN_ASSERT_ADDRESS_LLO (5)  ///< Address this device and send it LLO, putting it in RWLS.
#  define VI_GPIB_REN_ADDRESS_GTL        (6)  ///< Send the Go To Local command (GTL) to this device.
  /**@}*/                                     /* TLVISA_CONTROL_REN_CODES_x */

  /*========================================================================*//**
\defgroup   TLVISA_ASRL_MACROSx  Macros for extension of TLVISA to async. serial aka RS232
\ingroup    TLVISA_MACROS_x
@{
*//*=========================================================================*/

#  ifdef TLVISA_WITH_ASRL

#    define VI_ASRL_WIRE_232_DTE \
      (128)  ///< only possible response for attribute VI_ATTR_ASRL_WIRE_MODE (unless using National Instruments (C) Hardware)
#    define TLVISA_ASRL_BREAK_LEN_MIN (1)    ///< minimal length for ASRL BREAK signal (on data line!)
#    define TLVISA_ASRL_BREAK_LEN_MAX (500)  ///< maximal length for ASRL BREAK signal (on data line!)
#    define TLVISA_ASRL_BREAK_LEN_DEF (250)  ///< default length for ASRL BREAK signal (on data line!)
#  endif                                     //	TLVISA_WITH_ASRL

  /**@}*/ /* TLVISA_ASRL_MACROSx */

  /*========================================================================*//**
\defgroup   TLVISA_TYPES_x  Types
\ingroup    TLVISA_API_x
\brief      Data types used in TLVISA library code.
*//*=========================================================================*/

  /*========================================================================*//**
\defgroup   TLVISA_TYPES_LEGACY_x  Types as used in NI-VISA (TM)
\ingroup    TLVISA_TYPES_x
@{
*//*=========================================================================*/
  typedef ViObject ViFindList;                 ///< used like a handle, is basically a 32bit unsigned integer
  typedef ViFindList _VI_PTR ViPFindList;      ///< pointer to a \ref ViFindList
  typedef ViUInt32 ViAccessMode;               ///< to be done
  typedef ViAccessMode _VI_PTR ViPAccessMode;  ///< pointer to a \ref ViAccessMode
#  if defined(__TL_COMPILE_64BIT__)
  typedef ViUInt64 ViAttrState;  ///< set/get attribute should be able to store a pointer -> we need 64bit here
#  elif defined __TL_COMPILE_32BIT__
typedef ViUInt32 ViAttrState;  ///< for 32bit environment a 32bit pointer is enough
#  else
#    error Unknown bittness!
#  endif

  // NEW added types used in MUN1180 Deformable Mirror
  typedef ViObject ViEvent;
  typedef ViEvent _VI_PTR ViPEvent;

  typedef ViUInt32 ViEventType;
  typedef ViEventType _VI_PTR ViPEventType;
  typedef ViEventType _VI_PTR ViAEventType;
  typedef ViUInt32 ViEventFilter;

  typedef ViString ViKeyId;
  typedef ViPString ViPKeyId;

  typedef ViStatus(_VI_FUNCH _VI_PTR ViHndlr)(ViSession vi, ViEventType eventType, ViEvent event, ViAddr userHandle);

  /**@}*/ /* TLVISA_TYPES_LEGACY_x */

  /*========================================================================*//**
\defgroup   TLVISA_METHODS_x Functions
\ingroup    TLVISA_API_x
\brief      Functions provided by TLVISA library, e.g. communicating with devices.
@{
*//*=========================================================================*/

  /*========================================================================*//**
\defgroup   TLVISA_METHODS_RM_x Resource Manager TLVISA library functions
\ingroup    TLVISA_METHODS_x
\brief      Functions provided by TLVISA library for managing resources, e.g. finding resources.
@{
*//*=========================================================================*/

  /*========================================================================*//**
   \brief         TLvisa equivalent to NI VISA (TM) viOpenDefaultRM()
   \details       Returns a session to the Default Resource Manager resource.
                  The Default Resource Manager is needed to open a session to
                  any device. The Resource Manager is capable
                  (through \ref TL_viFindRsrc and \ref TL_viFindNext) to locate
                  all resources currently on the system.\n
                  \n
                  When a Resource Manager session is passed to viClose(), not
                  only is that session closed, but also all find lists and
                  device sessions (which that Resource Manager session was
                  used to create) are closed.
   \param[out]    vi Unique session to the Default Resource Manager.
   \return        Reports the status of a VISA operation.
                  A textual description of the returned Status Code may be obtained
                  by calling the \ref TL_viStatusDesc function.
*//*=========================================================================*/
  ViStatus _VI_FUNC TL_viOpenDefaultRM(ViPSession vi);

  /*========================================================================*//**
   \brief         TLvisa equivalent to NI VISA (TM) viFindRsrc()
   \details       Matches the value specified in 'expr' with all known
                  instruments. On successful completion, 'retCnt' is the
                  total number of matches and 'desc' is the descriptor string
                  for the first instrument that matches 'expr'.  'desc' can be
                  used to open an instrument with the \ref TL_viOpen operation.
                  The Find Handle 'vi' can be used in subsequent calls to the
                  \ref TL_viFindNext operation to retrieve descriptor strings
                  for the remaining instruments that were found.
                  This operation applies to TLVISA resource manager sessions only.

   \param[in]     sesn ViSession Rsrc_Manager_Handle
                  Session to a Resource Manager.  This should always be a session
                  returned from \ref TL_viOpenDefaultRM.

   \param[in]     expr The TL_viFindRsrc operation matches the value specified in
                  'expr' with all known resources. The 'expr' parameter can
                  contain a logical expression enclosed in {} that specifies
                  attributes to match.\n
                  \n
                  The search criteria specified in 'expr' parameter has two parts:
                  a regular expression over a resource string, and an optional logical
                  expression over attribute values. The regular expression is matched
                  against the resource strings of resources known to the VISA Resource
                  Manager. If the resource string matches the regular expression, the
                  attribute values of the resource are then matched against the
                  expression over attribute values. If the match is successful, the
                  resource has met the search criteria and gets added to the list of
                  resources found. All resource strings returned by
                  \ref TL_viFindRsrc() will always be recognized by \ref TL_viOpen().\n
                  \n
                  By using the optional attribute expression, you can construct
                  flexible and powerful expressions with the use of logical ANDs (&&),
                  ORs(||), and NOTs (!). You can use equal (==) and unequal (!=)
                  comparators to compare attributes of any type, and other inequality
                  comparators (>, <, >=, <=) to compare attributes of numeric type.
                  Use only global attributes in the attribute expression. Local
                  attributes are not allowed in the logical expression part of
                  the 'expr' parameter.\n
                  \n
                  \n

                  |Attribute                  |    Explanation                   |
                  |---------------------------|----------------------------------|
                  |"VI_ATTR_MANF_ID"          |    the USB vendor ID, VID        |
                  |"VI_ATTR_MODEL_CODE"       |  the USB product ID, PID         |
                  |"VI_ATTR_USB_SERIAL_NUM"   |  the serial number of the device |
                  |"VI_ATTR_USB_CLASS"        |  the USB class                   |
                  |"VI_ATTR_USB_SUBCLASS"     |  the USB subclass                |
                  |"VI_ATTR_USB_PROTOCOL"     |  the USB protocol                |

                  \n
                  Example:
                  "USB?*?{VI_ATTR_MANF_ID==0x1313 && VI_ATTR_MODEL_CODE==0x8011}"\n
                  \n
                  This finds all devices with VID/PID combination of 0x1313/0x8011

   \param[out]    vi A new object created by TLVISA that refers to this find list.
                  This object handle can be used to find the remaining resources by
                  invoking \ref TL_viFindNext. When you are finished with the find
                  list handle, you should deallocate it by passing it to
                  \ref TL_viClose.
                  If you need only the first match, this parameter can be VI_NULL.

   \param[out]    retCnt The number of matches found. If the number of matches is not
                  important (for example, if you need only the first match), this
                  parameter can be VI_NULL.

   \param[out]    desc The descriptor string for the first instrument that matches
                  'expr'.  This string can be passed to \ref TL_viOpen to establish
                  a session to the given device.\n
                  \n
                  Note:  String declaration size must be at least
                  MAX_BUF_SIZE_DEVICE_DESCR (256) bytes.

   \return        Reports the status of a VISA operation.
                  A textual description of the returned Status Code may be obtained
                  by calling the \ref TL_viStatusDesc function.

*//*=========================================================================*/
  ViStatus _VI_FUNC
  TL_viFindRsrc(ViSession sesn, ViString expr, ViPFindList vi, ViPUInt32 retCnt, ViChar _VI_FAR desc[]);

  /*========================================================================*//**
   \brief         TLvisa equivalent to NI VISA (TM) viFindNext()
   \details       Returns the next instrument descriptor that matched the
                  expression from the operation \ref TL_viFindRsrc.\n
                  \n
                  This operation applies to NIVISA resource manager sessions only.

   \param[in]     vi A reference to a find list. This must have been returned
                  by the \ref TL_viFindRsrc operation.

   \param[out]    desc The descriptor string for the next instrument that matches
                  'expr' from \ref TL_viFindRsrc. This string can be passed to
                  \ref TL_viOpen to establish a session to the given device.\n
                  \n
                  Note:  String declaration size must be at least
                  MAX_BUF_SIZE_DEVICE_DESCR (256) bytes.

   \return        Reports the status of a VISA operation.
                  A textual description of the returned Status Code may be obtained
                  by calling the \ref TL_viStatusDesc function.

*//*=========================================================================*/
  ViStatus _VI_FUNC TL_viFindNext(ViFindList vi, ViChar _VI_FAR desc[]);

  /*========================================================================*//**
   \brief         TLvisa equivalent to NI VISA (TM) viOpen()
   \details       The TL_viOpen operation is used to establish communication
                  with a remote instrument. Based on the Instrument Descriptor,
                  the TL_viOpen operation establishes a communication session
                  with a device and creates an Instrument Handle that is used
                  by all other TLVISA operations to perform operations on
                  that session.\n
                  \n
                  This operation applies to TLVISA resource manager sessions only.

   \param[in]     sesn Session to a Resource Manager.
                  This must be a session returned from \ref TL_viOpenDefaultRM.

   \param[in]     name This parameter specifies the resource with which to
                  establish a communication session. The syntax for the Instrument
                  Descriptor is shown below. Optional segments are shown in square
                  brackets ([]).\n
                  \n
                  |Interface |  Syntax                                                          |
                  |----------|------------------------------------------------------------------|
                  |USB INSTR | \code USB[board]::vid::pid::serial number[::interface number][::INSTR] \endcode |
                  |USB RAW   | \code USB[board]::vid::pid::serial number[::interface number]::RAW     \endcode |
                  \n
                  The default values for optional parameters are shown below.\n
                  \n
                  |Optional Segment       |  Default Value  |
                  |-----------------------|-----------------|
                  |board                  |  0              |
                  |USB interface number   |  0              |
                  \n
                  Example Resource Strings:\n
                  \code USB::0x1234::0xABCD::A123B456C::INSTR \endcode\n
                  A USB TMC device using a vendor ID of 1234 hex, a product ID of ABCD hex,
                  a serial number of A123B456C, and the first USB TMC interface.\n
                  \n
                  \code USB::0xCDEF::0x4567::R789Q321P::2::RAW \endcode \n
                  Raw USB access to a device using a vendor ID of CDEF hex, a product ID of
                  4567 hex, a serial number of R789Q321P, and interface number 2.

   \param[in]     mode If this parameter is VI_EXCLUSIVE_LOCK, the resource will be locked
                  before the operation returns. If the resource cannot be locked, then the
                  operation waits up to the time specified by the timeout parameter before
                  returning an error.\n

   \param[in]     timeout This is the maximum time to wait when trying to open a resource.
                  This does not set the I/O timeout. To set the I/O timeout you must call
                  \ref TL_viSetAttribute with the attribute VI_ATTR_TMO_VALUE.\n
                  \n
                  The timeout value is given in ms.

   \param[out]    vi A unique identifier reference to a device I/O session. The Instrument
                  Handle is used by all TLVISA operations to perform operations
                  on a specific session.

   \return        Reports the status of a VISA operation.
                  A textual description of the returned Status Code may be obtained
                  by calling the \ref TL_viStatusDesc function.

*//*=========================================================================*/
  ViStatus _VI_FUNC TL_viOpen(ViSession sesn, ViRsrc name, ViAccessMode mode, ViUInt32 timeout, ViPSession vi);

  /**@}*/ /* TLVISA_METHODS_RM_x */

  /*========================================================================*//**
\defgroup   TLVISA_METHODS_RM_TEMPLATE_x Resource Manager Template Operations
\ingroup    TLVISA_METHODS_x
\brief      Functions provided by TLVISA library for basic device operation,
            e.g. open/close a connection to a device.
@{
*//*=========================================================================*/

  /*========================================================================*//**
   \brief         TLvisa equivalent to NI VISA (TM) viClose()
   \details       Closes the specified device I/O session or resource manager
                  session. This operation deallocates all system resources
                  allocated to the Session Handle. The same operation is also
                  used to close handles to other types of VISA objects.\n
                  \n
                  Notice that if a resource manager session is closed, all
                  TLVISA sessions opened with the corresponding resource manager
                  session are also closed. Therefore, an application should
                  usually close resource manager sessions only when it is about
                  to exit.

   \param[in]     vi A unique identifier reference to a device I/O session,
                  event, or find list. An Object Handle is used by all VISA
                  operations to perform operations on a specific object.

   \return        Reports the status of a VISA operation.
                  A textual description of the returned Status Code may be obtained
                  by calling the \ref TL_viStatusDesc function.
*//*=========================================================================*/
  ViStatus _VI_FUNC TL_viClose(ViObject vi);

  /*========================================================================*//**
   \brief         TLvisa equivalent to NI VISA (TM) viSetAttribute()
   \details       Sets a specified attribute for the given object.
                  Attributes have values of different data types with different
                  valid ranges. Because of this, a list of attributes, their
                  data types and valid values are provided in the Help for
                  "Attribute Value."\n
                  \n
                  Some attributes are not settable by the user, and are denoted
                  as such in the Help for "Attribute Value".\n
                  \n
                  This operation applies to all VISA session types.

   \param[in]     vi Unique logical identifier to a session.

   \param[in]     attrName Specifies the attribute whose value you want to set.

   \param[in]     attrValue The value to which to set the specified attribute.

   \return        Reports the status of a VISA operation.
                  A textual description of the returned Status Code may be obtained
                  by calling the \ref TL_viStatusDesc function.

*//*=========================================================================*/
  ViStatus _VI_FUNC TL_viSetAttribute(ViObject vi, ViAttr attrName, ViAttrState attrValue);

  /*========================================================================*//**
   \brief         TLvisa equivalent to NI VISA (TM) viGetAttribute()
   \details       Queries the specified attribute for a given object.
                  Since attributes are of values of different data types with
                  different valid ranges, a list of attributes, their data types
                  and valid values are provided in the Help for "Attribute Value."\n
                  \n
                  This operation applies to all VISA object types.

   \param[in]     vi Unique logical identifier to a session, find list, or event.

   \param[in]     attrName Specifies the attribute whose value you want to obtain.

   \param[out]    attrValue The value of the specified attribute.
                  This parameter must be of the type of the attribute actually being
                  retrieved. For example, when retrieving an attribute that is defined
                  as a ViBoolean, your application should pass a reference to a
                  variable of type ViBoolean. Similarly, if the attribute is defined
                  as being ViUInt32, your application should pass a reference to a
                  variable of type ViUInt32.

   \return        Reports the status of a VISA operation.
                  A textual description of the returned Status Code may be obtained
                  by calling the \ref TL_viStatusDesc function.

*//*=========================================================================*/
  ViStatus _VI_FUNC TL_viGetAttribute(ViObject vi, ViAttr attrName, void _VI_PTR attrValue);

  /*========================================================================*//**
   \brief         TLvisa equivalent to NI VISA (TM) viStatusDesc()
   \details       Returns a user-readable description of the status code
                  passed to the operation.

   \param[in]     vi Unique logical identifier to a session, event, or find list.

   \param[in]     status Status value to interpret. This can be a value returned
                  from any TLVISA operation.

   \param[out]    desc Returns a user-readable description of the status code
                  passed to the operation.\n
                  Note:\n
                  String declaration size must be at least
                  MAX_STATUS_DESCRIPTION (256) bytes.

   \return        Reports the status of a VISA operation.
                  A textual description of the returned Status Code may be obtained
                  by calling the \ref TL_viStatusDesc function.
*//*=========================================================================*/
  ViStatus _VI_FUNC TL_viStatusDesc(ViObject vi, ViStatus status, ViChar _VI_FAR desc[]);

#  ifdef TLVISA_WITH_LOCKING_AND_EVENTS

  /*========================================================================*//**
   \brief         TLvisa equivalent to NI VISA (TM) viLock()
   \details       This operation is used to obtain a lock on the specified
                  resource.  The caller can specify the type of lock requested
                  (exclusive or shared) and the length of time the operation
                  will suspend while waiting to acquire the lock before timing
                  out.
                  This operation can also be used for sharing and nesting locks.\n\n

                  This operation applies to all VISA session types.

   \param[in]     vi Unique logical identifier to a session.

   \param[in]     lockType the type of lock requested on the given resource.
                  Valid lock types are: VI_EXCLUSIVE_LOCK, VI_SHARE_LOCK.

   \param[in]     timeout the maximum time to wait (in milliseconds)
                  to acquire a lock on the resource. Special values are:\n
                  VI_TMO_IMMEDIATE (just try to lock it, do not wait)\n
                  VI_TMO_INFINITE  (wait forever, do not fail)

   \param[in]     requestedKey this string parameter specifies the requested
                  key and is used only when requesting a shared lock.  If the
                  operation is successful, the key can be passed to other
                  sessions for lock sharing. If not specified,
                  VISA will generate a unique key.

   \param[out]    accessKey the actual access key to the resource associated
                  with the Instrument Handle session. Other sessions can pass
                  this key as the RequestedKey to share the lock on the resource.\n\n

                  If the lock being requested is an exclusive lock, this parameter
                  can be VI_NULL.\n\n

                  Note: String declaration size must be at least 256 bytes.

   \return        Reports the status of a VISA operation.
                  A textual description of the returned Status Code may be obtained
                  by calling the \ref TL_viStatusDesc function.

                  \n
                  | Status | Description                                            | Mnemonic                    |
                  |--------|--------------------------------------------------------|-----------------------------|
                  |       0| No error (the call was successful).                    | VI_SUCCESS                  |
                  |3FFF0099| Current session has nested shared locks.               | VI_SUCCESS_NESTED_SHARED    |
                  |3FFF009A| Current session has nested exclusive locks.            | VI_SUCCESS_NESTED_EXCLUSIVE |
                  |BFFF0000| Miscellaneous or system error occurred.                | VI_ERROR_SYSTEM_ERROR       |
                  |BFFF000E| Invalid object or session handle.                      | VI_ERROR_INV_OBJECT         |
                  |BFFF000F| Resource locked for specified access.                  | VI_ERROR_RSRC_LOCKED        |
                  |BFFF0015| Could not acquire lock within the timeout period.      | VI_ERROR_TMO                |
                  |BFFF0020| Invalid lock type.                                     | VI_ERROR_INV_LOCK_TYPE      |
                  |BFFF0021| The access key is invalid for access to the resource.  | VI_ERROR_INV_ACCESS_KEY     |
                  |BFFF0071| The user buffer is not valid for the required size.    | VI_ERROR_USER_BUF           |
                  \n

*//*=========================================================================*/
  ViStatus _VI_FUNC
  TL_viLock(ViSession vi, ViAccessMode lockType, ViUInt32 timeout, ViKeyId requestedKey, ViChar _VI_FAR accessKey[]);

  /*========================================================================*//**
   \brief         TLvisa equivalent to NI VISA (TM) viUnlock()
   \details       This operation is used to obtain a lock on the specified
                  resource.  The caller can specify the type of lock requested
                  (exclusive or shared) and the length of time the operation
                  will suspend while waiting to acquire the lock before timing
                  out.
                  This operation can also be used for sharing and nesting locks.\n\n

                  This operation applies to all VISA session types.

   \param[in]     vi Unique logical identifier to a session.

   \return        Reports the status of a VISA operation.
                  A textual description of the returned Status Code may be obtained
                  by calling the \ref TL_viStatusDesc function.

                  \n
                  | Status | Description                                            | Mnemonic                    |
                  |--------|--------------------------------------------------------|-----------------------------|
                  |       0| No error (the call was successful).                    | VI_SUCCESS                  |
                  |3FFF0099| Current session has nested shared locks.               | VI_SUCCESS_NESTED_SHARED    |
                  |3FFF009A| Current session has nested exclusive locks.            | VI_SUCCESS_NESTED_EXCLUSIVE |
                  |BFFF0000| Miscellaneous or system error occurred.                | VI_ERROR_SYSTEM_ERROR       |
                  |BFFF000E| Invalid object or session handle.                      | VI_ERROR_INV_OBJECT         |
                  |BFFF009C| Current session did not have any lock on the resource. | VI_ERROR_SESN_NLOCKED       |
                  \n

*//*=========================================================================*/
  ViStatus _VI_FUNC TL_viUnlock(ViSession vi);

  /*========================================================================*//**
   \brief         TLvisa equivalent to NI VISA (TM) viEnableEvent()
   \details       Enables notification of a specified event type for the
                  specified event mechanism(s) for a given session.\n\n

                  This operation applies to all VISA session types.

   \param[in]     vi Unique logical identifier to a session.

   \param[in]     eventType Logical event identifier. Valid values are:\n
                  VI_ALL_ENABLED_EVENTS\n
                  VI_EVENT_USB_INTR

   \param[in]     mechanism Specifies event handling mechanisms to be enabled.
                  Valid values are: VI_QUEUE, VI_HNDLR or VI_SUSPEND_HNDLR.\n\n

                  TLVISA does not support enabling both the queue and the handler
                  for the same event type on the same session. If you need to use
                  both mechanisms for the same event type, you should open multiple
                  sessions to the resource.


   \param[in]     context This is reserved for future definition.
                  Currently this parameter should always be VI_NULL.

   \return        Reports the status of a VISA operation.
                  A textual description of the returned Status Code may be obtained
                  by calling the \ref TL_viStatusDesc function.

                  \n
                  | Status | Description                                   | Mnemonic                    |
                  |--------|-----------------------------------------------|-----------------------------|
                  |       0| No error (the call was successful).           | VI_SUCCESS                  |
                  |3FFF0002| Specified event type is already enabled.      | VI_SUCCESS_EVENT_EN         |
                  |BFFF0000| Miscellaneous or system error occurred.       | VI_ERROR_SYSTEM_ERROR       |
                  |BFFF000E| Invalid object or session handle.             | VI_ERROR_INV_OBJECT         |
                  |BFFF0026| Invalid event type.                           | VI_ERROR_INV_EVENT          |
                  |BFFF0027| Invalid event mechanism.                      | VI_ERROR_INV_MECH           |
                  |BFFF0028| No handler is installed for the event type.   | VI_ERROR_HNDLR_NINSTALLED   |
                  |BFFF002A| Invalid event filter context.                 | VI_ERROR_INV_CONTEXT        |
                  |BFFF0067| Operation is not supported on this session.   | VI_ERROR_NSUP_OPER          |
                  \n

*//*=========================================================================*/
  ViStatus _VI_FUNC TL_viEnableEvent(ViSession vi, ViEventType eventType, ViUInt16 mechanism, ViEventFilter context);

  /*========================================================================*//**
   \brief         TLvisa equivalent to NI VISA (TM) viDisableEvent()
   \details       Disables notification of the specified event type(s) for
                  the specified event mechanism(s) for a given session.\n\n

                  This operation applies to all VISA session types.

   \param[in]     vi Unique logical identifier to a session.

   \param[in]     eventType Logical event identifier. Valid values are:\n
                  VI_ALL_ENABLED_EVENTS\n
                  VI_EVENT_USB_INTR

   \param[in]     mechanism Specifies event handling mechanisms to be disabled.
                  Valid values are: VI_QUEUE, VI_HNDLR, VI_SUSPEND_HNDLR or
                  VI_ALL_MECH.\n\n

   \return        Reports the status of a VISA operation.
                  A textual description of the returned Status Code may be obtained
                  by calling the \ref TL_viStatusDesc function.

                  \n
                  | Status | Description                                   | Mnemonic              |
                  |--------|-----------------------------------------------|-----------------------|
                  |       0| No error (the call was successful).           | VI_SUCCESS            |
                  |3FFF0003| Specified event type is already disabled.     | VI_SUCCESS_EVENT_DIS  |
                  |BFFF0000| Miscellaneous or system error occurred.       | VI_ERROR_SYSTEM_ERROR |
                  |BFFF000E| Invalid object or session handle.             | VI_ERROR_INV_OBJECT   |
                  |BFFF0026| Invalid event type.                           | VI_ERROR_INV_EVENT    |
                  |BFFF0027| Invalid event mechanism.                      | VI_ERROR_INV_MECH     |
                  |BFFF0067| Operation is not supported on this session.   | VI_ERROR_NSUP_OPER    |
                  \n

*//*=========================================================================*/
  ViStatus _VI_FUNC TL_viDisableEvent(ViSession vi, ViEventType eventType, ViUInt16 mechanism);

  /*========================================================================*//**
   \brief         TLvisa equivalent to NI VISA (TM) viDiscardEvents()
   \details       Discards event occurrences of the specified event type(s)
                  for the specified event mechanism(s) for a given session.\n\n

                  This operation applies to all VISA session types.

   \param[in]     vi Unique logical identifier to a session.

   \param[in]     eventType Logical event identifier. Valid values are:\n
                  VI_ALL_ENABLED_EVENTS\n
                  VI_EVENT_USB_INTR

   \param[in]     mechanism Specifies event handling mechanisms to be discarded.
                  Valid values are: VI_QUEUE or VI_SUSPEND_HNDLR.
                  It is possible to discard both mechanisms simultaneously
                  by specifying the bit-wise OR of VI_QUEUE and VI_SUSPEND_HNDLR.

   \return        Reports the status of a VISA operation.
                  A textual description of the returned Status Code may be obtained
                  by calling the \ref TL_viStatusDesc function.

                  \n
                  | Status | Description                                      | Mnemonic                 |
                  |--------|--------------------------------------------------|--------------------------|
                  |       0| No error (the call was successful).              | VI_SUCCESS               |
                  |3FFF0004| Queue was already empty for specified mechanism. | VI_SUCCESS_QUEUE_EMPTY   |
                  |BFFF0000| Miscellaneous or system error occurred.          | VI_ERROR_SYSTEM_ERROR    |
                  |BFFF000E| Invalid object or session handle.                | VI_ERROR_INV_OBJECT      |
                  |BFFF0026| Invalid event type.                              | VI_ERROR_INV_EVENT       |
                  |BFFF0027| Invalid event mechanism.                         | VI_ERROR_INV_MECH        |
                  |BFFF0067| Operation is not supported on this session.      | VI_ERROR_NSUP_OPER       |
                  \n
*//*=========================================================================*/
  ViStatus _VI_FUNC TL_viDiscardEvents(ViSession vi, ViEventType eventType, ViUInt16 mechanism);

  /*========================================================================*//**
   \brief         TLvisa equivalent to NI VISA (TM) viWaitOnEvent()
   \details       Waits for an occurrence of the specified event type(s)
                  for a given session.\n\n

                  This operation applies to all VISA session types.

   \param[in]     vi Unique logical identifier to a session.

   \param[in]     inEventType Logical event identifier. Valid values are:\n
                  VI_ALL_ENABLED_EVENTS\n
                  VI_EVENT_USB_INTR

   \param[in]     timeout the maximum time to wait (in milliseconds) for an
                  event of the specified type to occur. Special values are:\n
                  VI_TMO_IMMEDIATE (dequeue only, do not wait)\n
                  VI_TMO_INFINITE  (wait forever, do not fail)

   \param[out]    outEventType Logical identifier of the event type actually received.
                  Beginning with VISA 1.1, if you do not need to know the actual event
                  type received (for example, if you specified one event type as an
                  input), you may pass VI_NULL for this parameter.

   \param[out]    outContext A handle specifying the unique occurrence of an event.
                  You can use this event handle to query attributes about the event
                  (such as the status/ID value for VXI signals or the line on which
                  a trigger occurred) by invoking viGetAttribute. When you are
                  finished with the event handle, you should deallocate it by passing
                  it to viClose. Beginning with VISA 1.1, if you do not need to use
                  the actual event occurrence (for example, if you just need to know
                  whether the event was received), you may pass VI_NULL for this
                  parameter. In this case, VISA will call viClose on the event for you.

   \return        Reports the status of a VISA operation.
                  A textual description of the returned Status Code may be obtained
                  by calling the \ref TL_viStatusDesc function.

                  \n
                  | Status | Description                                         | Mnemonic                 |
                  |--------|-----------------------------------------------------|--------------------------|
                  |       0| No error (the call was successful).                 | VI_SUCCESS               |
                  |3FFF0080| At least one more event remains in the queue.       | VI_SUCCESS_QUEUE_NEMPTY  |
                  |BFFF0000| Miscellaneous or system error occurred.             | VI_ERROR_SYSTEM_ERROR    |
                  |BFFF000E| Invalid object or session handle.                   | VI_ERROR_INV_OBJECT      |
                  |BFFF0015| Operation timed out before an event arrived.        | VI_ERROR_TMO             |
                  |BFFF0026| Invalid event type.                                 | VI_ERROR_INV_EVENT       |
                  |BFFF002D| Event queue for the specified type has overflowed.  | VI_ERROR_QUEUE_OVERFLOW  |
                  |BFFF0067| Operation is not supported on this session.         | VI_ERROR_NSUP_OPER       |
                  \n

*//*=========================================================================*/
  ViStatus _VI_FUNC TL_viWaitOnEvent(ViSession vi,
                                     ViEventType inEventType,
                                     ViUInt32 timeout,
                                     ViPEventType outEventType,
                                     ViPEvent outContext);

  /*========================================================================*//**
   \brief         TLvisa equivalent to NI VISA (TM) viInstallHandler()
   \details       The TL_viInstallHandler() operation allows applications to
                  install handlers on sessions. The handler specified in the
                  handler parameter is installed along with any previously
                  installed handlers for the specified event. Applications can
                  specify a value in the userHandle parameter that is passed to
                  the handler on its invocation. TLVISA identifies handlers
                  uniquely using the handler reference and this value.\n\n

                  TLVISA allows applications to install multiple handlers for
                  an event type on the same session. You can install multiple
                  handlers through multiple invocations of the
                  viInstallHandler() operation. If more than one handler is
                  installed for an event type, each handler is invoked on every
                  occurrence of the specified event(s). TLVISA specifies that
                  handlers are invoked in Last In First Out (LIFO) order.\n\n

                  Related Items: See the \ref TL_viEnableEvent and
                  \ref TL_viUninstallHandler descriptions.

                  This operation applies to all VISA session types.

   \param[in]     vi Unique logical identifier to a session.

   \param[in]     eventType Logical event identifier. Valid values are:\n
                  VI_ALL_ENABLED_EVENTS\n
                  VI_EVENT_USB_INTR

   \param[in]     handler Interpreted as a valid reference to a handler to be
                  installed by a client application.

   \param[in]     userHandle A value specified by an application that can be used
                  for identifying handlers uniquely for an event type

   \return        Reports the status of a VISA operation.
                  A textual description of the returned Status Code may be obtained
                  by calling the \ref TL_viStatusDesc function.

                  \n
                  | Status | Description                                         | Mnemonic                    |
                  |--------|-----------------------------------------------------|-----------------------------|
                  |       0| No error (the call was successful).                 | VI_SUCCESS                  |
                  |BFFF0000| Miscellaneous or system error occurred.             | VI_ERROR_SYSTEM_ERROR       |
                  |BFFF000E| Invalid object or session handle.                   | VI_ERROR_INV_OBJECT         |
                  |BFFF0026| Invalid event type.                                 | VI_ERROR_INV_EVENT          |
                  |BFFF0028| Handler was not installed for the event type.       | VI_ERROR_HNDLR_NINSTALLED   |
                  |BFFF0029| Invalid handler reference.                          | VI_ERROR_INV_HNDLR_REF      |
                  |BFFF0067| Operation is not supported on this session.         | VI_ERROR_NSUP_OPER          |
                  \n

*//*=========================================================================*/
  ViStatus _VI_FUNC TL_viInstallHandler(ViSession vi, ViEventType eventType, ViHndlr handler, ViAddr userHandle);

  /*========================================================================*//**
   \brief         TLvisa equivalent to NI VISA (TM) viUninstallHandler()
   \details       The TL_viUninstallHandler() operation allows applications to
                  uninstall handlers for events on sessions. Applications
                  should also specify the value in the User Handle parameter
                  that was passed while installing the handler. TLVISA
                  identifies handlers uniquely using the handler reference and
                  this value. All the handlers, for which the handler reference
                  and the value matches, are uninstalled. Specifying VI_ANY_HNDLR
                  as the value for the handler parameter causes the operation to
                  uninstall all the handlers with the matching value in the
                  User Handle parameter.\n\n

                  Related Items: See the \ref TL_viInstallHandler description.

                  This operation applies to all VISA session types.

   \param[in]     vi Unique logical identifier to a session.

   \param[in]     eventType Logical event identifier. Valid values are:\n
                  VI_ALL_ENABLED_EVENTS\n
                  VI_EVENT_USB_INTR

   \param[in]     handler Interpreted as a valid reference to a handler to be
                  uninstalled by a client application.

   \param[in]     userHandle A value specified by an application that can be used
                  for identifying handlers uniquely in a session for an event.

   \return        Reports the status of a VISA operation.
                  A textual description of the returned Status Code may be obtained
                  by calling the \ref TL_viStatusDesc function.

                  \n
                  | Status | Description                                         | Mnemonic                    |
                  |--------|-----------------------------------------------------|-----------------------------|
                  |       0| No error (the call was successful).                 | VI_SUCCESS                  |
                  |BFFF0000| Miscellaneous or system error occurred.             | VI_ERROR_SYSTEM_ERROR       |
                  |BFFF000E| Invalid object or session handle.                   | VI_ERROR_INV_OBJECT         |
                  |BFFF0026| Invalid event type.                                 | VI_ERROR_INV_EVENT          |
                  |BFFF0028| Handler was not installed for the event type.       | VI_ERROR_HNDLR_NINSTALLED   |
                  |BFFF0029| Invalid handler reference.                          | VI_ERROR_INV_HNDLR_REF      |
                  |BFFF0067| Operation is not supported on this session.         | VI_ERROR_NSUP_OPER          |
                  \n

*//*=========================================================================*/
  ViStatus _VI_FUNC TL_viUninstallHandler(ViSession vi, ViEventType eventType, ViHndlr handler, ViAddr userHandle);

#  endif  // TLVISA_WITH_LOCKING_AND_EVENTS

  /**@}*/ /* TLVISA_METHODS_RM_TEMPLATE_x */

  /*========================================================================*//**
\defgroup   TLVISA_METHODS_BASIC_IO_x Basic Device I/O Operations
\ingroup    TLVISA_METHODS_x
\brief      Functions provided by TLVISA library for basic device communication,
            e.g. read/write via a connection to a device.
@{
*//*=========================================================================*/

  /*========================================================================*//**
   \brief         TLvisa equivalent to NI VISA (TM) viRead()
   \details       Reads data from a device. The data read is stored in the
                  specified Buffer. This operation returns when the transfer
                  terminates.\n
                  \n
                  Original:\n
                  This operation applies to the VISA INSTR session type.\n
                  This implementation:\n
                  This operation applies to USB INSTR as well as USB RAW
                  session types. In case of a RAW session type the function
                  will get the data from the specified USB BULK IN pipe.

   \param[in]     vi Unique logical identifier to a session.

   \param[out]    buf Data read from the instrument.

   \param[in]     cnt Number of bytes to read.

   \param[out]    retCnt Number of bytes actually transferred. If you do not
                  need to know the actual number of bytes transferred
                  (for example, if you need to know only whether this operation
                  succeeded), you may pass VI_NULL for this parameter.

   \return        Reports the status of a VISA operation.
                  A textual description of the returned Status Code may be obtained
                  by calling the \ref TL_viStatusDesc function.

*//*=========================================================================*/
  ViStatus _VI_FUNC TL_viRead(ViSession vi, ViPBuf buf, ViUInt32 cnt, ViPUInt32 retCnt);

  /*========================================================================*//**
   \brief         TLvisa equivalent to NI VISA (TM) viWrite()
   \details       Writes data to a device. The data to be written is in the
                  specified Buffer. This operation returns when the transfer
                  terminates.\n
                  \n
                  This operation applies to the VISA INSTR session type.

   \param[in]     vi Unique logical identifier to a session.

   \param[out]    buf Data to send to the instrument.

   \param[in]     cnt Number of bytes to write.

   \param[out]    retCnt Number of bytes actually transferred. If you do not need
                  to know the actual number of bytes transferred (for example,
                  if you need to know only whether this operation succeeded),
                  you may pass VI_NULL for this parameter.

   \return        Reports the status of a VISA operation.
                  A textual description of the returned Status Code may be obtained
                  by calling the \ref TL_viStatusDesc function.

*//*=========================================================================*/
  ViStatus _VI_FUNC TL_viWrite(ViSession vi, ViBuf buf, ViUInt32 cnt, ViPUInt32 retCnt);

  /*========================================================================*//**
   \brief         TLvisa equivalent to NI VISA (TM) viReadSTB()
   \details       Reads the status byte of a message-based device. The result is
                  equivalent to the device's response to a "*STB?\n" query.\n
                  \n
                  This operation applies to the VISA INSTR session type.

   \param[in]     vi Unique logical identifier to a session.

   \param[out]    status The status byte read from the instrument.

   \return        Reports the status of a VISA operation.
                  A textual description of the returned Status Code may be obtained
                  by calling the \ref TL_viStatusDesc function.

*//*=========================================================================*/
  ViStatus _VI_FUNC TL_viReadSTB(ViSession vi, ViPUInt16 status);

  /*========================================================================*//**
   \brief         TLvisa equivalent to NI VISA (TM) viClear()
   \details       This operation sends a command to clear the device. The
                  behaviour of the device should be the same a sending the
                  string "*CLS\n" to it.\n
                  \n
                  This operation applies to the VISA INSTR session type.

   \param[in]     vi Unique logical identifier to a session.

   \return        Reports the status of a VISA operation.
                  A textual description of the returned Status Code may be obtained
                  by calling the \ref TL_viStatusDesc function.

*//*=========================================================================*/
  ViStatus _VI_FUNC TL_viClear(ViSession vi);

  /**@}*/ /* TLVISA_METHODS_BASIC_IO_x */

  /*========================================================================*//**
\defgroup   TLVISA_METHODS_INTF_SPECIFIC_x Interface specific operations
\ingroup    TLVISA_METHODS_x
\brief      Functions provided by TLVISA library for interface specific device communication,
            e.g. USB control transfers.
@{
*//*=========================================================================*/

  /*========================================================================*//**
   \brief         TLvisa equivalent to NI VISA (TM) viGpibControlREN()
   \details       Controls the state of the GPIB REN line, and optionally
                  places the device associated with the given session in
                  remote or local mode.\n\n
                  Obsolete, we do not care about GPIB sessions:\n
                  For GPIB INTFC sessions, only the modes dealing directly with
                  the REN line iteself are valid (in other words, the modes
                  dealing with device addressing are invalid).\n\n

                  For USB INSTR sessions, this operation and all of
                  the defined modes are valid.

   \param[in]     vi Unique logical identifier to a session.
   \param[in]     mode The mode must be one of the predefined identifiers,
                  see \ref TLVISA_CONTROL_REN_CODES_x and specifies whether
                  to assert or deassert the REN line.
                  This parameter also specifies whether to place the device
                  in local or remote mode.

   \return        Reports the status of a VISA operation.
                  A textual description of the returned Status Code may be obtained
                  by calling the \ref TL_viStatusDesc function.

*//*=========================================================================*/
  ViStatus _VI_FUNC TL_viGpibControlREN(ViSession vi, ViUInt16 mode);

  /*========================================================================*//**
   \brief         TLvisa equivalent to NI VISA (TM) viUsbControlIn()
   \details       Performs a USB control pipe transfer to the device. The
                  values of the data payload in the setup stage of the control
                  transfer are taken as parameters and include bmRequestType,
                  bRequest, wValue, wIndex, and wLength. An optional data buffer
                  is also sent if a data stage is required for this transfer.

   \param[in]     vi Unique logical identifier to a session.
   \param[in]     bmRequestType bmRequestType parameter of USB control transfer.
   \param[in]     bRequest bRequest parameter of USB control transfer.
   \param[in]     wValue wValue parameter of USB control transfer.
   \param[in]     wIndex wIndex parameter of USB control transfer.
   \param[in]     wLength Number of bytes to write.
   \param[in]     buf Data to send to the control pipe of the USB instrument.

   \return        Reports the status of a VISA operation.
                  A textual description of the returned Status Code may be obtained
                  by calling the \ref TL_viStatusDesc function.

*//*=========================================================================*/
  ViStatus _VI_FUNC TL_viUsbControlOut(ViSession vi,
                                       ViInt16 bmRequestType,
                                       ViInt16 bRequest,
                                       ViUInt16 wValue,
                                       ViUInt16 wIndex,
                                       ViUInt16 wLength,
                                       ViBuf buf);

  /*========================================================================*//**
   \brief         TLvisa equivalent to NI VISA (TM) viUsbControlIn()
   \details       Performs a USB control pipe transfer from the device. The
                  values of the data payload in the setup stage of the control
                  transfer are taken as parameters and include bmRequestType,
                  bRequest, wValue, wIndex, and wLength. An optional data buffer
                  is also read if a data stage is required for this transfer.

   \param[in]     vi Unique logical identifier to a session.
   \param[in]     bmRequestType bmRequestType parameter of USB control transfer.
   \param[in]     bRequest bRequest parameter of USB control transfer.
   \param[in]     wValue wValue parameter of USB control transfer.
   \param[in]     wIndex wIndex parameter of USB control transfer.
   \param[in]     wLength Number of bytes to write.
   \param[in]     buf Data to send to the control pipe of the USB instrument.
   \param[out]    retCnt Number of bytes actually transferred. If you do not
                  need to know the actual number of bytes transferred
                  (for example, if you need to know only whether this
                  operation succeeded), you may pass VI_NULL for this parameter.

   \return        Reports the status of a VISA operation.
                  A textual description of the returned Status Code may be obtained
                  by calling the \ref TL_viStatusDesc function.

*//*=========================================================================*/
  ViStatus _VI_FUNC TL_viUsbControlIn(ViSession vi,
                                      ViInt16 bmRequestType,
                                      ViInt16 bRequest,
                                      ViUInt16 wValue,
                                      ViUInt16 wIndex,
                                      ViUInt16 wLength,
                                      ViPBuf buf,
                                      ViPUInt16 retCnt);

  // test function only, do not use
  ViStatus TLvisa_TESTprintEvtQueue(ViSession vi);

  /**@}*/ /* TLVISA_METHODS_INTF_SPECIFIC_x */

  /**@}*/ /* TLVISA_METHODS_x */

  /*========================================================================*//**
\defgroup   TLVISA_ATTRIBUTES_x  Attributes of objects (e.g. devices)
\ingroup    TLVISA_MACROS_x
@{
*//*=========================================================================*/

#  if defined(__WIN64__)
#    define VI_ATTR_USER_DATA (VI_ATTR_USER_DATA_64)
#  else
#    define VI_ATTR_USER_DATA (VI_ATTR_USER_DATA_32)
#  endif

#  define VI_ATTR_RSRC_CLASS        (0xBFFF0001UL)
#  define VI_ATTR_RSRC_NAME         (0xBFFF0002UL)
#  define VI_ATTR_RSRC_IMPL_VERSION (0x3FFF0003UL)
#  define VI_ATTR_RSRC_LOCK_STATE   (0x3FFF0004UL)
#  define VI_ATTR_MAX_QUEUE_LENGTH  (0x3FFF0005UL)
#  define VI_ATTR_USER_DATA_32      (0x3FFF0007UL)
#  define VI_ATTR_USER_DATA_64      (0x3FFF000AUL)
#  define VI_ATTR_FDC_CHNL          (0x3FFF000DUL)
#  define VI_ATTR_FDC_MODE          (0x3FFF000FUL)
#  define VI_ATTR_FDC_GEN_SIGNAL_EN (0x3FFF0011UL)
#  define VI_ATTR_FDC_USE_PAIR      (0x3FFF0013UL)
#  define VI_ATTR_SEND_END_EN       (0x3FFF0016UL)
#  ifdef TLVISA_WITH_ASRL
#    define VI_ATTR_TERMCHAR (0x3FFF0018UL)
#  endif  //	TLVISA_WITH_ASRL
#  define VI_ATTR_TMO_VALUE      (0x3FFF001AUL)
#  define VI_ATTR_GPIB_READDR_EN (0x3FFF001BUL)
#  define VI_ATTR_IO_PROT        (0x3FFF001CUL)
#  define VI_ATTR_DMA_ALLOW_EN   (0x3FFF001EUL)

#  ifdef TLVISA_WITH_ASRL
#    define VI_ATTR_ASRL_BAUD       (0x3FFF0021UL)
#    define VI_ATTR_ASRL_DATA_BITS  (0x3FFF0022UL)
#    define VI_ATTR_ASRL_PARITY     (0x3FFF0023UL)
#    define VI_ATTR_ASRL_STOP_BITS  (0x3FFF0024UL)
#    define VI_ATTR_ASRL_FLOW_CNTRL (0x3FFF0025UL)
#  endif  //	TLVISA_WITH_ASRL

#  define VI_ATTR_RD_BUF_OPER_MODE (0x3FFF002AUL)
#  define VI_ATTR_RD_BUF_SIZE      (0x3FFF002BUL)
#  define VI_ATTR_WR_BUF_OPER_MODE (0x3FFF002DUL)
#  define VI_ATTR_WR_BUF_SIZE      (0x3FFF002EUL)
#  define VI_ATTR_SUPPRESS_END_EN  (0x3FFF0036UL)
#  ifdef TLVISA_WITH_ASRL
#    define VI_ATTR_TERMCHAR_EN (0x3FFF0038UL)
#  endif  //	TLVISA_WITH_ASRL
#  define VI_ATTR_DEST_ACCESS_PRIV (0x3FFF0039UL)
#  define VI_ATTR_DEST_BYTE_ORDER  (0x3FFF003AUL)
#  define VI_ATTR_SRC_ACCESS_PRIV  (0x3FFF003CUL)
#  define VI_ATTR_SRC_BYTE_ORDER   (0x3FFF003DUL)
#  define VI_ATTR_SRC_INCREMENT    (0x3FFF0040UL)
#  define VI_ATTR_DEST_INCREMENT   (0x3FFF0041UL)
#  define VI_ATTR_WIN_ACCESS_PRIV  (0x3FFF0045UL)
#  define VI_ATTR_WIN_BYTE_ORDER   (0x3FFF0047UL)
#  define VI_ATTR_CMDR_LA          (0x3FFF006BUL)
#  define VI_ATTR_VXI_DEV_CLASS    (0x3FFF006CUL)
#  define VI_ATTR_MANF_NAME        (0xBFFF0072UL)
#  define VI_ATTR_MODEL_NAME       (0xBFFF0077UL)
#  define VI_ATTR_WIN_BASE_ADDR_32 (0x3FFF0098UL)
#  define VI_ATTR_WIN_SIZE_32      (0x3FFF009AUL)
#  ifdef TLVISA_WITH_ASRL
#    define VI_ATTR_ASRL_AVAIL_NUM (0x3FFF00ACUL)
#  endif  //	TLVISA_WITH_ASRL
#  define VI_ATTR_MEM_BASE_32 (0x3FFF00ADUL)
#  ifdef TLVISA_WITH_ASRL
#    define VI_ATTR_ASRL_CTS_STATE    (0x3FFF00AEUL)
#    define VI_ATTR_ASRL_DCD_STATE    (0x3FFF00AFUL)
#    define VI_ATTR_ASRL_DISCARD_NULL (0x3FFF00B0UL)
#    define VI_ATTR_ASRL_DSR_STATE    (0x3FFF00B1UL)
#    define VI_ATTR_ASRL_DTR_STATE    (0x3FFF00B2UL)
#    define VI_ATTR_ASRL_END_IN       (0x3FFF00B3UL)
#    define VI_ATTR_ASRL_END_OUT      (0x3FFF00B4UL)
#    define VI_ATTR_ASRL_REPLACE_CHAR (0x3FFF00BEUL)
#    define VI_ATTR_ASRL_RI_STATE     (0x3FFF00BFUL)
#    define VI_ATTR_ASRL_RTS_STATE    (0x3FFF00C0UL)
#    define VI_ATTR_ASRL_XON_CHAR     (0x3FFF00C1UL)
#    define VI_ATTR_ASRL_XOFF_CHAR    (0x3FFF00C2UL)
#  endif  //	TLVISA_WITH_ASRL

#  define VI_ATTR_WIN_ACCESS (0x3FFF00C3UL)
#  define VI_ATTR_RM_SESSION (0x3FFF00C4UL)
//#define VI_ATTR_VXI_LA                        (0x3FFF00D5UL)
#  define VI_ATTR_MANF_ID     (0x3FFF00D9UL)
#  define VI_ATTR_MEM_SIZE_32 (0x3FFF00DDUL)
#  define VI_ATTR_MEM_SPACE   (0x3FFF00DEUL)
#  define VI_ATTR_MODEL_CODE  (0x3FFF00DFUL)
// #define VI_ATTR_SLOT                          (0x3FFF00E8UL)
#  define VI_ATTR_INTF_INST_NAME    (0xBFFF00E9UL)
#  define VI_ATTR_IMMEDIATE_SERV    (0x3FFF0100UL)
#  define VI_ATTR_INTF_PARENT_NUM   (0x3FFF0101UL)
#  define VI_ATTR_RSRC_SPEC_VERSION (0x3FFF0170UL)
#  define VI_ATTR_INTF_TYPE         (0x3FFF0171UL)
// #define VI_ATTR_GPIB_PRIMARY_ADDR             (0x3FFF0172UL)
// #define VI_ATTR_GPIB_SECONDARY_ADDR           (0x3FFF0173UL)
#  define VI_ATTR_RSRC_MANF_NAME (0xBFFF0174UL)
#  define VI_ATTR_RSRC_MANF_ID   (0x3FFF0175UL)
#  define VI_ATTR_INTF_NUM       (0x3FFF0176UL)
#  define VI_ATTR_TRIG_ID        (0x3FFF0177UL)
// #define VI_ATTR_GPIB_REN_STATE                (0x3FFF0181UL)
// #define VI_ATTR_GPIB_UNADDR_EN                (0x3FFF0184UL)
#  define VI_ATTR_DEV_STATUS_BYTE  (0x3FFF0189UL)
#  define VI_ATTR_FILE_APPEND_EN   (0x3FFF0192UL)
#  define VI_ATTR_VXI_TRIG_SUPPORT (0x3FFF0194UL)

#  define VI_ATTR_4882_COMPLIANT    (0x3FFF019FUL)
#  define VI_ATTR_USB_SERIAL_NUM    (0xBFFF01A0UL)
#  define VI_ATTR_USB_INTFC_NUM     (0x3FFF01A1UL)
#  define VI_ATTR_USB_PROTOCOL      (0x3FFF01A7UL)
#  define VI_ATTR_USB_MAX_INTR_SIZE (0x3FFF01AFUL)

#  ifdef TLVISA_WITH_ASRL
#    define VI_ATTR_ASRL_CONNECTED      (0x3FFF01BBUL)
#    define VI_ATTR_ASRL_BREAK_STATE    (0x3FFF01BCUL)
#    define VI_ATTR_ASRL_BREAK_LEN      (0x3FFF01BDUL)
#    define VI_ATTR_ASRL_ALLOW_TRANSMIT (0x3FFF01BEUL)
#    define VI_ATTR_ASRL_WIRE_MODE      (0x3FFF01BFUL)
#  endif  //	TLVISA_WITH_ASRL

#  define VI_ATTR_JOB_ID          (0x3FFF4006UL)
#  define VI_ATTR_EVENT_TYPE      (0x3FFF4010UL)
#  define VI_ATTR_SIGP_STATUS_ID  (0x3FFF4011UL)
#  define VI_ATTR_RECV_TRIG_ID    (0x3FFF4012UL)
#  define VI_ATTR_INTR_STATUS_ID  (0x3FFF4023UL)
#  define VI_ATTR_STATUS          (0x3FFF4025UL)
#  define VI_ATTR_RET_COUNT_32    (0x3FFF4026UL)
#  define VI_ATTR_BUFFER          (0x3FFF4027UL)
#  define VI_ATTR_RECV_INTR_LEVEL (0x3FFF4041UL)
#  define VI_ATTR_OPER_NAME       (0xBFFF4042UL)

#  define VI_ATTR_USB_RECV_INTR_SIZE (0x3FFF41B0UL)
#  define VI_ATTR_USB_RECV_INTR_DATA (0xBFFF41B1UL)
#  define VI_ATTR_PXI_RECV_INTR_SEQ  (0x3FFF4240UL)
#  define VI_ATTR_PXI_RECV_INTR_DATA (0x3FFF4241UL)

  /**@}*/ /* TLVISA_ATTRIBUTES_x */

  /*========================================================================*//**
\defgroup   TLVISA_PRIVATE_ATTRIBUTES_x  TLVISA specific Attributes of objects (e.g. devices), not available in other VISA implementations like e.g. (TM) NI-VISA
\ingroup    TLVISA_ATTRIBUTES_x
@{
*//*=========================================================================*/

#  define TL_ATTR_DEVICE_NAME       (0x3FFE0001UL)  ///< the device name as listet in USB device descriptor
#  define TL_ATTR_USB_INTR_OUT_PIPE (0x3FFE0002UL)  ///< the index of the Interrupt Out endpoint
#  define TL_ATTR_CLASS \
    (0x3FFE0003UL)  ///< reports well known classes like TMC, HID, MSC (as defined here: http://www.usb.org/developers/defined_class)
#  define TL_ATTR_CLASS_NAME \
    (0x3FFE0004UL)  ///< prints well known classes like TMC, HID, MSC (as defined here: http://www.usb.org/developers/defined_class)
#  define TL_ATTR_INTERFACE_NAME  (0x3FFE0005UL)  ///< the interface name as listed in USB interface descriptor
#  define TL_ATTR_BUILD_DATE_TIME (0x3FFE0006UL)  ///< TLVISA build date and time as a string

  /**@}*/ /* TLVISA_PRIVATE_ATTRIBUTES_x */

  /*========================================================================*//**
\defgroup   TLVISA_USB_CLASSES_x  USB classes as listed in
\ingroup    TLVISA_MACROS_x
@{
*//*=========================================================================*/

#  define TL_CLASS_TMC         (0x00FE0300)  ///< USBTMC (Test and Measurement Class)
#  define TL_CLASS_TMC_USB488  (0x00FE0301)  ///< USBTMC (Test and Measurement Class) conforming to USB488 subclass
#  define TL_CLASS_DFU_RUNTIME (0x00FE0101)  ///< DFU (Device Firmware Update Class) in Runtime Mode
#  define TL_CLASS_DFU_DFU     (3)           ///< DFU (Device Firmware Update Class) in DFU Mode
#  define TL_CLASS_HID         (0x00030000)  ///< HID (Human Interface Device Class)
#  define TL_CLASS_UNKNOWN     (-1)          ///< could not detect the USB Class

  /**@}*/ /* TLVISA_PRIVATE_ATTRIBUTES_x */

  /*- Event Types -------------------------------------------------------------*/

#  define VI_EVENT_USB_INTR     (0x3FFF2037UL)
#  define VI_ALL_ENABLED_EVENTS (0x3FFF7FFFUL)

  /*========================================================================*//**
\defgroup   TLVISA_ERROR_CODES_x  Error codes for TLVISA functions
\ingroup    TLVISA_MACROS_x
@{
*//*=========================================================================*/

#  define VI_SUCCESS_EVENT_EN         (0x3FFF0002L) /* 3FFF0002,  1073676290 */
#  define VI_SUCCESS_EVENT_DIS        (0x3FFF0003L) /* 3FFF0003,  1073676291 */
#  define VI_SUCCESS_QUEUE_EMPTY      (0x3FFF0004L) /* 3FFF0004,  1073676292 */
#  define VI_SUCCESS_TERM_CHAR        (0x3FFF0005L) /* 3FFF0005,  1073676293 */
#  define VI_SUCCESS_MAX_CNT          (0x3FFF0006L) /* 3FFF0006,  1073676294 */
#  define VI_SUCCESS_DEV_NPRESENT     (0x3FFF007DL) /* 3FFF007D,  1073676413 */
#  define VI_SUCCESS_TRIG_MAPPED      (0x3FFF007EL) /* 3FFF007E,  1073676414 */
#  define VI_SUCCESS_QUEUE_NEMPTY     (0x3FFF0080L) /* 3FFF0080,  1073676416 */
#  define VI_SUCCESS_NCHAIN           (0x3FFF0098L) /* 3FFF0098,  1073676440 */
#  define VI_SUCCESS_NESTED_SHARED    (0x3FFF0099L) /* 3FFF0099,  1073676441 */
#  define VI_SUCCESS_NESTED_EXCLUSIVE (0x3FFF009AL) /* 3FFF009A,  1073676442 */
#  define VI_SUCCESS_SYNC             (0x3FFF009BL) /* 3FFF009B,  1073676443 */

#  define VI_WARN_QUEUE_OVERFLOW  (0x3FFF000CL) /* 3FFF000C,  1073676300 */
#  define VI_WARN_CONFIG_NLOADED  (0x3FFF0077L) /* 3FFF0077,  1073676407 */
#  define VI_WARN_NULL_OBJECT     (0x3FFF0082L) /* 3FFF0082,  1073676418 */
#  define VI_WARN_NSUP_ATTR_STATE (0x3FFF0084L) /* 3FFF0084,  1073676420 */
#  define VI_WARN_UNKNOWN_STATUS  (0x3FFF0085L) /* 3FFF0085,  1073676421 */
#  define VI_WARN_NSUP_BUF        (0x3FFF0088L) /* 3FFF0088,  1073676424 */
#  define VI_WARN_EXT_FUNC_NIMPL  (0x3FFF00A9L) /* 3FFF00A9,  1073676457 */

#  define VI_ERROR_SYSTEM_ERROR     (_VI_ERROR + 0x3FFF0000L) /* BFFF0000, -1073807360 */
#  define VI_ERROR_INV_OBJECT       (_VI_ERROR + 0x3FFF000EL) /* BFFF000E, -1073807346 */
#  define VI_ERROR_RSRC_LOCKED      (_VI_ERROR + 0x3FFF000FL) /* BFFF000F, -1073807345 */
#  define VI_ERROR_INV_EXPR         (_VI_ERROR + 0x3FFF0010L) /* BFFF0010, -1073807344 */
#  define VI_ERROR_RSRC_NFOUND      (_VI_ERROR + 0x3FFF0011L) /* BFFF0011, -1073807343 */
#  define VI_ERROR_INV_RSRC_NAME    (_VI_ERROR + 0x3FFF0012L) /* BFFF0012, -1073807342 */
#  define VI_ERROR_INV_ACC_MODE     (_VI_ERROR + 0x3FFF0013L) /* BFFF0013, -1073807341 */
#  define VI_ERROR_TMO              (_VI_ERROR + 0x3FFF0015L) /* BFFF0015, -1073807339 */
#  define VI_ERROR_CLOSING_FAILED   (_VI_ERROR + 0x3FFF0016L) /* BFFF0016, -1073807338 */
#  define VI_ERROR_INV_DEGREE       (_VI_ERROR + 0x3FFF001BL) /* BFFF001B, -1073807333 */
#  define VI_ERROR_INV_JOB_ID       (_VI_ERROR + 0x3FFF001CL) /* BFFF001C, -1073807332 */
#  define VI_ERROR_NSUP_ATTR        (_VI_ERROR + 0x3FFF001DL) /* BFFF001D, -1073807331 */
#  define VI_ERROR_NSUP_ATTR_STATE  (_VI_ERROR + 0x3FFF001EL) /* BFFF001E, -1073807330 */
#  define VI_ERROR_ATTR_READONLY    (_VI_ERROR + 0x3FFF001FL) /* BFFF001F, -1073807329 */
#  define VI_ERROR_INV_LOCK_TYPE    (_VI_ERROR + 0x3FFF0020L) /* BFFF0020, -1073807328 */
#  define VI_ERROR_INV_ACCESS_KEY   (_VI_ERROR + 0x3FFF0021L) /* BFFF0021, -1073807327 */
#  define VI_ERROR_INV_EVENT        (_VI_ERROR + 0x3FFF0026L) /* BFFF0026, -1073807322 */
#  define VI_ERROR_INV_MECH         (_VI_ERROR + 0x3FFF0027L) /* BFFF0027, -1073807321 */
#  define VI_ERROR_HNDLR_NINSTALLED (_VI_ERROR + 0x3FFF0028L) /* BFFF0028, -1073807320 */
#  define VI_ERROR_INV_HNDLR_REF    (_VI_ERROR + 0x3FFF0029L) /* BFFF0029, -1073807319 */
#  define VI_ERROR_INV_CONTEXT      (_VI_ERROR + 0x3FFF002AL) /* BFFF002A, -1073807318 */
#  define VI_ERROR_QUEUE_OVERFLOW   (_VI_ERROR + 0x3FFF002DL) /* BFFF002D, -1073807315 */
#  define VI_ERROR_NENABLED         (_VI_ERROR + 0x3FFF002FL) /* BFFF002F, -1073807313 */
#  define VI_ERROR_ABORT            (_VI_ERROR + 0x3FFF0030L) /* BFFF0030, -1073807312 */
#  define VI_ERROR_RAW_WR_PROT_VIOL (_VI_ERROR + 0x3FFF0034L) /* BFFF0034, -1073807308 */
#  define VI_ERROR_RAW_RD_PROT_VIOL (_VI_ERROR + 0x3FFF0035L) /* BFFF0035, -1073807307 */
#  define VI_ERROR_OUTP_PROT_VIOL   (_VI_ERROR + 0x3FFF0036L) /* BFFF0036, -1073807306 */
#  define VI_ERROR_INP_PROT_VIOL    (_VI_ERROR + 0x3FFF0037L) /* BFFF0037, -1073807305 */
#  define VI_ERROR_BERR             (_VI_ERROR + 0x3FFF0038L) /* BFFF0038, -1073807304 */
#  define VI_ERROR_IN_PROGRESS      (_VI_ERROR + 0x3FFF0039L) /* BFFF0039, -1073807303 */
#  define VI_ERROR_INV_SETUP        (_VI_ERROR + 0x3FFF003AL) /* BFFF003A, -1073807302 */
#  define VI_ERROR_QUEUE_ERROR      (_VI_ERROR + 0x3FFF003BL) /* BFFF003B, -1073807301 */
#  define VI_ERROR_ALLOC            (_VI_ERROR + 0x3FFF003CL) /* BFFF003C, -1073807300 */
#  define VI_ERROR_INV_MASK         (_VI_ERROR + 0x3FFF003DL) /* BFFF003D, -1073807299 */
#  define VI_ERROR_IO               (_VI_ERROR + 0x3FFF003EL) /* BFFF003E, -1073807298 */
#  define VI_ERROR_INV_FMT          (_VI_ERROR + 0x3FFF003FL) /* BFFF003F, -1073807297 */
#  define VI_ERROR_NSUP_FMT         (_VI_ERROR + 0x3FFF0041L) /* BFFF0041, -1073807295 */
#  define VI_ERROR_LINE_IN_USE      (_VI_ERROR + 0x3FFF0042L) /* BFFF0042, -1073807294 */
#  define VI_ERROR_LINE_NRESERVED   (_VI_ERROR + 0x3FFF0043L) /* BFFF0043, -1073807293 */
#  define VI_ERROR_NSUP_MODE        (_VI_ERROR + 0x3FFF0046L) /* BFFF0046, -1073807290 */
#  define VI_ERROR_SRQ_NOCCURRED    (_VI_ERROR + 0x3FFF004AL) /* BFFF004A, -1073807286 */
#  define VI_ERROR_INV_SPACE        (_VI_ERROR + 0x3FFF004EL) /* BFFF004E, -1073807282 */
#  define VI_ERROR_INV_OFFSET       (_VI_ERROR + 0x3FFF0051L) /* BFFF0051, -1073807279 */
#  define VI_ERROR_INV_WIDTH        (_VI_ERROR + 0x3FFF0052L) /* BFFF0052, -1073807278 */
#  define VI_ERROR_NSUP_OFFSET      (_VI_ERROR + 0x3FFF0054L) /* BFFF0054, -1073807276 */
#  define VI_ERROR_NSUP_VAR_WIDTH   (_VI_ERROR + 0x3FFF0055L) /* BFFF0055, -1073807275 */
#  define VI_ERROR_WINDOW_NMAPPED   (_VI_ERROR + 0x3FFF0057L) /* BFFF0057, -1073807273 */
#  define VI_ERROR_RESP_PENDING     (_VI_ERROR + 0x3FFF0059L) /* BFFF0059, -1073807271 */
#  define VI_ERROR_NLISTENERS       (_VI_ERROR + 0x3FFF005FL) /* BFFF005F, -1073807265 */
#  define VI_ERROR_NCIC             (_VI_ERROR + 0x3FFF0060L) /* BFFF0060, -1073807264 */
#  define VI_ERROR_NSYS_CNTLR       (_VI_ERROR + 0x3FFF0061L) /* BFFF0061, -1073807263 */
#  define VI_ERROR_NSUP_OPER        (_VI_ERROR + 0x3FFF0067L) /* BFFF0067, -1073807257 */
#  define VI_ERROR_INTR_PENDING     (_VI_ERROR + 0x3FFF0068L) /* BFFF0068, -1073807256 */
#  ifdef TLVISA_WITH_ASRL
#    define VI_ERROR_ASRL_PARITY  (_VI_ERROR + 0x3FFF006AL)    /* BFFF006A, -1073807254 */
#    define VI_ERROR_ASRL_FRAMING (_VI_ERROR + 0x3FFF006BL)    /* BFFF006B, -1073807253 */
#    define VI_ERROR_ASRL_OVERRUN (_VI_ERROR + 0x3FFF006CL)    /* BFFF006C, -1073807252 */
#  endif                                                       //	TLVISA_WITH_ASRL
#  define VI_ERROR_TRIG_NMAPPED      (_VI_ERROR + 0x3FFF006EL) /* BFFF006E, -1073807250 */
#  define VI_ERROR_NSUP_ALIGN_OFFSET (_VI_ERROR + 0x3FFF0070L) /* BFFF0070, -1073807248 */
#  define VI_ERROR_USER_BUF          (_VI_ERROR + 0x3FFF0071L) /* BFFF0071, -1073807247 */
#  define VI_ERROR_RSRC_BUSY         (_VI_ERROR + 0x3FFF0072L) /* BFFF0072, -1073807246 */
#  define VI_ERROR_NSUP_WIDTH        (_VI_ERROR + 0x3FFF0076L) /* BFFF0076, -1073807242 */
#  define VI_ERROR_INV_PARAMETER     (_VI_ERROR + 0x3FFF0078L) /* BFFF0078, -1073807240 */
#  define VI_ERROR_INV_PROT          (_VI_ERROR + 0x3FFF0079L) /* BFFF0079, -1073807239 */
#  define VI_ERROR_INV_SIZE          (_VI_ERROR + 0x3FFF007BL) /* BFFF007B, -1073807237 */
#  define VI_ERROR_WINDOW_MAPPED     (_VI_ERROR + 0x3FFF0080L) /* BFFF0080, -1073807232 */
#  define VI_ERROR_NIMPL_OPER        (_VI_ERROR + 0x3FFF0081L) /* BFFF0081, -1073807231 */
#  define VI_ERROR_INV_LENGTH        (_VI_ERROR + 0x3FFF0083L) /* BFFF0083, -1073807229 */
#  define VI_ERROR_INV_MODE          (_VI_ERROR + 0x3FFF0091L) /* BFFF0091, -1073807215 */
#  define VI_ERROR_SESN_NLOCKED      (_VI_ERROR + 0x3FFF009CL) /* BFFF009C, -1073807204 */
#  define VI_ERROR_MEM_NSHARED       (_VI_ERROR + 0x3FFF009DL) /* BFFF009D, -1073807203 */
#  define VI_ERROR_LIBRARY_NFOUND    (_VI_ERROR + 0x3FFF009EL) /* BFFF009E, -1073807202 */
#  define VI_ERROR_NSUP_INTR         (_VI_ERROR + 0x3FFF009FL) /* BFFF009F, -1073807201 */
#  define VI_ERROR_INV_LINE          (_VI_ERROR + 0x3FFF00A0L) /* BFFF00A0, -1073807200 */
#  define VI_ERROR_FILE_ACCESS       (_VI_ERROR + 0x3FFF00A1L) /* BFFF00A1, -1073807199 */
#  define VI_ERROR_FILE_IO           (_VI_ERROR + 0x3FFF00A2L) /* BFFF00A2, -1073807198 */
#  define VI_ERROR_NSUP_LINE         (_VI_ERROR + 0x3FFF00A3L) /* BFFF00A3, -1073807197 */
#  define VI_ERROR_NSUP_MECH         (_VI_ERROR + 0x3FFF00A4L) /* BFFF00A4, -1073807196 */
#  define VI_ERROR_INTF_NUM_NCONFIG  (_VI_ERROR + 0x3FFF00A5L) /* BFFF00A5, -1073807195 */
#  define VI_ERROR_CONN_LOST         (_VI_ERROR + 0x3FFF00A6L) /* BFFF00A6, -1073807194 */
#  define VI_ERROR_MACHINE_NAVAIL    (_VI_ERROR + 0x3FFF00A7L) /* BFFF00A7, -1073807193 */
#  define VI_ERROR_NPERMISSION       (_VI_ERROR + 0x3FFF00A8L) /* BFFF00A8, -1073807192 */

  /**@}*/ /* TLVISA_ERROR_CODES_x */

  /*- Other VISA Definitions --------------------------------------------------*/

#  define VI_DEFAULT_END_CODE (0x0A)  //	<LF> as default end character

#  define VI_VERSION_MAJOR(ver)    ((((ViVersion)ver) & 0xFFF00000UL) >> 20)
#  define VI_VERSION_MINOR(ver)    ((((ViVersion)ver) & 0x000FFF00UL) >> 8)
#  define VI_VERSION_SUBMINOR(ver) ((((ViVersion)ver) & 0x000000FFUL))

#  define VI_FIND_BUFLEN (256)

// #define VI_INTF_GPIB                (1)
// #define VI_INTF_VXI                 (2)
// #define VI_INTF_GPIB_VXI            (3)
#  define VI_INTF_ASRL (4)
// #define VI_INTF_PXI                 (5)
// #define VI_INTF_TCPIP               (6)
#  define VI_INTF_USB (7)

#  define VI_PROT_NORMAL        (1)
#  define VI_PROT_FDC           (2)
#  define VI_PROT_HS488         (3)
#  define VI_PROT_4882_STRS     (4)
#  define VI_PROT_USBTMC_VENDOR (5)

#  define VI_FDC_NORMAL (1)
#  define VI_FDC_STREAM (2)

#  define VI_QUEUE         (1)
#  define VI_HNDLR         (2)
#  define VI_SUSPEND_HNDLR (4)
#  define VI_ALL_MECH      (0xFFFF)

#  define VI_ANY_HNDLR (0)

#  define VI_TRIG_PROT_DEFAULT   (0)
#  define VI_TRIG_PROT_ON        (1)
#  define VI_TRIG_PROT_OFF       (2)
#  define VI_TRIG_PROT_SYNC      (5)
#  define VI_TRIG_PROT_RESERVE   (6)
#  define VI_TRIG_PROT_UNRESERVE (7)

#  define VI_READ_BUF           (1)
#  define VI_WRITE_BUF          (2)
#  define VI_READ_BUF_DISCARD   (4)
#  define VI_WRITE_BUF_DISCARD  (8)
#  define VI_IO_IN_BUF          (16)
#  define VI_IO_OUT_BUF         (32)
#  define VI_IO_IN_BUF_DISCARD  (64)
#  define VI_IO_OUT_BUF_DISCARD (128)

#  define VI_FLUSH_ON_ACCESS (1)
#  define VI_FLUSH_WHEN_FULL (2)
#  define VI_FLUSH_DISABLE   (3)

#  define VI_NMAPPED              (1)
#  define VI_USE_OPERS            (2)
#  define VI_DEREF_ADDR           (3)
#  define VI_DEREF_ADDR_BYTE_SWAP (4)

#  define VI_TMO_IMMEDIATE (0L)
#  define VI_TMO_INFINITE  (0xFFFFFFFFUL)

#  define VI_NO_LOCK        (0)
#  define VI_EXCLUSIVE_LOCK (1)
#  define VI_SHARED_LOCK    (2)
#  define VI_LOAD_CONFIG    (4)

#  define VI_NO_SEC_ADDR (0xFFFF)

#  ifdef TLVISA_WITH_ASRL
#    define VI_ASRL_PAR_NONE  (0)
#    define VI_ASRL_PAR_ODD   (1)
#    define VI_ASRL_PAR_EVEN  (2)
#    define VI_ASRL_PAR_MARK  (3)
#    define VI_ASRL_PAR_SPACE (4)

#    define VI_ASRL_STOP_ONE  (10)
#    define VI_ASRL_STOP_ONE5 (15)
#    define VI_ASRL_STOP_TWO  (20)

#    define VI_ASRL_FLOW_NONE     (0)
#    define VI_ASRL_FLOW_XON_XOFF (1)
#    define VI_ASRL_FLOW_RTS_CTS  (2)
#    define VI_ASRL_FLOW_DTR_DSR  (4)

#    define FLOW_DTRDSR_RTSCTS (VI_ASRL_FLOW_DTR_DSR | VI_ASRL_FLOW_RTS_CTS)

#    define VI_ASRL_END_NONE     (0)
#    define VI_ASRL_END_LAST_BIT (1)
#    define VI_ASRL_END_TERMCHAR (2)
#    define VI_ASRL_END_BREAK    (3)

#  endif  //	TLVISA_WITH_ASRL

#  define VI_STATE_ASSERTED   (1)
#  define VI_STATE_UNASSERTED (0)
#  define VI_STATE_UNKNOWN    (-1)

#  define VI_BIG_ENDIAN    (0)
#  define VI_LITTLE_ENDIAN (1)

  /*- Backward Compatibility Macros -------------------------------------------*/

#  define VI_ERROR_INV_SESSION    (VI_ERROR_INV_OBJECT)
#  define VI_INFINITE             (VI_TMO_INFINITE)
#  define VI_NORMAL               (VI_PROT_NORMAL)
#  define VI_FDC                  (VI_PROT_FDC)
#  define VI_HS488                (VI_PROT_HS488)
#  define VI_ASRL488              (VI_PROT_4882_STRS)
#  define VI_ASRL_IN_BUF          (VI_IO_IN_BUF)
#  define VI_ASRL_OUT_BUF         (VI_IO_OUT_BUF)
#  define VI_ASRL_IN_BUF_DISCARD  (VI_IO_IN_BUF_DISCARD)
#  define VI_ASRL_OUT_BUF_DISCARD (VI_IO_OUT_BUF_DISCARD)

/* the following macros ared defined in NI-VISA only when NIVISA_USB is defined before including visa.h */

/*========================================================================*//**
\ingroup    TLVISA_ATTRIBUTES_x
@{
*//*=========================================================================*/
#  define VI_ATTR_USB_BULK_OUT_PIPE   (0x3FFF01A2UL)
#  define VI_ATTR_USB_BULK_IN_PIPE    (0x3FFF01A3UL)
#  define VI_ATTR_USB_INTR_IN_PIPE    (0x3FFF01A4UL)
#  define VI_ATTR_USB_CLASS           (0x3FFF01A5UL)
#  define VI_ATTR_USB_SUBCLASS        (0x3FFF01A6UL)
#  define VI_ATTR_USB_ALT_SETTING     (0x3FFF01A8UL)
#  define VI_ATTR_USB_END_IN          (0x3FFF01A9UL)
#  define VI_ATTR_USB_NUM_INTFCS      (0x3FFF01AAUL)
#  define VI_ATTR_USB_NUM_PIPES       (0x3FFF01ABUL)
#  define VI_ATTR_USB_BULK_OUT_STATUS (0x3FFF01ACUL)
#  define VI_ATTR_USB_BULK_IN_STATUS  (0x3FFF01ADUL)
#  define VI_ATTR_USB_INTR_IN_STATUS  (0x3FFF01AEUL)
#  define VI_ATTR_USB_CTRL_PIPE       (0x3FFF01B0UL)
  /**@}*/ /* TLVISA_ATTRIBUTES_x */

#  define VI_USB_PIPE_STATE_UNKNOWN (-1)
#  define VI_USB_PIPE_READY         (0)
#  define VI_USB_PIPE_STALLED       (1)

#  define VI_USB_END_NONE           (0)
#  define VI_USB_END_SHORT          (4)
#  define VI_USB_END_SHORT_OR_COUNT (5)

#  if defined(__cplusplus) || defined(__cplusplus__)
}
#  endif

#endif  // __TLVISA_HEADER__

/*- The End -----------------------------------------------------------------*/
