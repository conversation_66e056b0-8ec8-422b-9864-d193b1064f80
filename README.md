﻿# optical_power_meter_driver光功率计功能类库

本工程用于需要使用光功率计进行测量项目中，目前只有**Thorlabs**的光功率计

## 1 如何添加到工程中

### 1.1 使用**git submodule**

- 在需要导入的工程目录下, 具体方法如下：

    ```bash
    git <NAME_EMAIL>:system_codebase/common_driver/optical_power_meter_driver.git src/optical_power_meter_driver
    cd src/optical_power_meter_driver
    git checkout <branch> # 切换到对应分支
    git submodule init
    git submodule update
    ```

### 1.2 在**CMakeLists.txt**

- **add_executable**之前加入：

    ```cmake
    # =========================
    # add power meter
    # =========================
    # NOTE: BUILD_POWER_METER_TEST为optical_power_meter_driver内部test，这里屏蔽避免当前工程使用test冲突
    set(BUILD_POWER_METER_TEST
        OFF
        CACHE BOOL "disable optical_power_meter_driver test build")
    # NOTE: submodule optical_power_meter_driver所在位置
    add_subdirectory(lib/optical_power_meter_driver)
    ```

- **add_executable**之后加入：

    ```cmake
    # NOTE: must after add_executable.
    target_link_libraries(${PROJECT_NAME} PRIVATE optical_power_meter_driver)
    ```

- 然后**cmake**和**make**即可

## 2 使用方法

- 具体使用方法请参考**thorlabs_optical_power_meter.h**中对各个函数的注释, 支持一托多

- 对于功率档位采用的是手动配置方式，**setPowerRange**进行设置

- 硬件给的方案：需要测量多大的功率(mW)，就手动配置**在此功率附近且大于该测量功率档位的值**进行测量

    ```
    e.g. when measure value about 60mW, set _power_range = 0.08(80mW) before measurePower
    ```

- 其他详细用法，如visa，参见文档 [PM100D-Manual.pdf](doc/PM100D-Manual.pdf)

- ubuntu下，安装软件时`./install.sh`， 将**Thorlabs.rules** **/etc/udev/rules.d/**

## 3 Q&A

### Q1 在新部署的ubuntu系统电脑无法识别光功率计问题
- 确保测试软件安装时，安装脚本有将**Thorlabs.rules** 拷贝到 **/etc/udev/rules.d/**

    ```
    sudo cp Thorlabs.rules /etc/udev/rules.d/
    ```

- 在新ubuntu系统上第一次安装时，`./install.sh`需加入如下脚本，原因猜想为光功率支持`usbtmc协议`，默认走的`usbtmc协议`，
   但目前使用库调用光功率api时不走`visa`，将`usbtmc`加入blacklist

    ```
    if [ `grep -c "blacklist usbtmc" /etc/modprobe.d/blacklist.conf` -eq '0' ];
    then
        echo "blacklist not set, set blacklist usbtmc"
        echo "-------please restart computer before run program-------"
        sudo sed -i '$a\blacklist usbtmc' /etc/modprobe.d/blacklist.conf
    else
        echo "blacklist already set"
    fi
    ```

## 4 待优化

- TODO: Thorlabs.rules添加打包
- TODO: 支持windows

**注意：请不要将文件单独拷贝到你的工程，请使用submodule的形式进行添加，否则你的修改将没有任何的版本信息，容易产生不必要的问题**